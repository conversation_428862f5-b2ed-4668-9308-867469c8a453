name: Distribute Build Artifacts (Adhoc & Beta)

on:
  schedule:
    - cron: '0 2 * * 1-6' # Nightly builds, Monday to Saturday

  pull_request:
    paths:
      - '.github/workflows/distribute-artifacts.yml'

  workflow_dispatch:
    inputs:
      config:
        description: 'Select configuration to build'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - production
      build_type:
        description: 'Select build type'
        required: true
        default: 'adhoc'
        type: choice
        options:
          - adhoc
          - beta

jobs:
  ios_build:
    name: iOS Build & Upload ipa
    runs-on: [ self-hosted, macOS ]
    environment: ${{ github.event.inputs.config || 'preview' }}
    env:
      FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
      MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
      MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
      MATCH_GIT_URL: ${{ vars.MATCH_GIT_URL }}
      APP_FLAVOR: ${{ vars.APP_FLAVOR }}
      APP_SCHEMA: ${{ vars.APP_SCHEMA }}
      APP_IDENTIFIER: ${{ vars.APP_IDENTIFIER }}

    steps:
      - uses: actions/checkout@v4

      - uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Install dependencies
        working-directory: ios
        run: bundle install

      - name: Run Fastlane
        working-directory: ios
        run: |
          bundle exec fastlane ios ${{ github.event.inputs.build_type || 'adhoc' }}

      - name: Upload IPA to Gitea Registry
        uses: Eltako/app-shared-actions/.github/actions/upload-release@release/0.1
        if: github.event.inputs.build_type == 'adhoc' || github.event_name == 'schedule'
        with:
          artifact-path: build/ios/ipa/
          artifact-name: app-${{ github.event.inputs.config || 'preview' }}-${{ github.event.inputs.build_type || 'adhoc' }}-run${{ github.run_number }}
          build-type: ${{ github.event.inputs.build_type || 'adhoc' }}
          changelog-file: CHANGELOG.md
          config: ${{ github.event.inputs.config || 'preview' }}
          registry-url: ${{ secrets.URL_CIGERAET_GITEA }}
          registry-user: ${{ secrets.USER_CIGERAET_GITEA_PACKAGES }}
          registry-token: ${{ secrets.TOKEN_RELEASE_CIGERAET_GITEA }}
          registry-repo: EltakoConnect
          registry-owner: ${{ secrets.USER_CIGERAET_GITEA_PACKAGES }}

  android_build:
    name: Android Build & Upload APK
    runs-on: [ self-hosted, macOS ]
    environment: ${{ github.event.inputs.config || 'preview' }}
    env:
      APP_FLAVOR: ${{ vars.APP_FLAVOR }}
      APP_SCHEMA: ${{ vars.APP_SCHEMA }}
      APP_IDENTIFIER_ANDROID: ${{ vars.APP_IDENTIFIER_ANDROID }}
      ANDROID_KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
      ANDROID_KEY_ALIAS: ${{ secrets.ANDROID_KEY_ALIAS }}
      ANDROID_KEYSTORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD }}
      ANDROID_KEYSTORE_B64: ${{ secrets.ANDROID_KEYSTORE_B64 }}

    steps:
      - uses: actions/checkout@v4

      - uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Set up service account JSON
        run: echo "${{ secrets.ANDROID_SERVICE_ACCOUNT_JSON }}" > /tmp/service_account.json

      - name: Decode and save keystore certificate and properties file
        working-directory: android
        run: |
          echo "${{ secrets.ANDROID_KEYSTORE_B64 }}" | base64 --decode > ./app/EltakoConnect${{ vars.APP_SCHEMA }}_Android_key
          echo "Keystore certificate saved to ./app/android/EltakoConnect${{ vars.APP_SCHEMA }}_Android_key"

          echo "storePassword=${{ secrets.ANDROID_KEYSTORE_PASSWORD }}" > "keystore-${{ vars.APP_FLAVOR }}.properties"
          echo "keyPassword=${{ secrets.ANDROID_KEY_PASSWORD }}" >> "keystore-${{ vars.APP_FLAVOR }}.properties"
          echo "keyAlias=${{ secrets.ANDROID_KEY_ALIAS }}" >> "keystore-${{ vars.APP_FLAVOR }}.properties"
          echo "storeFile=EltakoConnect${{ vars.APP_SCHEMA }}_Android_key" >> "keystore-${{ vars.APP_FLAVOR }}.properties"

      - name: Install dependencies
        working-directory: android
        run: bundle install

      - name: Run Fastlane for Android
        working-directory: android
        env:
          ANDROID_SERVICE_ACCOUNT_KEY_PATH: /tmp/service_account.json
        run: |
          source ~/.zshrc
          bundle exec fastlane android adhoc

      - name: Cleanup files
        if: always()
        run: |
          rm -f /tmp/service_account.json
          rm -f android/EltakoConnect${{ vars.APP_SCHEMA }}_Android_key
          rm -f android/app/EltakoConnect${{ vars.APP_SCHEMA }}_Android_key
          rm -f android/keystore-${{ vars.APP_FLAVOR }}.properties

      - name: Upload APK to Gitea Registry
        uses: Eltako/app-shared-actions/.github/actions/upload-release@release/0.1
        with:
          artifact-path: build/app/outputs/flutter-apk
          artifact-name: app-${{ github.event.inputs.config || 'preview' }}-${{ github.event.inputs.build_type || 'adhoc' }}-run${{ github.run_number }}
          build-type: ${{ github.event.inputs.build_type || 'adhoc' }}
          changelog-file: CHANGELOG.md
          config: ${{ github.event.inputs.config || 'preview' }}
          registry-url: ${{ secrets.URL_CIGERAET_GITEA }}
          registry-user: ${{ secrets.USER_CIGERAET_GITEA_PACKAGES }}
          registry-token: ${{ secrets.TOKEN_RELEASE_CIGERAET_GITEA }}
          registry-repo: EltakoConnect
          registry-owner: ${{ secrets.USER_CIGERAET_GITEA_PACKAGES }}
        