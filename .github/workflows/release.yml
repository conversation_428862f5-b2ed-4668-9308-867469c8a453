name: Release & Deployment

on:
  release:
    types: [published]

  pull_request:
    paths:
      - '.github/workflows/release.yml'

  workflow_dispatch:

jobs:
  release_ios:
    name: Release iOS to TestFlight
    runs-on: [self-hosted, macOS]
    environment: production

    env:
      FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}
      MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
      MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
      MATCH_GIT_URL: ${{ vars.MATCH_GIT_URL }}
      APP_FLAVOR: ${{ vars.APP_FLAVOR }}
      APP_SCHEMA: ${{ vars.APP_SCHEMA }}
      APP_IDENTIFIER: ${{ vars.APP_IDENTIFIER }}

    steps:
      - uses: actions/checkout@v4

      - uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Print variables for debug
        run: |
          echo "APP_FLAVOR=$APP_FLAVOR"
          echo "APP_SCHEMA=$APP_SCHEMA"
          echo "APP_IDENTIFIER=$APP_IDENTIFIER"
          echo "MATCH_GIT_URL=$MATCH_GIT_URL"
          echo "RELEASE_TAG=${{ github.event.release.tag_name }}"

      - name: Extract release notes from CHANGELOG
        id: changelog
        run: |
          VERSION=$(grep '^version:' pubspec.yaml | awk '{print $2}' | cut -d+ -f1)
          mkdir -p reports
          awk -v version="$VERSION" '
            BEGIN { collecting=0 }
            /^## / {
              if ($2 ~ "^" version "\\+?[0-9]*$") {
                collecting = 1
              } else if (collecting) {
                exit
              }
            }
            collecting { print }
          ' CHANGELOG.md | sed '/^[[:space:]]*$/d' > reports/release_notes.md
      
          echo "Release notes saved to reports/release_notes.md"
          echo "version=$VERSION" >> "$GITHUB_OUTPUT"
      
      - name: Create and activate venv
        run: |
          python3 -m venv venv
          source venv/bin/activate
          pip3 install -r tools/requirements.txt

      - name: Report Upload, checks for errors and sends a message to MS Teams
        # if: always() && github.event_name == 'release'
        run: |
          source venv/bin/activate
          python3 tools/report_ms_teams.py \
            --webhook_url "${{ secrets.MS_TEAMS_WEBHOOK_URI }}" \
            --report_path "reports/release_notes.md" \
            --title "Eltako Connect App Release:v${{ steps.changelog.outputs.version }}"

      # - name: Install dependencies
      #   working-directory: ios
      #   run: bundle install

      # - name: Run Fastlane beta
      #   if: github.event_name == 'release'
      #   working-directory: ios
      #   run: bundle exec fastlane ios store_release



