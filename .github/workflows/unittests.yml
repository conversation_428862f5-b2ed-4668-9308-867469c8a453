name: Unit Tests
on:
  pull_request:
  workflow_dispatch:
jobs:
  drive:
    runs-on: [ self-hosted, macOS ]
    steps:
      - uses: actions/checkout@v4
      - uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

        # Get version
      - name: Get version
        run: flutter --version

        #        # Verify formatting TODO: Fix formatting and enable
        #      - name: Verify formatting
        #        run: dart format --output=none --set-exit-if-changed .

        # Install dependencies
      - name: Install dependencies
        run: flutter pub get

        # Analyze project source
      - name: Analyze project source
        run: dart analyze --fatal-infos


        # Run code generation
      - name: Run code generation
        run: flutter pub run build_runner build --delete-conflicting-outputs

        # Run tests
      - name: Run tests
        run: flutter test test/unit

        # Publish test results
      - name: Publish test results
        id: compute_test_results
        uses: dorny/test-reporter@v1.6.0
        with:
          name: 'Unit tests report'
          path: reports/*.json
          reporter: 'flutter-json'
          max-annotations: '50'
          token: ${{ secrets.GITHUB_TOKEN }}