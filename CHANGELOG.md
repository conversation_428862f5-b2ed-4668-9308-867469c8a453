# Changelog

## 3.4.0+5

* ELC-1437 Fixed ESB64 switched input channels
* ELC-1402 Fixed ESB64 EnOcean configuration 404 error

## 3.4.0+4

* ELC-1447 Fixed BR64 Matter sensor delete failed
* ELC-1415 Fixed scene button localizations
* ELC-1470 Added ESB64 up/down localization
* Fixed demo sensors for EUD64 and ESB64

## 3.4.0+3

* Fixed notifyListener() on IService, which caused a lot of bugs
* ZGW16 | MQTT Error Screen
* ZGW16 | Network freezed
* ELC-1455 Gerätename fehlerhaft
* ELC-1395 Gerätename immer leer
* ELC-1396 EUD12 | Operating Mode ändert sich nicht
* ELC-1397 EUD12 | Settings in Operation Mode werden nicht gespeichert
* ELC-1464 Zeitschaltuhren | Sonnenwenden Versatz hat falsches Vorzeichen
* ELC-1468 ESB64 | Neue GTIN
* ELC-1454 ESB64 | Release Notes 3.4.0

## 3.4.0+2

* Merged 3.3.0+4

## 3.4.0+1

* Integration des ESB64NP-IPM
* Backend Refactoring

## 3.3.0+5

* Added ZGW16 Update hint
* Adjusted ZGW16 demo firmware version, to prevent update view in electricity meter view
* Adjusted ZGW16 electricity meter name length to 15
* Fixed ZGW16 demo device definition for electricity meter view
* Fixed ZGW16 demo device 2nd demo meter history values
* Fixed EltakoToggle alignment
* Improved translations

## 3.3.0+4

* Added Whats New release image
* Fixed ZGW16 Multi Electricity Meter import/export timestamp cleanup
* Fixed ZGW16 Register table search
* Fixed ZGW16 Multi Electricity Meter current values silent loading
* Fixed ZGW16 MQTT port keyboard closing automatically
* Changed ZGW16 name length to 100 and meter name length to 16

## 3.3.0+3

* Switched ZGW16 consumption and delivery graph color
* Fixed ZGW16 min/max graph defaults

## 3.3.0+2

* Added ZGW16 disable MQTT toggle
* Added ZGW16 MQTT/Modbus TCP Hints
* Bug Fixes ZGW16 Multi Electricity Meter (Meter diagram)

## 3.3.0+1

* ZGW16 Multi Electricity Meter
* Bug Fixes ZGW16 Multi Electricity Meter

## 3.2.1+1

* Fixed not working GS4 mode on ESB62 devices
* Fixed multiple ZGWs in the same network
* Fixed EUD12 flickering on live dimming test

## 3.2.0+24

* ZGW16 Register templates with localizations
* ZGW16 MQTT UX improvements
* Supports firmware 2.2.0 + 2.3.0 + 2.4.0
* Reset View grouped by reset categories

## 3.2.0+10 - 3.2.0+24

* Pipeline Tests

## 3.2.0+9

* Fixed history diagram für multi electricity meter

## 3.2.0+8

* Integration des Multi Zähler Updates für ZGW16-IP

## 3.2.0+7

* Fixed EUD12NPN-BT/230V bugs

## 3.2.0+6

* Integration des EUD12NPN-BT/230V 600W

## 3.1.3+2

* Fixed ESR62NPN-IPM/110-240V GTIN Bug

## 3.1.3+1

* Fixed EUD62NPN-IPM/110-240V GTIN Bug

## 3.1.2+1

* Fixed missing location permission for Bluetooth discovery lower than Android 12
* Übersetzungen verbessert

## 3.1.1+2

* Fixed ESR64PF-IPM single channel
* Fixed BR64 Demo devices switch behavior

## 3.1.1+1

* Fixed ESR64PF-IPM switch behavior
* Fixed BR62/64 Demo update
* Changed iOS App name from Eltako to ELTAKO

## 3.1.0+10

* SU62PF-BT/UC Demo Gerät eingeblendet
* Release Notes SU62PF-BT/UC hinzugefügt
* Übersetzungen verbessert

## 3.1.0+9

* SU62PF-BT/UC Demo Gerät ausblenden
* Integration des ESR64PF-IPM
* Integration des EUD64NPN-IPM

## 3.1.0+8

* NEW Banner für Demo Geräte hinzugefügt

## 3.1.0+7

* Mit Flutter 3.24.3 neu gebaut
* Fixed Developer Mode aus Production App Settings entfernt
* Übersetzungen verbessert

## 3.1.0+6

* What's New aktualisiert
* Release Notes View hinzugefügt
* Übersetzungen verbessert

## 3.1.0+5

* Fixed Time Picker und alle Bottom Modal Sheets
* Fixed Empty Service ID Error in der Discovery View

## 3.1.0+4

* SU62PF-BT/UC Demo Gerät einblenden

## 3.1.0+3

* SU62PF-BT/UC Bluetooth Advertising Mode hinzugefügt
* 'Geräte hinzufügen' Button in der Discovery entfernt
* Übersetzungen verbessert

## 3.1.0+2

* Zentral Ein/Aus von SU62PF-BT/UC entfernt
* Übersetzungen verbessert

## 3.1.0+1

* Integration der SU62PF-BT/UC
* Übersetzungen verbessert

## 3.0.3+2

* Fixed Astro negative offset Bug for EUD12

## 3.0.3+1

* Fixed Astro negative offset Bug

## 3.0.2+1

* Default Sprache auf Englisch geändert
* Übersetzungen verbessert

## 3.0.1+3

* Fixed What's new Dialog
* Fixed 24:51 Uhrzeit Bug
* Fixed sporadisch fehlende Modellbezeichnung im Bluetooth Service
* Fixed nicht updatende Picker

## 3.0.0+6

* Build Flavor Preview und Production hinzugefügt
* What's new Dialog
* App Icon
* 75 Jahre Splashscreen

## 3.0.0

* Umstieg auf Flutter App
* Redesign der App
* Neue App Architektur

# Android

## 2.7.2

* maybe fixed App freeze and crash on startup
* Übersetzungen verbessert

## 2.7.1

* ESB62 Offset Modus eingeblendet mit 15 Sekunden Nachlauf

## 2.7.0

* Integration des EUD12NPN-BT/230V

## 2.6.0

* Integration des EUD62NPN-IPM/110-240V Matter
* Added Matter Teach In Dialog
* ESB62 Offset Modus hinzugefügt aber ausgeblendet
* fixed Uhrzeit springt in Winterzeit um 1h zurück Bug
* fixed Provisioning Bug

## 2.5.0

* ASSU-BT/230V Demo Gerät einblenden
* ESR62 NP zu Demo Geräten hinzugefügt
* Übersetzungen angepasst

## 2.4.0

* Konfigurationen speichern und importieren (EEPROM Klon) hinzugefügt
* Integration der ASSU-BT/230V mit deaktiviertem Demo Gerät

## 2.3.0

* S2U12DBT-UC Demo Gerät einblenden
* fixed Initial Connection Error
* Übersetzungen verbessert

## 2.2.0

* BR62 Firmware Updates via App durchführen
* Notification wenn neues BR62 Firmware Update verfügbar
* EUD62 Dimmkurven hinzugefügt (1.6.0)
* Erneute Authentifizierung bei nicht validem BR62 API Key
* Spanische Übersetzungen verbessert
* BR62 SemVer Versionierung implementiert

## 2.1.1

* Integration der S2U12DBT-UC mit deaktiviertem Demo Gerät
* Demo Geräte Toggle ausblenden
* fixed BR62 Slider Buttons Bug
* fixed Time Program Channel 3 Bug

## 2.1.0

* ESB62 Laufzeit und GS4-Modus hinzugefügt

## 2.0.1

* Provisioning für Baureihe 62 hinzugefügt
* Sliver App Bar hinzugefügt
* Themes entkoppelt
* Package und Flutter Version updates

## 2.0.0

* WIFI Implementierung
* Integration der Baureihe 62 (ESR62, EUD62, ESB62)
* MFZ Diagramm für EAW ausgetauscht
* App Sprache NL hinzugefügt
* Neue Icons

## 1.2.0

* MFZ12DBT-UC Demo Gerät freigeschalten
* SU12DBT/1+1-UC Display Lock Verhalten verbessert
* fixed MFZ12DBT-UC Slider Bug
* fixed SU12DBT/1+1-UC Astro Slider Bug
* fixed App Sprache für Regionen (AT, CH, usw.) Bug

## 1.1.1

* Integration der MFZ12DBT-UC mit deaktiviertem Demo Gerät
* Bluetooth Settings Service hinzugefügt
* Eltako Logging Package hinzugefügt
* Code Dokumentation ausgebaut
* Zentral Ein/Aus refactored
* Kanäle UI redesign
* Verbindung getrennt Dialog hinzugefügt
* fixed Connection Widget Text
* fixed Impuls + Name Icons
* fixed Bluetooth Verbindung Bugs
* fixed Astro UI Bugs
* fixed UI Bugs

## 1.1.0

* Kompatibilität für SU12DBT/1+1-UC V2 (Zentral Ein/Aus)
* Werksreset
* Finnische Übersetzung verbessert
* fixed Demo Geräte Bug
* fixed Time Picker abgeschnitten

## 1.0.0

* Integration der SU12DBT/1+1-UC