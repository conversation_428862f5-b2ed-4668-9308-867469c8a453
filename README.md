# Eltako Connect App

[![Nightly Builds (Adhoc & Beta)](https://github.com/Eltako/app-EltakoConnect/actions/workflows/distribute-artifacts.yml/badge.svg)](https://github.com/Eltako/app-EltakoConnect/actions/workflows/distribute-artifacts.yml)
[![Unit Tests](https://github.com/Eltako/EltakoConnect/actions/workflows/unittests.yml/badge.svg)](https://github.com/Eltako/EltakoConnect/actions/workflows/unittests.yml)
[![Screenshots](https://github.com/Eltako/EltakoConnect/actions/workflows/screenshots.yml/badge.svg)](https://github.com/Eltako/EltakoConnect/actions/workflows/screenshots.yml)

Crossplatform Flutter App for controlling Eltako devices.

## Table of contents

<!-- TOC -->
* [Getting Started](#getting-started)
* [Changelog](#changelog)
* [App Release](#app-release)
* [Git-Workflow](#git-workflow)
* [Tools](#tools)
<!-- TOC -->

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

* [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
* [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Changelog

See all changes from Android until 2.7.2 and for both Platforms from Version 3.0.0 at the [Changelog](/CHANGELOG.md)

## App Release

[App Release with Signing](/doc/app-release.md)

## Git-Workflow

[Git-Workflow](/doc/git-workflow.md)
[Build Flavours](./doc/build-flavors.md)

## Tools

* [Auto generate assets](./doc/tools/asset-generation.md)
* [App Icon Generation](./doc/tools/app-icon-generation.md)
* [Splash Screen Generation](./doc/tools/splash-screen-generation.md)

## Good to know

* The **Android Manifest** file is merged depending on the packages used in our project and can be found under [build/app/intermediates/merged_manifests/debug/AndroidManifest.xml](./build/app/intermediates/merged_manifests/debug/AndroidManifest.xml). That is why we don't need to extend the AndroidManifest.xml file in the android folder with e.g. Bluetooth or Location permissions.
