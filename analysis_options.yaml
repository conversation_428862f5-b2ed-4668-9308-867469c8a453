# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

analyzer:
  plugins:
    - custom_lint
  errors:
    avoid_if_with_enum: ignore
    # Declare method return types.
    # BAD:  m() => 0;
    # GOOD: int m() => 0;
    always_declare_return_types: warning

    # Separate the control structure expression from its statement.
    # -> No one-liner-if-statements.
    always_put_control_body_on_new_line: ignore

    # Put required named parameters first.
    always_put_required_named_parameters_first: warning

    # Specify @required on named parameters without defaults. (DEPRECATED)
    always_require_non_null_named_parameters: ignore

    # Specify type annotations.
    # BAD:  var x = 0; final y = 0;
    # GOOD: int x = 0; final int y = 0;
    always_specify_types: ignore

    # Avoid relative imports for files in lib/.
    # BAD:  import '../bar.dart'; import 'src/xyz.dart';
    # GOOD: import 'package:foo/bar.dart';
    always_use_package_imports: warning

    # Annotate overridden members.
    # -> Always annotate @override.
    annotate_overrides: warning
    # recommended

    # Annotate redeclared members. (EXPERIMENTAL)
    # -> Always annotate @redeclare.
    annotate_redeclares: ignore

    # Avoid annotating with dynamic when not required.
    # BAD:  dynamic x(dynamic y) {}
    # GOOD: x(y) {}
    avoid_annotating_with_dynamic: warning

    # Avoid bool literals in conditional expressions.
    # BAD:  condition ? true : boolExpression;
    # GOOD: condition || boolExpression;
    avoid_bool_literals_in_conditional_expressions: warning

    # Avoid catches without on clauses.
    # BAD:  try {} catch (e) {}
    # GOOD: try {} on Exception catch (e) {}
    avoid_catches_without_on_clauses: ignore

    # Don’t explicitly catch Error or types that implement it.
    # BAD:  try {} on Error catch (e) {}
    # GOOD: try {} on Exception catch (e) {}
    avoid_catching_errors: warning

    # Avoid defining a class that contains only static members.
    avoid_classes_with_only_static_members: ignore

    # Avoid double and int checks.
    # BAD:  if (x is double) {} else if (x is int) {}
    # GOOD: if (x is num) {}
    avoid_double_and_int_checks: warning

    # Avoid method calls or property accesses on a “dynamic” target.
    avoid_dynamic_calls: warning

    # Avoid empty else statements.
    avoid_empty_else: warning

    # Avoid overloading operator == and hashCode on classes not marked @immutable.
    # -> Always mark classes @immutable if overriding == and hashCode.
    avoid_equals_and_hash_code_on_mutable_classes: warning

    # Avoid escaping inner quotes by converting surrounding quotes.
    # BAD:  var s = 'It\'s a string';
    # GOOD: var s = "It's a string";
    avoid_escaping_inner_quotes: warning

    # Avoid field initializers in const classes.
    avoid_field_initializers_in_const_classes: warning

    # Avoid final for parameter declarations.
    # BAD:  m(final int x) {}
    # GOOD: m(int x) {}
    avoid_final_parameters: ignore

    # Avoid using forEach with a function literal.
    # BAD:  people.forEach((person) {});
    # GOOD: for (var person in people) {}
    avoid_function_literals_in_foreach_calls: warning
    # recommended

    # Don’t implement classes that override ==.
    avoid_implementing_value_types: ignore

    # Don’t explicitly initialize variables to null.
    # BAD:  int? x = null;
    # GOOD: int? x;
    avoid_init_to_null: warning
    # recommended

    # Avoid JavaScript rounded ints.
    # BAD:  int value = 9007199254740995;
    # GOOD: BigInt value = BigInt.parse('9007199254740995');
    avoid_js_rounded_ints: warning

    # Don’t declare multiple variables on a single line.
    # BAD:  int x = 0, y = 0;
    # GOOD: int x = 0; int y = 0;
    avoid_multiple_declarations_per_line: warning

    # Don’t check for null in custom == operators.
    # -> As null is a special value, no instance of any class (other than Null) can be equivalent to it.
    avoid_null_checks_in_equality_operators: warning
    # recommended

    # Avoid positional boolean parameters.
    # BAD:  m(true);
    # GOOD: m(isEnabled: true);
    avoid_positional_boolean_parameters: ignore

    # Avoid print calls in production code.
    # BAD:  print('Hello World');
    # GOOD: if (kDebugMode) print('Hello World');
    avoid_print: warning
    #default

    # Avoid private typedef functions.
    # -> Avoid private typedef functions used only once.
    avoid_private_typedef_functions: warning

    # Avoid redundant argument values.
    # -> Don't pass an argument that matches the corresponding parameter’s default value.
    avoid_redundant_argument_values: warning

    # Avoid relative imports for files in lib/.
    # BAD:  import '../bar.dart';
    # GOOD: import 'package:foo/bar.dart';
    avoid_relative_lib_imports: warning

    # Don’t rename parameters of overridden methods.
    avoid_renaming_method_parameters: warning
    # recommended

    # Avoid return types on setters.
    # BAD:  void set x(int value) {}
    # GOOD: set x(int value) {}
    avoid_return_types_on_setters: warning
    # recommended

    # Avoid returning null from members whose return type is bool, double, int, or num. (DEPRECATED)
    avoid_returning_null: ignore

    # Avoid returning null for Future. (DEPRECATED)
    avoid_returning_null_for_future: ignore

    # Avoid returning null for void.
    avoid_returning_null_for_void: warning
    # recommended

    # Avoid returning this from methods just to enable a fluent interface.
    avoid_returning_this: ignore

    # Avoid setters without getters.
    avoid_setters_without_getters: warning

    # Avoid shadowing type parameters.
    # BAD:  class A<T> { m<T>() {} }
    # GOOD: class A<T> { m<U>() {} }
    avoid_shadowing_type_parameters: warning

    # Avoid single cascade in expression statements.
    # BAD:  o..m();
    # GOOD: o.m();
    avoid_single_cascade_in_expression_statements: warning
    # recommended

    # Avoid slow async dart:io methods.
    avoid_slow_async_io: warning

    # Avoid .toString() in production code since results may be minified.
    avoid_type_to_string: ignore

    # Avoid types as parameter names.
    # BAD:  m(f(int));
    # GOOD: m(f(int x));
    avoid_types_as_parameter_names: warning

    # Avoid annotating types for function expression parameters.
    # BAD:  var names = people.map((Person person) => person.name);
    # GOOD: var names = people.map((person) => person.name);
    avoid_types_on_closure_parameters: warning

    # Avoid unnecessary containers.
    # -> Wrapping a widget in Container with no other parameters set has no effect and makes code needlessly more complex.
    avoid_unnecessary_containers: warning
    # default

    # Avoid overriding a final field to return different values if called multiple times. (UNRELEASED)
    # BAD:  class A { final int i; } -> class B extends A { int i; }
    # GOOD: class A { final int i; } -> class B extends A { late final int i = 5; }
    avoid_unstable_final_fields: ignore

    # Avoid defining unused parameters in constructors.
    avoid_unused_constructor_parameters: warning

    # Avoid async functions that return void.
    # BAD:  void m() async {}
    # GOOD: Future<void> m() async {}
    avoid_void_async: warning

    # Avoid using web-only libraries outside Flutter web plugin packages.
    avoid_web_libraries_in_flutter: warning
    # default

    # Await only futures.
    await_only_futures: warning

    # Name extensions using UpperCamelCase.
    camel_case_extensions: warning

    # Name types using UpperCamelCase.
    camel_case_types: warning

    # Cancel instances of dart.async.StreamSubscription.
    # -> Always must call subscription.cancel().
    cancel_subscriptions: warning

    # Cascade consecutive method invocations on the same reference.
    # BAD:  o..m(); o..m();
    # GOOD: o.m()..n();
    cascade_invocations: warning

    # Don’t cast a nullable value to a non nullable type.
    # BAD:  A? a; var v = a as B;
    # GOOD: A? a; var v = a! as B;
    cast_nullable_to_non_nullable: warning

    # Close instances of dart.core.Sink.
    # -> Always must call ioSink.cancel().
    close_sinks: warning

    # Invocation of various collection methods with arguments of unrelated types.
    # BAD:  <int>[].contains('1');
    # GOOD: <int>[].contains(1);
    collection_methods_unrelated_type: warning

    # Sort combinator names alphabetically.
    # BAD:  import 'a.dart' show B, A hide D, C;
    # GOOD: import 'a.dart' show A, B hide C, D;
    combinators_ordering: warning

    # Only reference in scope identifiers in doc comments.
    # BAD:  /// Return true if [x] > [y] -- bool gt(int x);
    # GOOD: /// Return true if [x] > [y] -- bool gt(int x, int y);
    comment_references: warning

    # Missing conditional import.
    # BAD:  if (condition) import 'file_that_does_not_exist.dart';
    # GOOD: if (condition) import 'file_that_does_exist.dart';
    conditional_uri_does_not_exist: warning

    # Prefer using lowerCamelCase for constant names.
    # BAD:  const int MY_CONSTANT = 0;
    # GOOD: const int myConstant = 0;
    constant_identifier_names: warning
    # recommended

    # Avoid control flow in finally blocks.
    # -> No return, break or continue in finally blocks.
    control_flow_in_finally: warning
    # recommended

    # Do use curly braces for all flow control structures.
    # -> Only for breaking lines, one-liners are allowed.
    curly_braces_in_flow_control_structures: warning

    # Attach library doc comments to library directives.
    dangling_library_doc_comments: ignore

    # Depend on referenced packages.
    depend_on_referenced_packages: warning

    # Missing deprecated annotation.
    # -> @deprecated on class, constructors, etc. (not on class only)
    deprecated_consistency: warning

    # Avoid using deprecated elements from within the package in which they are declared.
    deprecated_member_use_from_same_package: warning

    # Do reference all public properties in debug methods.
    # -> debugFillProperties(...) must contain all public properties.
    diagnostic_describe_all_properties: ignore

    # Adhere to Effective Dart Guide directives sorting conventions.
    # -> imports(dart: > package: > relative) > exports (ascending)
    directives_ordering: warning

    # Don’t invoke asynchronous functions in non-async blocks.
    # -> Must use async.
    discarded_futures: warning

    # Do not use environment declared variables.
    # -> Don't use fromEnvironment or hasEnvironment factory constructors.
    do_not_use_environment: warning

    # Avoid empty catch blocks.
    empty_catches: warning

    # Use ; instead of {} for empty constructor bodies.
    empty_constructor_bodies: warning
    # recommended

    # Avoid empty statements.
    empty_statements: warning
    # recommended

    # Put a single newline at end of file.
    eol_at_end_of_file: warning

    # Define case clauses for all constants in enum-like classes.
    # -> Checks on statics, this may be a problem if it's not meant to be enum-like.
    exhaustive_cases: warning
    # recommended

    # Name source files using lowercase_with_underscores.
    file_names: warning

    # Use Flutter to do format: // TODO(username): message, https://URL-to-issue.
    flutter_style_todos: warning

    # Always override hashCode if overriding ==.
    hash_and_equals: warning

    # Don’t import implementation files from another package.
    implementation_imports: warning
    # recommended

    # Explicitly tear-off call methods when using an object as a Function.
    # -> Must use .call on params that are functions.
    implicit_call_tearoffs: ignore

    # Don’t implicitly reopen classes. (EXPERIMENTAL)
    implicit_reopen: ignore

    # Use case expressions that are valid in Dart 3.0. (EXPERIMENTAL)
    # -> Only constant values in switch cases.
    invalid_case_patterns: ignore

    # Invocation of Iterable.contains with references of unrelated types. (DEPRECATED)
    # BAD:  <int>[].contains('1');
    # GOOD: <int>[].contains(1);
    iterable_contains_unrelated_type: ignore

    # Join return statement with assignment when possible.
    join_return_with_assignment: warning

    # Start multiline strings with a newline.
    leading_newlines_in_multiline_strings: warning

    # Attach library annotations to library directives.
    # -> Usage of 'library' keyword.
    library_annotations: warning

    # Name libraries using lowercase_with_underscores.
    library_names: warning
    # recommended

    # Avoid using private types in public APIs.
    library_prefixes: warning
    # recommended

    # Avoid using private types in public APIs.
    library_private_types_in_public_api: warning
    # recommended

    # Avoid lines longer than 80 characters.
    lines_longer_than_80_chars: ignore

    # Invocation of remove with references of unrelated types. (DEPRECATED)
    # BAD:  <int>[].remove('1');
    # GOOD: <int>[].remove(1);
    list_remove_unrelated_type: ignore

    # Boolean expression composed only with literals.
    # BAD:  if (true) {}
    # GOOD: if (variableValue) {}
    literal_only_boolean_expressions: warning

    # Use matching super parameter names.
    matching_super_parameters: warning

    # Missing whitespace between adjacent strings.
    # -> On new lines, the last one must end with a whitespace.
    missing_whitespace_between_adjacent_strings: warning

    # Don’t use adjacent strings in list.
    # BAD:  ['a' 'b', 'c'];
    # GOOD: ['a' + 'b', 'c'];
    no_adjacent_strings_in_list: warning

    # No default cases. (EXPERIMENTAL)
    no_default_cases: ignore

    # Don’t use more than one case with same value.
    no_duplicate_case_values: warning

    # Avoid leading underscores for library prefixes.
    no_leading_underscores_for_library_prefixes: warning
    # recommended

    # Avoid leading underscores for local identifiers.
    no_leading_underscores_for_local_identifiers: warning
    # recommended

    # Don’t compare booleans to boolean literals.
    # BAD:  if (x == true) {}
    # GOOD: if (x) {}
    no_literal_bool_comparisons: warning

    # Don’t put any logic in createState.
    # BAD:  MyState createState() => MyState(42);
    # GOOD: MyState createState() => MyState();
    no_logic_in_create_state: warning
    # default

    # Avoid calling toString() on runtimeType.
    # BAD:  String toString() => '$runtimeType()';
    # GOOD: String toString() => 'MyClass()';
    no_runtimeType_toString: warning

    # Don’t assign a variable to itself.
    no_self_assignments: warning

    # Don’t use wildcard parameters or variables.
    # BAD:  var _ = 1;
    # GOOD: for (var _ in [1, 2, 3]) {};
    no_wildcard_variable_uses: warning

    # Name non-constant identifiers using lowerCamelCase.
    non_constant_identifier_names: warning

    # Noop primitive operations.
    # BAD:  x = intValue.toInt();
    # GOOD: x = intValue;
    noop_primitive_operations: warning

    # Don’t use null check on a potentially nullable type parameter.
    null_check_on_nullable_type_parameter: warning

    # Do not pass null as an argument where a closure is expected.
    # BAD:  Future.delayed(Duration.zero, null);
    # GOOD: Future.delayed(Duration.zero, () {});
    null_closures: warning
    # recommended

    # Omit type annotations for local variables.
    # -> Opposite approach of 'always_specify_types'.
    omit_local_variable_types: ignore

    # Avoid defining a one-member abstract class when a simple function will do.
    # -> Use typedef instead of abstract class with one method.
    one_member_abstracts: ignore

    # Only throw instances of classes extending either Exception or Error.
    only_throw_errors: warning

    # Don’t override fields.
    overridden_fields: warning
    # recommended

    # Provide doc comments for all public APIs.
    package_api_docs: warning

    # Use lowercase_with_underscores for package names.
    package_names: warning
    # recommended

    # Prefix library names with the package name and a dot-separated path.
    # Example: library my_package.foo.bar;
    package_prefixed_library_names: warning

    # Don’t reassign references to parameters of functions or methods.
    # BAD:  m(int x) { x = 1; }
    # GOOD: m(int x) { var y = 1; }
    parameter_assignments: warning

    # Use adjacent strings to concatenate string literals.
    # BAD:  'a' + 'b';
    # GOOD: 'a' 'b';
    prefer_adjacent_string_concatenation: ignore
    # recommended

    # Prefer putting asserts in initializer lists.
    # BAD:  A() { assert(x != null); }
    # GOOD: A() : assert(x != null);
    prefer_asserts_in_initializer_lists: warning

    # Prefer asserts with message.
    prefer_asserts_with_message: warning

    # Use collection literals when possible.
    # BAD:  var addresses = Map<String, String>();
    # GOOD: var addresses = <String, String>{};
    prefer_collection_literals: warning
    # recommended

    # Prefer using ??= over testing for null.
    prefer_conditional_assignment: warning
    # recommended

    # Prefer const with constant constructors.
    prefer_const_constructors: warning
    # default

    # Prefer declaring const constructors on @immutable classes.
    prefer_const_constructors_in_immutables: warning
    # default

    # Prefer const over final for declarations.
    prefer_const_declarations: warning
    # default

    # Prefer const literals as parameters of constructors on @immutable classes.
    prefer_const_literals_to_create_immutables: warning
    # default

    # Prefer defining constructors instead of static methods to create instances.
    # BAD:  class A { static A make() => A(); }
    # GOOD: class A { A.make(); }
    prefer_constructors_over_static_methods: warning

    # Use contains for List and String instances.
    # BAD:  <int>[].indexOf(0) != -1;
    # GOOD: <int>[].contains(0);
    prefer_contains: warning
    # recommended

    # Prefer double quotes where they won’t require escape sequences.
    prefer_double_quotes: ignore

    # Use => for short members whose body is a single return statement.
    prefer_expression_function_bodies: warning

    # Private field could be final.
    prefer_final_fields: warning
    # recommended

    # Prefer final in for-each loop variable if reference is not reassigned.
    # BAD:  for (var element in elements) {}
    # GOOD: for (final element in elements) {}
    prefer_final_in_for_each: warning

    # Prefer final for variable declarations if they are not reassigned.
    prefer_final_locals: warning

    # Prefer final for parameter declarations if they are not reassigned.
    prefer_final_parameters: ignore

    # Prefer ‘for’ elements when building maps from iterables.
    prefer_for_elements_to_map_fromIterable: warning
    # recommended

    # Use forEach to only apply a function to all the elements.
    # BAD:  for (final key in map.keys.toList()) { map.remove(key); }
    # GOOD: map.keys.toList().forEach(map.remove);
    prefer_foreach: ignore

    # Use a function declaration to bind a function to a name.
    # BAD:  void main() { var localFunction = () {}; }
    # GOOD: void main() { localFunction() {} }
    prefer_function_declarations_over_variables: warning
    # recommended

    # Prefer generic function type aliases.
    # BAD:  typedef void F();
    # GOOD: typedef F = void Function();
    prefer_generic_function_type_aliases: warning

    # Prefer if elements to conditional expressions where possible.
    # BAD:  var list = ['a', 'b', condition ? 'c' : null].where((e) => e != null).toList();
    # GOOD: var list = ['a', 'b', if (condition) 'c'];
    prefer_if_elements_to_conditional_expressions: warning

    # Prefer using if null operators.
    # BAD:  var x = a != null ? a : b;
    # GOOD: var x = a ?? b;
    prefer_if_null_operators: warning
    # recommended

    # Use initializing formals when possible.
    # BAD:  class A { int x; A(int x) { this.x = x; } }
    # GOOD: class A { int x; A(this.x); }
    prefer_initializing_formals: warning
    # recommended

    # Inline list item declarations where possible.
    # BAD:  var l = ['a']..add('b')..add('c');
    # GOOD: var l = ['a', 'b', 'c'];
    prefer_inlined_adds: warning

    # Prefer int literals over double literals.
    # BAD:  const double x = 1.0;
    # GOOD: const double x = 1;
    prefer_int_literals: ignore

    # Use interpolation to compose strings and values.
    # BAD:  a + ' ' +b;
    # GOOD: '$a $b';
    prefer_interpolation_to_compose_strings: warning
    # recommended

    # Use isEmpty for Iterables and Maps.
    # BAD:  if (list.length == 0) {}
    # GOOD: if (list.isEmpty) {}
    prefer_is_empty: warning

    # Use isNotEmpty for Iterables and Maps.
    # BAD:  if (!list.isEmpty) {}
    # GOOD: if (list.isNotEmpty) {}
    prefer_is_not_empty: warning

    # Prefer is! operator.
    # BAD:  if (!(x is X)) {}
    # GOOD: if (x is! X) {}
    prefer_is_not_operator: warning
    # recommended

    # Prefer to use whereType on iterable.
    # BAD:  iterable.where((e) => e is MyClass);
    # GOOD: iterable.whereType<MyClass>();
    prefer_iterable_whereType: warning

    # Prefer using mixins.
    # BAD:  class A{} class B extends Object with A {}
    # GOOD: mixin M{} class C with M {}
    prefer_mixin: warning

    # Prefer null aware method calls.
    # BAD:  if (x != null) x.foo();
    # GOOD: x?.foo();
    prefer_null_aware_method_calls: warning

    # Prefer using null aware operators.
    # BAD:  v = a == null ? null : a.b;
    # GOOD: v = a?.b;
    prefer_null_aware_operators: warning
    # recommended

    # Prefer relative imports for files in lib/.
    # BAD:  import 'package:my_package/bar.dart';
    # GOOD: import 'bar.dart';
    prefer_relative_imports: ignore

    # Only use double quotes for strings containing single quotes.
    prefer_single_quotes: warning

    # Use spread collections when possible.
    # BAD:  print(['a']..addAll(ints.map((i) => i.toString()))..addAll(['c']));
    # GOOD: print(['a', ...ints.map((i) => i.toString()), 'c']);
    prefer_spread_collections: warning
    # recommended

    # Prefer typing uninitialized variables and fields.
    # BAD:  class A { var x; }
    # GOOD: class A { int x; }
    prefer_typing_uninitialized_variables: warning

    # Don’t use the Null type, unless you are positive that you don’t want void.
    # Bad:  Null f();
    # Good: void f();
    prefer_void_to_null: warning

    # Provide a deprecation message, via @Deprecated(“message”).
    provide_deprecation_message: warning

    # Document all public members.
    public_member_api_docs: warning

    # Property getter recursively returns itself.
    # BAD:  int get foo => foo;
    # GOOD: int get foo => _foo;
    recursive_getters: warning
    # recommended

    # Use trailing commas for all function calls and declarations.
    # -> Only for breaking lines.
    require_trailing_commas: warning

    # Use secure urls in pubspec.yaml.
    # BAD:  http://example.com, git://github.com/dart-lang/example/example.git
    # GOOD: https://example.com
    secure_pubspec_urls: ignore

    # SizedBox for whitespace.
    # BAD:  Container(height: 10.0);
    # GOOD: SizedBox(height: 10.0);
    sized_box_for_whitespace: warning
    # default

    # Use SizedBox shrink and expand named constructors.
    # BAD:  SizedBox(height: double.infinity, width: double.infinity, ...);
    # GOOD: SizedBox.expand(...);
    sized_box_shrink_expand: warning

    # Prefer using /// for doc comments.
    # -> Don't use /** */ for doc comments.
    slash_for_doc_comments: warning
    # recommended

    # Sort child properties last in widget instance creations.
    sort_child_properties_last: warning
    # default

    # Sort constructor declarations before other members.
    sort_constructors_first: ignore

    # Sort pub dependencies alphabetically.
    sort_pub_dependencies: warning

    # Sort unnamed constructor declarations first.
    sort_unnamed_constructors_first: warning

    # Test type arguments in operator ==(Object other).
    test_types_in_equals: warning

    # Avoid throw in finally block.
    throw_in_finally: warning

    # Tighten type of initializing formal.
    # BAD:  A.c1(this.p) : assert(p != null);
    # GOOD: A.c2(String this.p);
    tighten_type_of_initializing_formals: warning

    # Type annotate public APIs.
    # BAD:  m(x, y) {}
    # GOOD: int m(int x, int y) {}
    type_annotate_public_apis: warning

    # Don’t type annotate initializing formals.
    # BAD:  A(int this.p);
    # GOOD: A(this.p);
    type_init_formals: warning
    # recommended

    # Don’t use constant patterns with type literals.
    # BAD:  if (x case num) { print('int or double'); }
    # GOOD: if (x case num _) { print('int or double'); }
    type_literal_in_constant_pattern: warning

    # Future results in async function bodies must be awaited or marked unawaited using dart:async.
    unawaited_futures: warning

    # Don't use <> for generics in doc comments.
    unintended_html_in_doc_comment: ignore

    # Unnecessary await keyword in return.
    # BAD:  Future<int> f1() async => await future;
    # GOOD: Future<int> f2() async => future;
    unnecessary_await_in_return: warning

    # Avoid using braces in interpolation when not needed.
    # BAD:  'Hello ${name}';
    # GOOD: 'Hello $name';
    unnecessary_brace_in_string_interps: warning
    # recommended

    # Don’t use explicit breaks when a break is implied.
    # -> No break in switch cases.
    unnecessary_breaks: warning

    # Avoid const keyword.
    # BAD:  class A { const A(); } => const a = const A();
    # GOOD: class A { const A(); } => const a = A();
    unnecessary_const: warning
    # recommended

    # Unnecessary .new constructor name.
    # BAD:  A.new();
    # GOOD: A();
    unnecessary_constructor_name: warning
    # recommended

    # Don’t use final for local variables.
    unnecessary_final: ignore

    # Avoid wrapping fields in getters and setters just to be “safe”.
    unnecessary_getters_setters: warning
    # recommended

    # Don’t create a lambda when a tear-off will do.
    # BAD:  list.forEach((e) => print(e));
    # GOOD: list.forEach(print);
    unnecessary_lambdas: warning

    # Don’t specify the late modifier when it is not needed.
    unnecessary_late: warning
    # recommended

    # Avoid library directives unless they have documentation comments or annotations.
    unnecessary_library_directive: warning

    # Unnecessary new keyword.
    unnecessary_new: warning
    # recommended

    # Avoid null in null-aware assignment.
    # BAD:  x ??= null;
    # GOOD: x ??= 1;
    unnecessary_null_aware_assignments: warning
    # recommended

    # Unnecessary null aware operator on extension on a nullable type.
    unnecessary_null_aware_operator_on_extension_on_nullable: ignore

    # Unnecessary null checks. (EXPERIMENTAL)
    # -> Don't apply a null check when a nullable value is accepted.
    unnecessary_null_checks: ignore

    # Avoid using null in if null operators.
    # BAD:  var y = null ?? 1;
    # GOOD: var y = a ?? 1;
    unnecessary_null_in_if_null_operators: warning
    # recommended

    # Use a non-nullable type for a final variable initialized with a non-nullable value.
    # BAD:  final int? x = 1;
    # GOOD: final int x = 1;
    unnecessary_nullable_for_final_variable_declarations: warning
    # recommended

    # Don’t override a method to do a super method invocation with the same parameters.
    # BAD:  class A { @override void m() { super.m(); } }
    # GOOD: class A { @override void m() { doSomethingElse(); } }
    unnecessary_overrides: warning

    # Unnecessary parentheses can be removed.
    unnecessary_parenthesis: warning

    # Unnecessary raw string.
    unnecessary_raw_strings: warning

    # Avoid using unnecessary statements.
    # BAD:  x;
    # GOOD: y = x;
    unnecessary_statements: warning

    # Remove unnecessary backslashes in strings.
    # BAD:  'a: \"b\"';
    # GOOD: 'a: "b"';
    unnecessary_string_escapes: warning
    # recommended

    # Unnecessary string interpolation.
    # BAD:  x = '$name';
    # GOOD: x = name;
    unnecessary_string_interpolations: warning
    # recommended

    # Don’t access members with this unless avoiding shadowing.
    unnecessary_this: warning
    # recommended

    # Unnecessary toList() in spreads.
    unnecessary_to_list_in_spreads: warning
    # recommended

    # Unreachable top-level members in executable libraries.
    unreachable_from_main: warning

    # Equality operator == invocation with references of unrelated types.
    # BAD:  if ('1' == 1) {};
    # GOOD: if (1 == 1) {};
    unrelated_type_equality_checks: warning

    # Avoid unsafe HTML APIs.
    unsafe_html: warning

    # Do not use BuildContexts across async gaps.
    # BAD:  await ...; Navigator.of(context).pop();
    # BAD:  await ...; if (context.mounted) Navigator.of(context).pop();
    use_build_context_synchronously: warning
    # default

    # Use ColoredBox.
    # -> Use ColoredBox instead of Container with color.
    use_colored_box: warning

    # Use DecoratedBox.
    # -> Use DecoratedBox instead of Container with decoration.
    use_decorated_box: warning

    # Use enums rather than classes that behave like enums.
    use_enums: warning

    # Prefer an 8-digit hexadecimal integer(0xFFFFFFFF) to instantiate Color.
    use_full_hex_values_for_flutter_colors: warning
    # default

    # Use generic function type syntax for parameters.
    # BAD:  Iterable<T> where(bool predicate(T element)) {}
    # GOOD: Iterable<T> where(bool Function(T) predicate) {}
    use_function_type_syntax_for_parameters: warning
    # recommended

    # Use if-null operators to convert nulls to bools.
    # BAD:  if (nullableBool == true) {}
    # GOOD: if (nullableBool ?? false) {}
    use_if_null_to_convert_nulls_to_bools: warning

    # Prefer intValue.isOdd/isEven instead of checking the result of % 2.
    use_is_even_rather_than_modulo: warning

    # Use key in widget constructors.
    use_key_in_widget_constructors: warning
    # default

    # Use late for private members with a non-nullable type. (EXPERIMENTAL)
    # -> Use late instead of declaring nullable wherever possible.
    use_late_for_private_fields_and_variables: warning

    # Use predefined named constants.
    # BAD:  const Duration(seconds: 0);
    # GOOD: Duration.zero;
    use_named_constants: warning

    # Use raw string to avoid escapes.
    # BAD:  var s = 'A string with only \\ and \$';
    # GOOD: var s = r'A string with only \ and $';
    use_raw_strings: warning

    # Use rethrow to rethrow a caught exception.
    # BAD:  try {} catch (e) { throw e; }
    # GOOD: try {} catch (e) { rethrow; }
    use_rethrow_when_possible: warning
    # recommended

    # Use a setter for operations that conceptually change a property.
    use_setters_to_change_properties: ignore

    # Use string buffers to compose strings.
    use_string_buffers: ignore

    # Use string in part of directives.
    # BAD:  part of my_library;
    # GOOD: part '../../my_library.dart';
    use_string_in_part_of_directives: ignore

    # Use super-initializer parameters where possible. (EXPERIMENTAL)
    use_super_parameters: warning
    # recommended

    # Use throwsA matcher instead of fail().
    use_test_throws_matchers: ignore

    # Start the name of the method with to/_to or as/_as if applicable.
    use_to_and_as_if_applicable: warning

    # Use valid regular expression syntax.
    # BAD:  RegExp(r'(');
    # GOOD: RegExp(r'\(');
    valid_regexps: warning

    # Don’t assign to void.
    void_checks: warning
  exclude: [lib/generated/**, pubspec.yaml, lib/l10n/**]
linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  custom_lint:
    rules:
      - smart_translate_lint_role: true # for enabled or false for disabled
      - smart_translate_lint_locale_keys_role: true # for enabled or false for disabled
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule

    # Additional information about this file can be found at
    # https://dart.dev/guides/language/analysis-options

    - always_declare_return_types
    # - always_put_control_body_on_new_line
    - always_put_required_named_parameters_first
    # - always_require_non_null_named_parameters
    # - always_specify_types
    - always_use_package_imports
    - annotate_overrides
    # - annotate_redeclares
    - avoid_annotating_with_dynamic
    - avoid_bool_literals_in_conditional_expressions
    # - avoid_catches_without_on_clauses
    - avoid_catching_errors
    # - avoid_classes_with_only_static_members
    - avoid_double_and_int_checks
    - avoid_dynamic_calls
    - avoid_empty_else
    - avoid_equals_and_hash_code_on_mutable_classes
    - avoid_escaping_inner_quotes
    - avoid_field_initializers_in_const_classes
    # - avoid_final_parameters
    - avoid_function_literals_in_foreach_calls
    # - avoid_implementing_value_types
    - avoid_init_to_null
    - avoid_js_rounded_ints
    - avoid_multiple_declarations_per_line
    - avoid_null_checks_in_equality_operators
    - avoid_positional_boolean_parameters
    - avoid_print
    - avoid_private_typedef_functions
    - avoid_redundant_argument_values
    - avoid_relative_lib_imports
    - avoid_renaming_method_parameters
    - avoid_return_types_on_setters
    # - avoid_returning_null
    # - avoid_returning_null_for_future
    - avoid_returning_null_for_void
    # - avoid_returning_this
    - avoid_setters_without_getters
    - avoid_shadowing_type_parameters
    - avoid_single_cascade_in_expression_statements
    - avoid_slow_async_io
    # - avoid_type_to_string
    - avoid_types_as_parameter_names
    - avoid_types_on_closure_parameters
    - avoid_unnecessary_containers
    # - avoid_unstable_final_fields
    - avoid_unused_constructor_parameters
    - avoid_void_async
    - avoid_web_libraries_in_flutter
    - await_only_futures
    - camel_case_extensions
    - camel_case_types
    - cancel_subscriptions
    - cascade_invocations
    - cast_nullable_to_non_nullable
    - close_sinks
    - collection_methods_unrelated_type
    - combinators_ordering
    - comment_references
    - conditional_uri_does_not_exist
    - constant_identifier_names
    - control_flow_in_finally
    - curly_braces_in_flow_control_structures
    # - dangling_library_doc_comments
    - depend_on_referenced_packages
    - deprecated_consistency
    - deprecated_member_use_from_same_package
    # - diagnostic_describe_all_properties
    - directives_ordering
    - discarded_futures
    - do_not_use_environment
    - empty_catches
    - empty_constructor_bodies
    - empty_statements
    - eol_at_end_of_file
    - exhaustive_cases
    - file_names
    - flutter_style_todos
    - hash_and_equals
    - implementation_imports
    # - implicit_call_tearoffs
    # - implicit_reopen
    # - invalid_case_patterns
    # - iterable_contains_unrelated_type
    - join_return_with_assignment
    - leading_newlines_in_multiline_strings
    - library_annotations
    - library_names
    - library_prefixes
    - library_private_types_in_public_api
    # - lines_longer_than_80_chars
    # - list_remove_unrelated_type
    - literal_only_boolean_expressions
    - matching_super_parameters
    - missing_whitespace_between_adjacent_strings
    - no_adjacent_strings_in_list
    # - no_default_cases
    - no_duplicate_case_values
    - no_leading_underscores_for_library_prefixes
    - no_leading_underscores_for_local_identifiers
    - no_literal_bool_comparisons
    - no_logic_in_create_state
    - no_runtimeType_toString
    - no_self_assignments
    - no_wildcard_variable_uses
    - non_constant_identifier_names
    - noop_primitive_operations
    - null_check_on_nullable_type_parameter
    - null_closures
    # - omit_local_variable_types
    # - one_member_abstracts
    - only_throw_errors
    - overridden_fields
    - package_api_docs
    - package_names
    - package_prefixed_library_names
    - parameter_assignments
    - prefer_adjacent_string_concatenation: false
    - prefer_asserts_in_initializer_lists
    - prefer_asserts_with_message
    - prefer_collection_literals
    - prefer_conditional_assignment
    - prefer_const_constructors
    - prefer_const_constructors_in_immutables
    - prefer_const_declarations
    - prefer_const_literals_to_create_immutables
    - prefer_constructors_over_static_methods
    - prefer_contains
    # - prefer_double_quotes
    - prefer_expression_function_bodies
    - prefer_final_fields
    - prefer_final_in_for_each
    - prefer_final_locals
    # - prefer_final_parameters
    - prefer_for_elements_to_map_fromIterable
    # - prefer_foreach
    - prefer_function_declarations_over_variables
    - prefer_generic_function_type_aliases
    - prefer_if_elements_to_conditional_expressions
    - prefer_if_null_operators
    - prefer_initializing_formals
    - prefer_inlined_adds
    # - prefer_int_literals
    - prefer_interpolation_to_compose_strings
    - prefer_is_empty
    - prefer_is_not_empty
    - prefer_is_not_operator
    - prefer_iterable_whereType
    - prefer_mixin
    - prefer_null_aware_method_calls
    - prefer_null_aware_operators
    # - prefer_relative_imports
    - prefer_single_quotes
    - prefer_spread_collections
    - prefer_typing_uninitialized_variables
    - prefer_void_to_null
    - provide_deprecation_message
    - public_member_api_docs
    - recursive_getters
    - require_trailing_commas
    # - secure_pubspec_urls
    - sized_box_for_whitespace
    - sized_box_shrink_expand
    - slash_for_doc_comments
    - sort_child_properties_last
    # - sort_constructors_first
    - sort_pub_dependencies
    - sort_unnamed_constructors_first
    - test_types_in_equals
    - throw_in_finally
    - tighten_type_of_initializing_formals
    - type_annotate_public_apis
    - type_init_formals
    - type_literal_in_constant_pattern
    - unawaited_futures
    - unnecessary_await_in_return
    - unnecessary_brace_in_string_interps
    - unnecessary_breaks
    - unnecessary_const
    - unnecessary_constructor_name
    # - unnecessary_final
    - unnecessary_getters_setters
    - unnecessary_lambdas
    - unnecessary_late
    - unnecessary_library_directive
    - unnecessary_new
    - unnecessary_null_aware_assignments
    # - unnecessary_null_aware_operator_on_extension_on_nullable
    # - unnecessary_null_checks
    - unnecessary_null_in_if_null_operators
    - unnecessary_nullable_for_final_variable_declarations
    - unnecessary_overrides
    - unnecessary_parenthesis
    - unnecessary_raw_strings
    - unnecessary_statements
    - unnecessary_string_escapes
    - unnecessary_string_interpolations
    - unnecessary_this
    - unnecessary_to_list_in_spreads
    - unreachable_from_main
    - unrelated_type_equality_checks
    - unsafe_html
    - use_build_context_synchronously
    - use_colored_box
    - use_decorated_box
    - use_enums
    - use_full_hex_values_for_flutter_colors
    - use_function_type_syntax_for_parameters
    - use_if_null_to_convert_nulls_to_bools
    - use_is_even_rather_than_modulo
    - use_key_in_widget_constructors
    - use_late_for_private_fields_and_variables
    - use_named_constants
    - use_raw_strings
    - use_rethrow_when_possible
    # - use_setters_to_change_properties
    # - use_string_buffers
    # - use_string_in_part_of_directives
    - use_super_parameters
    # - use_test_throws_matchers
    - use_to_and_as_if_applicable
    - valid_regexps
    - void_checks
