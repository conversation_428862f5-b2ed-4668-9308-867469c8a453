plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProductionProperties = new Properties()
def keystoreProductionPropertiesFile = rootProject.file('keystore-production.properties')
if (keystoreProductionPropertiesFile.exists()) {
    keystoreProductionProperties.load(new FileInputStream(keystoreProductionPropertiesFile))
} else {
    println 'keystore-production.properties file is missing'
}

def keystorePreviewProperties = new Properties()
def keystorePreviewPropertiesFile = rootProject.file('keystore-preview.properties')
if (keystorePreviewPropertiesFile.exists()) {
    keystorePreviewProperties.load(new FileInputStream(keystorePreviewPropertiesFile))
} else {
    println 'keystore-preview.properties file is missing'
}

android {
    namespace "com.eltako.connectpreview"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.eltako.connectpreview"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdkVersion 24
        targetSdkVersion flutter.targetSdkVersion
        versionName flutterVersionName
    }

    flavorDimensions "default"

    productFlavors {
        create("production") {
            dimension = "default"
            applicationId = "com.eltako.connect"
            resValue "string", "app_name", "ELTAKO Connect"
            versionCode 80
            archivesBaseName = "eltako-connect-$flutterVersionName+$flutterVersionCode"
        }
        create("preview") {
            dimension = "default"
            applicationId = "com.eltako.connectpreview"
            resValue "string", "app_name", "ELTAKO Connect Preview"
            versionCode 22
            versionNameSuffix = " ($flutterVersionCode)-preview"
            archivesBaseName = "eltako-connect-$flutterVersionName+$flutterVersionCode"
        }
    }

    signingConfigs {
        production {
            keyAlias keystoreProductionProperties['keyAlias']
            keyPassword keystoreProductionProperties['keyPassword']
            storeFile keystoreProductionProperties['storeFile'] ? file(keystoreProductionProperties['storeFile']) : null
            storePassword keystoreProductionProperties['storePassword']
        }
        preview {
            keyAlias keystorePreviewProperties['keyAlias']
            keyPassword keystorePreviewProperties['keyPassword']
            storeFile keystorePreviewProperties['storeFile'] ? file(keystorePreviewProperties['storeFile']) : null
            storePassword keystorePreviewProperties['storePassword']
        }
    }

    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            productFlavors.production.signingConfig keystoreProductionPropertiesFile.exists() ? signingConfigs.production : signingConfigs.debug
            productFlavors.preview.signingConfig keystorePreviewPropertiesFile.exists() ? signingConfigs.preview : signingConfigs.debug
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "com.polidea.rxandroidble2:rxandroidble:1.16.0"
}
