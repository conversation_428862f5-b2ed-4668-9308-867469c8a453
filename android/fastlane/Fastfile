# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do

    before_all do
        require 'dotenv'
        Dotenv.load
    end

    # Global helper to load configuration values from the environment
    def get_build_config
        {
          flavor: ENV["APP_FLAVOR"] || "preview",
          scheme: (ENV["APP_SCHEMA"] || "Preview").capitalize,
          app_identifier: ENV["APP_IDENTIFIER_ANDROID"] || "com.eltako.connectpreview"
        }
    end

    desc "Build Flutter Android APK"
    lane :flutter_build do |options|
        type = options[:type] || "apk"

        config = get_build_config
        UI.message("*** Building Flutter app with flavor: #{config[:flavor]} ***")
        Dir.chdir("../..") do
          # Prepare Flutter project
          sh "flutter clean"
          sh "flutter pub get"
          # Build android app, the flavor is loaded from the environment
          sh("flutter", "build", type, "--release", "--flavor", config[:flavor])
        end
    end

    desc "Deploy AdHoc build"
    lane :adhoc do
        config = get_build_config

        flutter_build(type: "apk")

        UI.message("*** Building AdHoc APK with scheme: #{config[:scheme]} ***")

        gradle(
            task: "assemble",
            build_type: "Release",
            flavor: config[:scheme]
        )
    end

  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Push new Beta build to Google Play Store"
  lane :beta do
        config = get_build_config
        flutter_build(type: "appbundle")

        gradle(
            task: "assemble",
            build_type: "Release",
            flavor: config[:scheme]
        )
        upload_to_play_store(track: 'beta')
  end
end
