<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="fastlane.lanes">
    
    
    
      
      <testcase classname="fastlane.lanes" name="0: default_platform" time="0.000182">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="1: Switch to android flutter_build lane" time="6.5e-05">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="2: flutter clean" time="7.446592">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="3: flutter pub get" time="3.413894">
        
      </testcase>
    
      
      <testcase classname="fastlane.lanes" name="4: flutter build appbundle --release --flavor preview" time="45.443351">
        
          <failure message="/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/actions/actions_helper.rb:67:in &apos;Fastlane::Actions.execute_action&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/fast_file.rb:224:in &apos;Fastlane::FastFile.sh&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/fast_file.rb:216:in &apos;Fastlane::FastFile#sh&apos;&#10;Fastfile:46:in &apos;block (3 levels) in Fastlane::FastFile#parsing_binding&apos;&#10;Fastfile:41:in &apos;Dir.chdir&apos;&#10;Fastfile:41:in &apos;block (2 levels) in Fastlane::FastFile#parsing_binding&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/lane.rb:41:in &apos;Fastlane::Lane#call&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/runner.rb:204:in &apos;Fastlane::Runner#try_switch_to_lane&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/runner.rb:146:in &apos;Fastlane::Runner#trigger_action_by_name&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/fast_file.rb:159:in &apos;Fastlane::FastFile#method_missing&apos;&#10;Fastfile:73:in &apos;block (2 levels) in Fastlane::FastFile#parsing_binding&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/lane.rb:41:in &apos;Fastlane::Lane#call&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/runner.rb:49:in &apos;block in Fastlane::Runner#execute&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/runner.rb:45:in &apos;Dir.chdir&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/runner.rb:45:in &apos;Fastlane::Runner#execute&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/lane_manager.rb:46:in &apos;Fastlane::LaneManager.cruise_lane&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/command_line_handler.rb:34:in &apos;Fastlane::CommandLineHandler.handle&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/commands_generator.rb:110:in &apos;block (2 levels) in Fastlane::CommandsGenerator#run&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/commander-4.6.0/lib/commander/command.rb:187:in &apos;Commander::Command#call&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/commander-4.6.0/lib/commander/command.rb:157:in &apos;Commander::Command#run&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/commander-4.6.0/lib/commander/runner.rb:444:in &apos;Commander::Runner#run_active_command&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane_core/lib/fastlane_core/ui/fastlane_runner.rb:124:in &apos;Commander::Runner#run!&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/commander-4.6.0/lib/commander/delegates.rb:18:in &apos;Commander::Delegates#run!&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/commands_generator.rb:363:in &apos;Fastlane::CommandsGenerator#run&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/commands_generator.rb:43:in &apos;Fastlane::CommandsGenerator.start&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/fastlane/lib/fastlane/cli_tools_distributor.rb:123:in &apos;Fastlane::CLIToolsDistributor.take_off&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/gems/fastlane-2.227.0/bin/fastlane:23:in &apos;&lt;top (required)&gt;&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/bin/fastlane:25:in &apos;Kernel#load&apos;&#10;/opt/homebrew/Cellar/fastlane/2.227.0/libexec/bin/fastlane:25:in &apos;&lt;main&gt;&apos;&#10;&#10;Exit status of command &apos;flutter build appbundle --release --flavor preview&apos; was 1 instead of 0.&#10;&#10;Running Gradle task &apos;bundlePreviewRelease&apos;...                   &#10;warning: [options] source value 7 is obsolete and will be removed in a future release&#10;warning: [options] target value 7 is obsolete and will be removed in a future release&#10;warning: [options] To suppress warnings about obsolete options, use -Xlint:-options.&#10;Note: /Users/<USER>/.pub-cache/hosted/pub.dev/bluetooth_enable_fork-0.1.6/android/src/main/java/com/hui/bluetooth_enable/BluetoothEnablePlugin.java uses or overrides a deprecated API.&#10;Note: Recompile with -Xlint:deprecation for details.&#10;3 warnings&#10;Note: /Users/<USER>/.pub-cache/hosted/pub.dev/esp_provisioning_softap-1.0.3/android/src/main/java/de/petersen/nico/esp_provisioning_softap/EspSoftapProvisioningPlugin.java uses or overrides a deprecated API.&#10;Note: Recompile with -Xlint:deprecation for details.&#10;Note: Some input files use or override a deprecated API.&#10;Note: Recompile with -Xlint:deprecation for details.&#10;Note: Some input files use or override a deprecated API.&#10;Note: Recompile with -Xlint:deprecation for details.&#10;Font asset &quot;MaterialIcons-Regular.otf&quot; was tree-shaken, reducing it from 1645184 to 3204 bytes (99.8% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app.&#10;Font asset &quot;CupertinoIcons.ttf&quot; was tree-shaken, reducing it from 257628 to 848 bytes (99.7% reduction). Tree-shaking can be disabled by providing the --no-tree-shake-icons flag when building your app.&#10;Note: Some input files use or override a deprecated API.&#10;Note: Recompile with -Xlint:deprecation for details.&#10;Running Gradle task &apos;bundlePreviewRelease&apos;...                      44,4s&#10;Gradle build failed to produce an .aab file. It&apos;s likely that this file was generated under /Users/<USER>/Development/EltakoConnect/build, but the tool couldn&apos;t find it.&#10;" />
        
      </testcase>
    
  </testsuite>
</testsuites>
