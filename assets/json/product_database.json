{"schemaVersion": "0.0.1", "dataVersion": "0.5.1", "products": [{"productGuid": "3ccc1548-d935-43bf-aa12-c99b1a48da20", "name": "FBH55SB", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "illumination", "characteristics": [{"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 510, "step": 2, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "08857dd2-de5b-11eb-ba80-0242ac130004", "name": "FBH65SB", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "illumination", "characteristics": [{"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 510, "step": 2, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "006cbc7c-332a-4ca6-9bd5-12ceaa2fff06", "name": "FBHF65SB", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "illumination", "characteristics": [{"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 510, "step": 2, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "5910d88c-fde7-45af-a485-b422dd64d12a", "name": "FABH65S", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motionIllumination", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 2000, "step": 7.84, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "18631a8b-f822-49c5-9bd0-8076a8ebf1a7", "name": "FBH65/12V DC", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motionIllumination", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 2000, "step": 7.84, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "715be588-4d8a-4ec1-ba4e-4a1623de4d5a", "name": "FBH65S/12V DC", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motionIllumination", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 2000, "step": 7.84, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "1d3b2669-e401-411c-9469-e38adaedc268", "name": "FB55B", "category": "sensor", "profile": "motion", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "3d6d5f18-0e9c-4b75-9977-884d591aa111", "name": "FB65B", "category": "sensor", "profile": "motion", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "92ec3705-e209-4117-9efb-e0cfb2d116bb", "name": "FTKB", "category": "sensor", "profile": "windowContact", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}, {"identifier": "batteryVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}]}]}, {"productGuid": "6773c184-a4a5-4cbb-bc19-479e4fcfc491", "name": "FTKB_HG", "category": "sensor", "profile": "windowContact", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}, {"index": 2, "key": "tilted"}]}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}, {"identifier": "vibration", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "f12720bc-41e6-4c2c-b4e5-3b485347dc1c", "name": "FTKB_RW", "category": "sensor", "profile": "windowContact", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}, {"identifier": "batteryVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}]}]}, {"productGuid": "541d4e82-5904-4a39-abd8-c5f57f9bbdee", "name": "FTKE", "category": "sensor", "profile": "windowContact", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "beb2f4b7-b0d9-4b18-9772-47f15e62584f", "name": "FFTE", "category": "sensor", "profile": "windowContact", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "07f73245-b035-40ad-a403-f8581f067ef6", "name": "FFG7B", "category": "sensor", "profile": "windowHandle", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}, {"index": 2, "key": "tilted"}]}, {"identifier": "batteryVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}]}]}, {"productGuid": "38ff4e3d-ad7f-4ba0-8511-5240ad2ed4e7", "name": "FFGB_HG", "category": "sensor", "profile": "windowHandle", "channels": 1, "features": [{"type": "contact", "characteristics": [{"identifier": "contact", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}, {"index": 2, "key": "tilted"}]}, {"identifier": "locked", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "unlocked", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "unlocked"}, {"index": 1, "key": "locked"}]}, {"identifier": "batteryVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5, "step": 0.019, "unit": "V"}, {"identifier": "vibration", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "6f6d379f-4c6f-45ed-b881-671ed7ea8970", "name": "WiredInput", "category": "sensor", "profile": "input", "channels": 1, "features": [{"type": "input", "characteristics": [{"identifier": "input", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "52d055e8-4bf4-4583-a9d8-5672bb36dbcf", "name": "WiredInput", "category": "sensor", "profile": "input", "channels": 3, "features": [{"type": "input", "characteristics": [{"identifier": "inputChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "input", "characteristics": [{"identifier": "inputChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "input", "characteristics": [{"identifier": "inputBridged", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "14ea174a-53b4-4bba-8596-0a9b09f1d681", "name": "F2FT65B", "category": "sensor", "profile": "button", "channels": 2, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "05a148f7-61f0-47c9-bd6c-e6be5e54214b", "name": "F4FT65B", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "d446f36a-1cd9-4107-9abc-d954d13a33ca", "name": "FTE215", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "3ffb440a-d4ba-4d75-b97b-fe4e211b36dc", "name": "SingleButton", "category": "sensor", "profile": "button", "channels": 1, "features": [{"type": "singleButton", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "34f8d1f0-2af1-47b7-af13-db670d3ceee0", "name": "DualButton", "category": "sensor", "profile": "button", "channels": 2, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "d96c6b44-149e-4abf-a5fc-6685e866f45e", "name": "QuadButton", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "aee6772a-2aff-475f-a78d-2297202e3ad6", "name": "OctoButton", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker2", "characteristics": [{"identifier": "buttonChannel4", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel5", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker3", "characteristics": [{"identifier": "buttonChannel6", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel7", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "3c6b2967-b49b-416d-92cc-17c1ca808001", "name": "F6-01-01", "category": "sensor", "profile": "button", "channels": 1, "features": [{"type": "singleButton", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "47d45526-8144-452a-baf7-d672f80dd7a4", "name": "Eltako Wireless 2-way pushbutton", "category": "sensor", "profile": "button", "channels": 2, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "656829b2-4120-4c46-8e6b-221e5de200af", "name": "F6-02-01", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "9b77ee39-3aa6-4481-bc19-89d403a848ce", "name": "F6-02-02", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "60f928bb-103f-41aa-9e32-1cf4fbbdf3f6", "name": "F6-02-03", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "0f156fd6-681b-4bca-95de-3e5c57fd0e91", "name": "F6-02-04", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "f0881bc2-aee0-4b95-b5d5-a99bfbcfd7d8", "name": "F6-03-01", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker2", "characteristics": [{"identifier": "buttonChannel4", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel5", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker3", "characteristics": [{"identifier": "buttonChannel6", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel7", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "1795095c-e071-401d-a06e-3a2305274c6c", "name": "F6-03-02", "category": "sensor", "profile": "button", "channels": 4, "features": [{"type": "rocker0", "characteristics": [{"identifier": "buttonChannel0", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel1", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker1", "characteristics": [{"identifier": "buttonChannel2", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel3", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker2", "characteristics": [{"identifier": "buttonChannel4", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel5", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "rocker3", "characteristics": [{"identifier": "buttonChannel6", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}, {"identifier": "buttonChannel7", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "never", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}]}, {"productGuid": "fee3e0b3-a465-4778-8e75-38c6f71acdd0", "name": "FSR64NP/110-240V", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "78a11d26-0485-4ee2-b4d5-39ba4a422bd4", "name": "<PERSON><PERSON>-PM-EU", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "7b7c3f50-75a3-482b-b437-0ecb0671fc53", "name": "F2SR64NP/110-240V", "category": "actuator", "profile": "relay", "channels": 2, "features": [{"type": "relay", "characteristics": [{"identifier": "relayChannel1", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}, {"type": "power", "characteristics": [{"identifier": "powerChannel1", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}, {"type": "relay", "characteristics": [{"identifier": "relayChannel2", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}, {"type": "power", "characteristics": [{"identifier": "powerChannel2", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "*************-4947-a678-a47ccf84b982", "name": "FSR64PF/110-240V", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}]}, {"productGuid": "f4c6539f-807c-4936-8063-1e307c7795e7", "name": "ESR62NP-IP/110-240V", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}, {"type": "power", "characteristics": [{"identifier": "powerChannel1", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "ab9075a6-3bcf-428a-bb83-c65cc6af63a3", "name": "ESR62PF-IP/110-240V", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}]}, {"productGuid": "57bb9236-7af8-43dc-a8fe-79ac19b2029d", "name": "Relay-16A-EU", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}]}, {"productGuid": "776b4b10-1eb8-4651-8088-8c5d12fb8ed6", "name": "FUD64NPN/110-240V", "category": "actuator", "profile": "dimmer", "channels": 1, "features": [{"type": "dimmer", "characteristics": [{"identifier": "targetBrightness", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "edgeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "leading<PERSON>dge"}]}, {"identifier": "dimCurve", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}]}, {"productGuid": "8077e5f1-5e9c-44de-83c7-fdd7f2b8e090", "name": "EUD62NPN-IP/110-240V", "category": "actuator", "profile": "dimmer", "channels": 1, "features": [{"type": "dimmer", "characteristics": [{"identifier": "targetBrightness", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "edgeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "leading<PERSON>dge"}]}, {"identifier": "dimCurve", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}]}, {"productGuid": "82f2faba-9645-4de7-9083-0df9156cb0cc", "name": "EUD62NPN-IPM/110-240V", "category": "actuator", "profile": "dimmer", "channels": 1, "features": [{"type": "dimmer", "characteristics": [{"identifier": "targetBrightness", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "edgeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "leading<PERSON>dge"}]}, {"identifier": "dimCurve", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}]}, {"productGuid": "81262fd6-92cd-48bf-b537-155a952c11d2", "name": "Dimmer-300W-EU", "category": "actuator", "profile": "dimmer", "channels": 1, "features": [{"type": "dimmer", "characteristics": [{"identifier": "targetBrightness", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "edgeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "leading<PERSON>dge"}]}, {"identifier": "dimCurve", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}]}, {"productGuid": "6f382433-a1c6-491c-acb9-ce42913831d8", "name": "FSB64/12-36VDC", "category": "actuator", "profile": "motor", "channels": 1, "features": [{"type": "shader", "characteristics": [{"identifier": "targetPosition", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentPosition", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "maxRuntime", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "120000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "runtimeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "manual"}, {"index": 2, "key": "reserved"}, {"index": 3, "key": "lcdDisplay"}]}, {"identifier": "runtime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "16000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "offset", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 15000, "step": 1000, "unit": "ms"}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "846b235d-2fc7-4587-8013-ee282d4eaa7d", "name": "Shade-PM-EU", "category": "actuator", "profile": "motor", "channels": 1, "features": [{"type": "shader", "characteristics": [{"identifier": "targetPosition", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentPosition", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "maxRuntime", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "120000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "runtimeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "manual"}, {"index": 2, "key": "reserved"}, {"index": 3, "key": "lcdDisplay"}]}, {"identifier": "runtime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "16000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "offset", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 15000, "step": 1000, "unit": "ms"}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "7cde6ffb-3425-4407-9a94-c91319daff81", "name": "FSB64NP/110-240V", "category": "actuator", "profile": "motor", "channels": 1, "features": [{"type": "shader", "characteristics": [{"identifier": "targetPosition", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentPosition", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "maxRuntime", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "120000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "runtimeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "manual"}, {"index": 2, "key": "reserved"}, {"index": 3, "key": "lcdDisplay"}]}, {"identifier": "runtime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "16000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "offset", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 15000, "step": 1000, "unit": "ms"}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "0b898240-e820-4a06-879a-fccf35260a35", "name": "ESB62NP-IP/110-240V", "category": "actuator", "profile": "motor", "channels": 1, "features": [{"type": "shader", "characteristics": [{"identifier": "targetPosition", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "100.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentPosition", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "100.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "maxRuntime", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "120000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "runtimeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "manual"}, {"index": 2, "key": "reserved"}, {"index": 3, "key": "lcdDisplay"}]}, {"identifier": "runtime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "16000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "offset", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 15000, "step": 1000, "unit": "ms"}, {"identifier": "movementDirection", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "asPrintedOnCase", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "asPrintedOnCase"}, {"index": 1, "key": "inverted"}]}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "57860dd1-9cec-4ed4-bbb5-a32b5a284533", "name": "FSR64/12-36VDC", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}]}, {"productGuid": "27592acc-1839-48d1-b031-1ee09c68d2f1", "name": "F2LD/12-30VDC", "category": "actuator", "profile": "dimmer", "channels": 2, "features": [{"type": "dimmer", "characteristics": [{"identifier": "targetBrightnessChannel1", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightnessChannel1", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightnessChannel1", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightnessChannel1", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightnessChannel1", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "dimCurveChannel1", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}, {"type": "dimmer", "characteristics": [{"identifier": "targetBrightnessChannel2", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightnessChannel2", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightnessChannel2", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightnessChannel2", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightnessChannel2", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "dimCurveChannel2", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}, {"type": "undefined", "characteristics": [{"identifier": "channelMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "2Channel", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "1Channel"}, {"index": 1, "key": "2Channel"}, {"index": 2, "key": "tunableWhite"}]}]}]}, {"productGuid": "ade276c9-a9a2-41cd-9114-631b8d1177b4", "name": "ESR64NP-IPM", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}, {"type": "power", "characteristics": [{"identifier": "powerChannel1", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "4893e06b-448c-40d5-b426-42e2e6b73656", "name": "ESR64PF-IPM", "category": "actuator", "profile": "relay", "channels": 1, "features": [{"type": "relay", "characteristics": [{"identifier": "relay", "type": "enumeration", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "off", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "off"}, {"index": 1, "key": "on"}]}]}]}, {"productGuid": "b7580625-4e9f-473b-afe4-29e0c073744a", "name": "EUD64NPN-IPM", "category": "actuator", "profile": "dimmer", "channels": 1, "features": [{"type": "dimmer", "characteristics": [{"identifier": "targetBrightness", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "lastBrightness", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 1, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "minimumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "1", "underlyingDataType": "int", "minimum": 1, "maximum": 50, "step": 1, "unit": "%"}, {"identifier": "maximumBrightness", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "100", "underlyingDataType": "int", "minimum": 50, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "edgeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "leading<PERSON>dge"}]}, {"identifier": "dimCurve", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "LC1", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "LC1"}, {"index": 1, "key": "LC2"}, {"index": 2, "key": "LC3"}, {"index": 3, "key": "linear"}]}]}]}, {"productGuid": "2b046bb5-6bcd-4d41-9c5c-01bac63ebc1d", "name": "ESB64NP-IPM", "category": "actuator", "profile": "motor", "channels": 1, "features": [{"type": "shader", "characteristics": [{"identifier": "targetPosition", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "100.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentPosition", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "100.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "maxRuntime", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "120000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "runtimeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "manual"}, {"index": 2, "key": "reserved"}, {"index": 3, "key": "lcdDisplay"}]}, {"identifier": "runtime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "16000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "offset", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 15000, "step": 1000, "unit": "ms"}, {"identifier": "enableTilt", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "disabled", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "disabled"}, {"index": 1, "key": "enabled"}]}, {"identifier": "tiltRuntime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "2000.0", "underlyingDataType": "int", "minimum": 0, "maximum": 5000, "step": 100, "unit": "ms"}, {"identifier": "currentTilt", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "targetTilt", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "movementDirection", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "asPrintedOnCase", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "asPrintedOnCase"}, {"index": 1, "key": "inverted"}]}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "62077604-f0ec-4ecd-ad57-2031508361cf", "name": "VirtualShade", "category": "actuator", "profile": "motor", "channels": 1, "features": [{"type": "shader", "characteristics": [{"identifier": "targetPosition", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "100.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "currentPosition", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "100.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "maxRuntime", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "120000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "runtimeMode", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "auto", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "auto"}, {"index": 1, "key": "manual"}, {"index": 2, "key": "reserved"}, {"index": 3, "key": "lcdDisplay"}]}, {"identifier": "runtime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "16000.0", "underlyingDataType": "int", "minimum": 1000, "maximum": 900000, "step": 20, "unit": "ms"}, {"identifier": "offset", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 15000, "step": 1000, "unit": "ms"}, {"identifier": "enableTilt", "type": "enumeration", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "disabled", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "disabled"}, {"index": 1, "key": "enabled"}]}, {"identifier": "tiltRuntime", "type": "number", "category": "setting", "readOnly": false, "storageFrequency": "always", "defaultValue": "2000.0", "underlyingDataType": "int", "minimum": 0, "maximum": 5000, "step": 100, "unit": "ms"}, {"identifier": "currentTilt", "type": "number", "category": "info", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}, {"identifier": "targetTilt", "type": "number", "category": "function", "readOnly": false, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": 0, "maximum": 100, "step": 1, "unit": "%"}]}, {"type": "power", "characteristics": [{"identifier": "power", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "int", "minimum": -2147483648, "maximum": 2147483647, "step": 1, "unit": "mW"}]}]}, {"productGuid": "3ccc1548-d935-43bf-aa12-c99b1a48da20", "name": "FBH55SB", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "illumination", "characteristics": [{"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 510, "step": 2, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "3ccc1548-d935-43bf-aa12-c99b1a48da20", "name": "FBH55SB", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "illumination", "characteristics": [{"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 510, "step": 2, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}, {"productGuid": "3ccc1548-d935-43bf-aa12-c99b1a48da20", "name": "FBH55SB", "category": "sensor", "profile": "motionIllumination", "channels": 1, "features": [{"type": "motion", "characteristics": [{"identifier": "motionDetected", "type": "enumeration", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "opened", "underlyingDataType": "int", "enumerations": [{"index": 0, "key": "opened"}, {"index": 1, "key": "closed"}]}]}, {"type": "illumination", "characteristics": [{"identifier": "illumination", "type": "number", "category": "function", "readOnly": true, "storageFrequency": "always", "defaultValue": "0", "underlyingDataType": "int", "minimum": 0, "maximum": 510, "step": 2, "unit": "Lx"}, {"identifier": "supplyVoltage", "type": "number", "category": "info", "readOnly": true, "storageFrequency": "always", "defaultValue": "0.0", "underlyingDataType": "float", "minimum": 0, "maximum": 5.1, "step": 0.02, "unit": "V"}]}]}]}