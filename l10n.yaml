arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
synthetic-package: false
untranslated-messages-file: lib/l10n/untranslated.txt
nullable-getter: false
# By default, the tool generates the supported locales list in alphabetical order (German). Use this flag to default to a different locale:
preferred-supported-locales: [ en ]
# Run "pub get" to generate Strings from .arb files