//
//  i_device.dart
//  EltakoConnect
//
//  Created by <PERSON> on 11.12.23.
//  Copyright © 2023 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/connection/i_connection.dart';
import 'package:eltako_connect/device/i_wifi_device.dart';
import 'package:eltako_connect/enumeration/authorization_state.dart';
import 'package:eltako_connect/enumeration/connection_type.dart';
import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/manager/i_connection_manager.dart';
import 'package:eltako_connect/manager/login/i_log_in_data.dart';
import 'package:eltako_connect/model/authorization_info.dart';
import 'package:eltako_connect/model/device_info.dart';
import 'package:eltako_connect/model/device_login.dart';
import 'package:elta<PERSON>_connect/model/progress_data.dart';
import 'package:eltako_connect/model/sensor/sensor_config_data.dart';
import 'package:eltako_connect/model/service_category.dart';
import 'package:eltako_connect/model/sub_device/sub_device/dimmer.dart';
import 'package:eltako_connect/model/sub_device/sub_device/dimmer_config_data.dart';
import 'package:eltako_connect/model/sub_device/sub_device/eltako_shader.dart';
import 'package:eltako_connect/model/sub_device/sub_device/shader_config_data.dart';
import 'package:eltako_connect/model/sub_device/sub_device/wired_input.dart';
import 'package:eltako_connect/model/sub_device/sub_device/wired_input_config_data.dart';
import 'package:eltako_connect/model/version_number.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/config_service/config_data.dart';
import 'package:eltako_connect/service/dimmer_service/dimmer_data.dart';
import 'package:eltako_connect/service/i_service.dart';
import 'package:eltako_connect/service/i_service_data.dart';
import 'package:eltako_connect/service/shader_service/i_shader_service.dart';
import 'package:eltako_connect/service/shader_service/shader_data.dart';
import 'package:eltako_connect/service/wired_input_service/wired_input_data.dart';
import 'package:eltako_connect/storage/secure_storage/section/device_login_storage.dart';
import 'package:eltako_connect/storage/secure_storage/secure_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';

/// Interface for devices
abstract class IDevice with ChangeNotifier {
  // region [PROPERTIES]

  /// Show hidden services (for debugging purposes)
  bool get showHiddenServices => kDebugMode && true;

  /// Connection
  final IConnection connection;

  /// Connection manager
  final IConnectionManager connectionManager;

  /// Image path
  final String imagePath;

  /// Device information
  DeviceInfo deviceInfo;

  /// Authorization information
  AuthorizationInfo get authorizationInfo;

  /// Icon
  final dynamic icon;

  /// Value notifier for dump progress
  ValueNotifier<ProgressData> dumpProgress;

  /// Flag for canceling all running operations
  bool cancelAllOperations;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// List of services (categorized)
  List<ServiceCategory> get serviceCategories;

  /// Flat list of services
  List<IService> get services => serviceCategories.expand((c) => c.services).toList();

  /// Authorization state (only for devices with authorization process handled by the app)
  AuthorizationState get authorizationState {
    switch (connection.type) {
      case ConnectionType.ble:
        // BLE connection is handled by operating system
        return AuthorizationState.unknown;
      case ConnectionType.wifi:
        // Return authorization state for wifi devices
        return tryCast<IWifiDevice>()?.authorizationInfo.authorizationState ?? AuthorizationState.unknown;
      case ConnectionType.bleDemo:
      case ConnectionType.wifiDemo:
        // Demo devices are always authorized
        return AuthorizationState.authorized;
      case ConnectionType.unknown:
        // Unknown connection type
        return AuthorizationState.unknown;
    }
  }

  /// Firmware version
  VersionNumber? get firmwareVersion => VersionNumber.fromString(deviceInfo.softwareVersion);

  /// Tag for logging
  String get tag => '[${deviceInfo.tag}]';

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  IDevice({
    required this.connection,
    required this.connectionManager,
    required this.imagePath,
    required this.deviceInfo,
    required this.icon,
    this.cancelAllOperations = false,
  }) : dumpProgress = ProgressData.empty().obs();

  /// Authorize device
  Future<VoidResult> authorize();

  /// Connect to device
  Future<VoidResult> connect();

  /// Triggered after device is connected
  void onConnected() {}

  /// Disconnect from device
  Future<VoidResult> disconnect();

  /// Set login [data]
  Future<VoidResult> login(ILoginData data) async {
    // Check login data
    if (data.loginType.key != authorizationInfo.logInType.key) {
      return const VoidResult.error(message: "Login type doesn't match", error: LocalizedError.loginFailed);
    }

    // Save login data
    authorizationInfo.loginData = data;

    // Check if login data should be saved
    if (data.saveLoginData) {
      for (final entry in data.toMap().entries) {
        // Create device login data
        final deviceLogin = DeviceLogin(
          identifier: deviceInfo.identifier,
          connectionType: connection.type,
          loginType: data.loginType,
          key: entry.key,
          value: entry.value?.toString() ?? '',
        );

        // Save device login
        await GetIt.I.get<SecureStorage>().saveDeviceLogin(deviceLogin);
      }
    }

    // Authorize
    return authorize();
  }

  /// Set demo data
  void setDemoData(ConfigData data, {String? name, String? identifier, String? version}) {
    // Update device info
    final updatedInfo = data.deviceInfo;
    updatedInfo
      ..name = name ?? updatedInfo.name
      ..identifier = identifier ?? updatedInfo.identifier
      ..softwareVersion = version ?? updatedInfo.softwareVersion;
    deviceInfo = updatedInfo;

    // Update services
    for (var index = 0; index < services.length; index++) {
      for (final serviceConfig in data.serviceData) {
        if (serviceConfig?.key == services[index].key || serviceConfig?.key.key == '${services[index].key.key}_demo') {
          // Check for specific service data
          if (_checkForDimmerConfigData(serviceConfig, index: index)) break;
          if (_checkForShaderConfigData(serviceConfig, index: index)) break;
          // if (_checkForFirmwareUpdateConfigData(serviceConfig, index: index)) break;
          if (_checkForWiredInputConfigData(serviceConfig, index: index)) break;
          if (_checkForSensorDemoData(serviceConfig, index: index)) break;

          // Set data
          services[index].data = serviceConfig;
          break;
        }
      }
    }
  }

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Check for dimmer config data and convert and save it
  bool _checkForDimmerConfigData(IServiceData? data, {required int index}) {
    // Check data
    if (data is! DimmerConfigData) return false;

    // Create dimmer
    final dimmer = Dimmer.forDemo(
      name: data.name,
      edgeMode: data.edgeMode,
      dimCurve: data.channels.first.dimCurve,
      minBrightness: data.channels.first.minBrightness,
      maxBrightness: data.channels.first.maxBrightness,
    );
    if (dimmer == null) return false;

    // Set data
    services[index].data = DimmerData(dimmer: dimmer);
    return true;
  }

  // /// Check for firmware update config data and convert and save it
  // bool _checkForFirmwareUpdateConfigData(IServiceData? data, {required int index}) {
  //   // Check data
  //   if (data is! FirmwareUpdateConfigData) return false;
  //
  //   // Set data
  //   services[index].data = FirmwareUpdateData();
  //   return true;
  // }

  /// Check for wired input config data and convert and save it
  bool _checkForWiredInputConfigData(IServiceData? data, {required int index}) {
    // Check data
    if (data is! WiredInputConfigData) return false;

    // Create wired input
    final wiredInput =
        services.firstOfTypeOrNull<IShaderService>() != null
            ? WiredInput.forDemoShader(name: data.name, logic: data.configuration)
            : WiredInput.forDemoRelay(name: data.name, logic: data.configuration);
    if (wiredInput == null) return false;

    // Set data
    services[index].data = WiredInputData(wiredInput: wiredInput);
    return true;
  }

  /// Check for shader config data and convert and save it
  bool _checkForShaderConfigData(IServiceData? data, {required int index}) {
    // Check data
    if (data is! ShaderConfigData) return false;

    // Create shader
    final shader = EltakoShader.forDemo(
      configureAsEsb62: deviceInfo.deviceType == DeviceType.esb62ip,
      name: data.name,
      runtimeMode: data.runtimeMode,
      runtime: data.runtime,
      runtimeOffset: data.runtimeOffset,
      maxRuntime: data.maxRuntime,
      power: data.power,
      currentPosition: data.currentPosition,
      targetPosition: data.targetPosition,
      currentTilt: data.currentTilt,
      targetTilt: data.targetTilt,
      tiltMode: data.tiltMode,
      tiltRuntime: data.tiltRuntime,
      movementDirection: data.movementDirection,
    );
    if (shader == null) return false;

    // Set data
    services[index].data = ShaderData(shader: shader);
    return true;
  }

  /// Check for sensor demo data and convert and save it
  bool _checkForSensorDemoData(IServiceData? data, {required int index}) {
    // Check data
    if (data is! SensorConfigData) return false;

    // Set data
    services[index].data = data.toSensorData();
    return true;
  }

  // endregion
}
