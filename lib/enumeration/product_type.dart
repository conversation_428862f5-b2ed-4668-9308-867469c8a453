//
//  product_type.dart
//  EltakoConnect
//
//  Created by <PERSON> on 27.02.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/extension/list_extension.dart';

/// Enumeration for product types
enum ProductType {
  // region [CASES]

  /// Push button
  button,

  /// Dimmer
  dimmer,

  /// Input
  input,

  /// Motion
  motion,

  /// Motion and illumination
  motionIllumination,

  /// Motor
  motor,

  /// Relay
  relay,

  /// Window contact
  windowContact,

  /// Window handle
  windowHandle,

  /// Unknown
  unknown;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Name
  String get name {
    switch (this) {
      case ProductType.button:
        return 'Push button';
      case ProductType.dimmer:
        return 'Dimmer';
      case ProductType.input:
        return 'Wired input';
      case ProductType.motion:
        return 'Motion';
      case ProductType.motionIllumination:
        return 'Motion and illumination';
      case ProductType.motor:
        return 'Motor';
      case ProductType.relay:
        return 'Relay';
      case ProductType.windowContact:
        return 'Window contact';
      case ProductType.windowHandle:
        return 'Window handle';
      case ProductType.unknown:
        return 'Unknown';
    }
  }

  /// Coding key
  String get key {
    switch (this) {
      case ProductType.button:
        return 'button';
      case ProductType.dimmer:
        return 'dimmer';
      case ProductType.input:
        return 'input';
      case ProductType.motion:
        return 'motion';
      case ProductType.motionIllumination:
        return 'motion_illumination';
      case ProductType.motor:
        return 'motor';
      case ProductType.relay:
        return 'relay';
      case ProductType.windowContact:
        return 'window_contact';
      case ProductType.windowHandle:
        return 'window_handle';
      case ProductType.unknown:
        return 'unknown';
    }
  }

  /// Eltako key
  String get eltakoKey {
    switch (this) {
      case ProductType.button:
        return 'button';
      case ProductType.dimmer:
        return 'dimmer';
      case ProductType.input:
        return 'input';
      case ProductType.motion:
        return 'motion';
      case ProductType.motionIllumination:
        return 'motionIllumination';
      case ProductType.motor:
        return 'motor';
      case ProductType.relay:
        return 'relay';
      case ProductType.windowContact:
        return 'windowContact';
      case ProductType.windowHandle:
        return 'windowHandle';
      case ProductType.unknown:
        return '';
    }
  }

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Get product type from [key]
  static ProductType? fromKey(String key) => ProductType.values.firstWhereOrNull((t) => t.key == key);

  /// Get product type from Eltako [key]
  static ProductType fromEltakoKey(String key) =>
      ProductType.values.firstWhereOrNull((t) => t.eltakoKey == key) ?? ProductType.unknown;

  @override
  String toString() => name;

  // endregion
}
