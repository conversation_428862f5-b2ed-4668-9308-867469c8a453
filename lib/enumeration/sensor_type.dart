//
//  sensor_type.dart
//  EltakoConnect
//
//  Created by <PERSON> on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_type_category.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/product/product_info.dart';

/// Sensor Type, defines the groups of sensors (analog to [DeviceType])
enum SensorType {
  // region [CASES]

  /// Wired button 1 way
  wiredButton1Way,

  /// Wired button 2 way
  wiredButton2Way,

  /// EnOcean Button 1 way
  enOceanButton1Way,

  /// EnOcean Button 2 way
  enOceanButton2Way,

  /// EnOcean Button 4 way
  enOceanButton4Way,

  /// Motion Sensor
  motionSensor,

  /// Unknown
  unknown;

  // endregion

  // region [CONSTANTS]

  /// Selectable sensor types
  static const List<SensorType> selectable = [
    SensorType.enOceanButton1Way,
    SensorType.enOceanButton2Way,
    SensorType.enOceanButton4Way,
    SensorType.wiredButton1Way,
    SensorType.wiredButton2Way,
  ];

  /// 1 way button product id
  static const String button1WayProductId = '3c6b2967-b49b-416d-92cc-17c1ca808001';

  /// 2 way button product id
  static const String button2WayProductId = '47d45526-8144-452a-baf7-d672f80dd7a4';

  /// 4 way button product id
  static const String button4WayProductId = '656829b2-4120-4c46-8e6b-221e5de200af';

  /// Wired 1 way input sensor product id (single channel)
  static const String wiredInput1WayProductId = '6f6d379f-4c6f-45ed-b881-671ed7ea8970';

  /// Wired 2 way input sensor product id
  static const String wiredInput2WayProductId = '52d055e8-4bf4-4583-a9d8-5672bb36dbcf';

  // endregion

  /// ID
  String get id {
    switch (this) {
      case SensorType.wiredButton1Way:
        return wiredInput1WayProductId;
      case SensorType.wiredButton2Way:
        return wiredInput2WayProductId;
      case SensorType.enOceanButton1Way:
        return button1WayProductId;
      case SensorType.enOceanButton2Way:
        return button2WayProductId;
      case SensorType.enOceanButton4Way:
        return button4WayProductId;
      case SensorType.motionSensor:
      default:
        return 'unknown';
    }
  }

  /// Type name
  String get name {
    switch (this) {
      case SensorType.wiredButton1Way:
        return 'Wired Button 1 way';
      case SensorType.wiredButton2Way:
        return 'Wired Button 2 way';
      case SensorType.enOceanButton1Way:
        return 'EnOcean Button 1 way';
      case SensorType.enOceanButton2Way:
        return 'EnOcean Button 2 way';
      case SensorType.enOceanButton4Way:
        return 'EnOcean Button 4 way';
      case SensorType.motionSensor:
        return 'Motion Sensor';
      default:
        return 'Unknown';
    }
  }

  /// Type name
  String get localizedName {
    switch (this) {
      case SensorType.wiredButton1Way:
      case SensorType.wiredButton2Way:
        return l10n.wiredButton;
      case SensorType.enOceanButton1Way:
        return l10n.button1Way;
      case SensorType.enOceanButton2Way:
        return l10n.button2Way;
      case SensorType.enOceanButton4Way:
        return l10n.button4Way;
      case SensorType.motionSensor:
        return l10n.generalTextMotionsensor;
      case SensorType.unknown:
        return l10n.timerListitemUnknown;
    }
  }

  /// Telegram
  String get telegram {
    switch (this) {
      case SensorType.enOceanButton1Way:
        return 'F6-01-01';
      case SensorType.enOceanButton2Way:
      case SensorType.enOceanButton4Way:
        return 'F6-02-01';
      case SensorType.wiredButton1Way:
      case SensorType.wiredButton2Way:
      case SensorType.motionSensor:
      case SensorType.unknown:
        return l10n.timerListitemUnknown;
    }
  }

  /// Image
  dynamic get imagePath {
    switch (this) {
      case SensorType.wiredButton1Way:
      case SensorType.wiredButton2Way:
        return Assets.images.sensors.button1way.path;
      case SensorType.enOceanButton1Way:
        return Assets.images.sensors.button1way.path;
      case SensorType.enOceanButton2Way:
        return Assets.images.sensors.button2way.path;
      case SensorType.enOceanButton4Way:
        return Assets.images.sensors.button4way.path;
      case SensorType.motionSensor:
      case SensorType.unknown:
        return Assets.icons.devices.outline;
    }
  }

  /// Icon
  dynamic get icon {
    switch (this) {
      case SensorType.wiredButton1Way:
      case SensorType.wiredButton2Way:
        return Assets.icons.lightSwitch.circle;
      case SensorType.enOceanButton1Way:
        return Assets.icons.singleswitch.circle;
      case SensorType.enOceanButton2Way:
        return Assets.icons.doubleswitch.circle;
      case SensorType.enOceanButton4Way:
        return Assets.icons.multiswitch.circle;
      case SensorType.motionSensor:
        return Assets.icons.motionsensor.circle;
      case SensorType.unknown:
        return Assets.icons.devices.circle;
    }
  }

  /// Checks if the sensor type matches any of the given products
  bool matchesAny(List<ProductInfo> products) {
    bool hasProduct(String productId) => products.any((element) => element.id == productId);

    switch (this) {
      case SensorType.enOceanButton1Way:
        return hasProduct(button1WayProductId);
      case SensorType.enOceanButton2Way:
        return hasProduct(button2WayProductId);
      case SensorType.enOceanButton4Way:
        return hasProduct(button4WayProductId);
      default:
        return false;
    }
  }

  // region [CATEGORY]

  /// Category
  SensorTypeCategory get category {
    switch (this) {
      case SensorType.wiredButton1Way:
      case SensorType.wiredButton2Way:
        return SensorTypeCategory.wiredButton;
      case SensorType.enOceanButton1Way:
      case SensorType.enOceanButton2Way:
      case SensorType.enOceanButton4Way:
        return SensorTypeCategory.enOceanButton;
      case SensorType.motionSensor:
        return SensorTypeCategory.motionSensor;
      case SensorType.unknown:
        return SensorTypeCategory.unknown;
    }
  }

  /// Possible button positions
  List<SensorChannelPosition> get possibleButtonBehaviorPositions {
    switch (this) {
      case SensorType.enOceanButton1Way:
      case SensorType.wiredButton1Way:
        return [SensorChannelPosition.single];
      case SensorType.wiredButton2Way:
      case SensorType.enOceanButton2Way:
        return [SensorChannelPosition.top, SensorChannelPosition.bottom];
      case SensorType.enOceanButton4Way:
        return [
          SensorChannelPosition.topLeft,
          SensorChannelPosition.bottomLeft,
          SensorChannelPosition.topRight,
          SensorChannelPosition.bottomRight,
        ];
      case SensorType.motionSensor:
      case SensorType.unknown:
        return [];
    }
  }

  /// Buttons
  static List<SensorType> get enOceanButtons => [
    SensorType.enOceanButton1Way,
    SensorType.enOceanButton2Way,
    SensorType.enOceanButton4Way,
  ];

  // endregion

  /// From id
  static SensorType fromId(String id) => values.firstWhereOrNull((t) => t.id == id) ?? SensorType.unknown;
}
