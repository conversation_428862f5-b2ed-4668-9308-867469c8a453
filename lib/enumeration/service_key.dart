//
//  service_key.dart
//  EltakoConnect
//
//  Created by <PERSON> on 11.12.23.
//  Copyright © 2023 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/service/automation_service/automation_data.dart';
import 'package:eltako_connect/service/bluetooth_config_service/bluetooth_config_data.dart';
import 'package:eltako_connect/service/central_service/central_data.dart';
import 'package:eltako_connect/service/channel_service/channel_data.dart';
import 'package:eltako_connect/service/config_service/config_data.dart';
import 'package:eltako_connect/service/dimmer_behaviour_service/dimmer_behaviour_data.dart';
import 'package:eltako_connect/service/dimmer_control_service/dimmer_control_data.dart';
import 'package:eltako_connect/service/dimmer_service/dimmer_data.dart';
import 'package:eltako_connect/service/dimmer_settings_service/dimmer_settings_data.dart';
import 'package:eltako_connect/service/dimmer_time_program_service/dimmer_time_program_data.dart';
import 'package:eltako_connect/service/dimmer_value_service/dimmer_value_data.dart';
import 'package:eltako_connect/service/electricity_meter_definition_service/electricity_meter_definition_data.dart';
import 'package:eltako_connect/service/electricity_meter_service/electricity_meter_data.dart';
import 'package:eltako_connect/service/enocean_config_service/enocean_config_data.dart';
import 'package:eltako_connect/service/firmware_config_service/firmware_config_data.dart';
import 'package:eltako_connect/service/firmware_update_service/firmware_update_data.dart';
import 'package:eltako_connect/service/i_service_data.dart';
import 'package:eltako_connect/service/impulse_service/impulse_data.dart';
import 'package:eltako_connect/service/language_service/language_data.dart';
import 'package:eltako_connect/service/location_service/location_data.dart';
import 'package:eltako_connect/service/lock_service/lock_data.dart';
import 'package:eltako_connect/service/matter_sub_sensor_service/matter_sub_sensor_data.dart';
import 'package:eltako_connect/service/modbus_service/modbus_data.dart';
import 'package:eltako_connect/service/mqtt_service/mqtt_data.dart';
import 'package:eltako_connect/service/name_service/name_data.dart';
import 'package:eltako_connect/service/network_service/network_data.dart';
import 'package:eltako_connect/service/passkey_service/passkey_data.dart';
import 'package:eltako_connect/service/password_service/password_data.dart';
import 'package:eltako_connect/service/relay_function_service/relay_function_data.dart';
import 'package:eltako_connect/service/relay_service/relay_data.dart';
import 'package:eltako_connect/service/reset_service/reset_data.dart';
import 'package:eltako_connect/service/sensor_service/sensor_data.dart';
import 'package:eltako_connect/service/shader_service/shader_data.dart';
import 'package:eltako_connect/service/solstice_service/solstice_data.dart';
import 'package:eltako_connect/service/sub_device_service/sub_device_data.dart';
import 'package:eltako_connect/service/sub_sensor_service/sub_sensor_data.dart';
import 'package:eltako_connect/service/system_service/i_system_data.dart';
import 'package:eltako_connect/service/time_ntp_service/time_ntp_data.dart';
import 'package:eltako_connect/service/time_program_service/time_program_data.dart';
import 'package:eltako_connect/service/time_service/time_data.dart';
import 'package:eltako_connect/service/wired_input_service/wired_input_data.dart';

/// Enumeration for service keys
enum ServiceKey {
  // region [CASES]

  /// Automation service (internal use only)
  automation,

  /// Bluetooth config service
  bluetoothConfig,

  /// Central service
  central,

  /// Channel service
  channel,

  /// Config service
  config,

  /// Dimmer service
  dimmer,

  /// Dimmer behaviour service
  dimmerBehaviour,

  /// Dimmer control service (internal use only)
  dimmerControl,

  /// Dimmer settings service
  dimmerSettings,

  /// Dimmer time program service
  dimmerTimeProgram,

  /// Dimmer value service
  dimmerValue,

  /// Electricity meter service
  electricityMeter,

  /// Electricity meter definition service (internal use only)
  electricityMeterDefinition,

  /// EnOcean config service
  enOceanConfig,

  /// Firmware config service (internal use only)
  firmwareConfig,

  /// Firmware update service
  firmwareUpdate,

  /// Impulse service
  impulse,

  /// Language service
  language,

  /// Location service
  location,

  /// Lock service
  lock,

  /// Matter sub sensor service (internal use only)
  matterSubSensor,

  /// Modbus service
  modbus,

  /// MQTT service
  mqtt,

  /// Name service
  name,

  /// Network service
  network,

  /// Passkey service
  passkey,

  /// Password service
  password,

  /// Relay service (internal use only)
  relay,

  /// Relay function service
  relayFunction,

  /// Reset service
  reset,

  /// Sensor service
  sensor,

  /// Shader service
  shader,

  /// Solstice service
  solstice,

  /// Sub device service (internal use only)
  subDevice,

  /// Sub Sensor service (internal use only)
  subSensor,

  /// System service (internal use only)
  system,

  /// Time service
  time,

  /// Time NTP service
  timeNtp,

  /// Time program service
  timeProgram,

  /// Wired input service
  wiredInput;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Service name
  String get serviceName {
    switch (this) {
      case ServiceKey.automation:
        return 'Automation';
      case ServiceKey.bluetoothConfig:
        return 'Bluetooth config';
      case ServiceKey.central:
        return 'Central';
      case ServiceKey.channel:
        return 'Channel';
      case ServiceKey.config:
        return 'Config';
      case ServiceKey.dimmer:
        return 'Dimmer';
      case ServiceKey.dimmerBehaviour:
        return 'Dimmer behaviour';
      case ServiceKey.dimmerControl:
        return 'Dimmer control';
      case ServiceKey.dimmerSettings:
        return 'Dimmer settings';
      case ServiceKey.dimmerTimeProgram:
        return 'Dimmer time program';
      case ServiceKey.dimmerValue:
        return 'Dimmer value';
      case ServiceKey.electricityMeter:
        return 'Electricity meter';
      case ServiceKey.electricityMeterDefinition:
        return 'Electricity meter definition';
      case ServiceKey.enOceanConfig:
        return 'EnOcean config';
      case ServiceKey.firmwareConfig:
        return 'Firmware config';
      case ServiceKey.firmwareUpdate:
        return 'Firmware update';
      case ServiceKey.impulse:
        return 'Impulse';
      case ServiceKey.language:
        return 'Language';
      case ServiceKey.location:
        return 'Location';
      case ServiceKey.lock:
        return 'Lock';
      case ServiceKey.matterSubSensor:
        return 'Matter sub sensor';
      case ServiceKey.modbus:
        return 'Modbus';
      case ServiceKey.mqtt:
        return 'MQTT';
      case ServiceKey.name:
        return 'Name';
      case ServiceKey.network:
        return 'Network';
      case ServiceKey.passkey:
        return 'Passkey';
      case ServiceKey.password:
        return 'Password';
      case ServiceKey.relay:
        return 'Relay';
      case ServiceKey.relayFunction:
        return 'Relay function';
      case ServiceKey.reset:
        return 'Reset';
      case ServiceKey.sensor:
        return 'Sensor';
      case ServiceKey.shader:
        return 'Shader';
      case ServiceKey.solstice:
        return 'Solstice';
      case ServiceKey.subDevice:
        return 'Sub device';
      case ServiceKey.subSensor:
        return 'Sub sensor';
      case ServiceKey.system:
        return 'System';
      case ServiceKey.time:
        return 'Time';
      case ServiceKey.timeNtp:
        return 'Time NTP';
      case ServiceKey.timeProgram:
        return 'Time program';
      case ServiceKey.wiredInput:
        return 'Wired input';
    }
  }

  /// Localized service name
  String localizedServiceName({int? count}) {
    switch (this) {
      case ServiceKey.bluetoothConfig:
        return l10n.settingsBluetoothHeader;
      case ServiceKey.central:
        return l10n.settingsCentralHeader;
      case ServiceKey.channel:
        return l10n.generalTextChannel(count ?? 2);
      case ServiceKey.config:
        return l10n.detailsConfigurationsectionHeader;
      case ServiceKey.dimmer:
        return l10n.detailsConfigurationDimmingbehavior;
      case ServiceKey.dimmerBehaviour:
        return l10n.detailsConfigurationDimmingbehavior;
      case ServiceKey.dimmerSettings:
        return l10n.generalTextBasicsettings;
      case ServiceKey.dimmerTimeProgram:
        return l10n.detailsTimersectionHeader;
      case ServiceKey.dimmerValue:
        return l10n.generalTextDimvalue;
      case ServiceKey.electricityMeter:
        return l10n.electricMeter(1);
      case ServiceKey.enOceanConfig:
        return l10n.settingsEnOceanConfigHeader;
      case ServiceKey.firmwareUpdate:
        return l10n.updateHeader;
      case ServiceKey.impulse:
        return l10n.settingsImpulseHeader;
      case ServiceKey.language:
        return l10n.settingsLanguageHeader;
      case ServiceKey.location:
        return l10n.settingsLocationHeader;
      case ServiceKey.lock:
        return l10n.detailsConfigurationDevicedisplaylock;
      case ServiceKey.modbus:
        return l10n.settingsModbusHeader;
      case ServiceKey.mqtt:
        return l10n.mqttHeader;
      case ServiceKey.name:
        return l10n.settingsNameHeader;
      case ServiceKey.network:
        return l10n.generalTextNetwork;
      case ServiceKey.passkey:
        return l10n.detailsConfigurationPin;
      case ServiceKey.password:
        return l10n.detailsConfigurationWifiloginPassword;
      case ServiceKey.relayFunction:
        return l10n.detailsFunctionsHeader;
      case ServiceKey.reset:
        return l10n.settingsFactoryresetHeader;
      case ServiceKey.sensor:
        return l10n.settingsSensorHeader;
      case ServiceKey.shader:
        return l10n.deviceConfiguration;
      case ServiceKey.solstice:
        return l10n.settingsSolsticeHeader;
      case ServiceKey.time:
        return l10n.settingsDatetimeHeader;
      case ServiceKey.timeNtp:
        return l10n.settingsDatetimeHeader;
      case ServiceKey.timeProgram:
        return l10n.detailsTimersectionHeader;
      case ServiceKey.wiredInput:
        return l10n.detailsConfigurationSwitchbehavior;
      // Internal and hidden services (no localization needed)
      case ServiceKey.automation:
      case ServiceKey.dimmerControl:
      case ServiceKey.electricityMeterDefinition:
      case ServiceKey.firmwareConfig:
      case ServiceKey.matterSubSensor:
      case ServiceKey.relay:
      case ServiceKey.subDevice:
      case ServiceKey.subSensor:
      case ServiceKey.system:
        return '[DBG] $serviceName';
    }
  }

  /// Full name
  String get fullName => '$serviceName service';

  /// Coding key
  String get key => serviceName.toLowerCase().replaceAll(' ', '_');

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Get service key from coding key
  static ServiceKey? fromKey(String key) => ServiceKey.values.firstWhereOrNull((s) => s.key == key);

  /// Get service key from data type
  static ServiceKey? fromType<T extends IServiceData>() {
    // Get service key
    switch (T) {
      case const (AutomationData):
        return ServiceKey.automation;
      case const (BluetoothConfigData):
        return ServiceKey.bluetoothConfig;
      case const (CentralData):
        return ServiceKey.central;
      case const (ChannelData):
        return ServiceKey.channel;
      case const (ConfigData):
        return ServiceKey.config;
      case const (DimmerData):
        return ServiceKey.dimmer;
      case const (DimmerBehaviourData):
        return ServiceKey.dimmerBehaviour;
      case const (DimmerControlData):
        return ServiceKey.dimmerControl;
      case const (DimmerSettingsData):
        return ServiceKey.dimmerSettings;
      case const (DimmerTimeProgramData):
        return ServiceKey.dimmerTimeProgram;
      case const (DimmerValueData):
        return ServiceKey.dimmerValue;
      case const (ElectricityMeterData):
        return ServiceKey.electricityMeter;
      case const (ElectricityMeterDefinitionData):
        return ServiceKey.electricityMeterDefinition;
      case const (EnOceanConfigData):
        return ServiceKey.enOceanConfig;
      case const (FirmwareConfigData):
        return ServiceKey.firmwareConfig;
      case const (FirmwareUpdateData):
        return ServiceKey.firmwareUpdate;
      case const (ImpulseData):
        return ServiceKey.impulse;
      case const (LanguageData):
        return ServiceKey.language;
      case const (LocationData):
        return ServiceKey.location;
      case const (LockData):
        return ServiceKey.lock;
      case const (MatterSubSensorData):
        return ServiceKey.matterSubSensor;
      case const (ModbusData):
        return ServiceKey.modbus;
      case const (MqttData):
        return ServiceKey.mqtt;
      case const (NameData):
        return ServiceKey.name;
      case const (NetworkData):
        return ServiceKey.network;
      case const (PasskeyData):
        return ServiceKey.passkey;
      case const (PasswordData):
        return ServiceKey.password;
      case const (RelayData):
        return ServiceKey.relay;
      case const (RelayFunctionData):
        return ServiceKey.relayFunction;
      case const (ResetData):
        return ServiceKey.reset;
      case const (SensorData):
        return ServiceKey.sensor;
      case const (ShaderData):
        return ServiceKey.shader;
      case const (SolsticeData):
        return ServiceKey.solstice;
      case const (SubDeviceData):
        return ServiceKey.subDevice;
      case const (SubSensorData):
        return ServiceKey.subSensor;
      case const (ISystemData):
        return ServiceKey.system;
      case const (TimeData):
        return ServiceKey.time;
      case const (TimeNtpData):
        return ServiceKey.timeNtp;
      case const (TimeProgramData):
        return ServiceKey.timeProgram;
      case const (WiredInputData):
        return ServiceKey.wiredInput;
    }
    return null;
  }

  // endregion
}
