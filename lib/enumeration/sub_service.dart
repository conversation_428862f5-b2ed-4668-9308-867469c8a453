//
//  sub_service.dart
//  EltakoConnect
//
//  Created by <PERSON> on 23.08.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/electricity_meter_detail.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/electricity_meter_service/i_electricity_meter_service.dart';
import 'package:eltako_connect/service/i_service.dart';
import 'package:eltako_connect/view/service/electricity_meter_service/electricity_meter_edit/services/current_meter_values_service/current_service_view.dart';
import 'package:eltako_connect/view/service/electricity_meter_service/electricity_meter_edit/services/current_meter_values_service/current_service_view_model.dart';
import 'package:elta<PERSON>_connect/view/service/electricity_meter_service/electricity_meter_edit/services/register_table_service_view.dart';
import 'package:eltako_connect/view/service/electricity_meter_service/electricity_meter_edit/services/register_table_service_view_model.dart';
import 'package:eltako_connect/view/service/name_service/electricity_meter_name_service_view_model.dart';
import 'package:eltako_connect/view/service/name_service/name_service_view.dart';
import 'package:eltako_connect/view/service/name_service/sensor_name_service_view_model.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_edit/services/sensor_button_type_service/sensor_button_type_service_view.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_edit/services/sensor_button_type_service/sensor_button_type_service_view_model.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_snack_bar.dart';
import 'package:flutter/material.dart';

/// Enum for sub services
enum SubService {
  /// Sensor name
  sensorName,

  /// Button type
  buttonType,

  /// Button rotation
  buttonRotation,

  /// Electricity meter name
  electricityMeterName,

  /// Register table
  registerTable,

  /// Current Value
  currentValue;

  /// Key
  String get key {
    switch (this) {
      case SubService.sensorName:
        return 'sensor_name';
      case SubService.buttonType:
        return 'button_type';
      case SubService.buttonRotation:
        return 'button_rotation';
      case SubService.electricityMeterName:
        return 'electricity_meter_name';
      case SubService.registerTable:
        return 'register_table';
      case SubService.currentValue:
        return 'current_value';
    }
  }

  /// Name
  String get name {
    switch (this) {
      case SubService.sensorName:
        return 'Sensor name';
      case SubService.buttonType:
        return 'Button type';
      case SubService.buttonRotation:
        return 'Button rotation';
      case SubService.electricityMeterName:
        return 'Electricity meter name';
      case SubService.registerTable:
        return 'Register table';
      case SubService.currentValue:
        return 'Current value';
    }
  }

  /// Localized Name
  String get localizedName {
    switch (this) {
      case SubService.sensorName:
      case SubService.electricityMeterName:
        return l10n.settingsNameHeader;
      case SubService.buttonType:
        return l10n.setButtonType;
      case SubService.buttonRotation:
        return l10n.setButtonOrientation;
      case SubService.registerTable:
        return l10n.registerTable(1);
      case SubService.currentValue:
        return l10n.currentValues;
    }
  }

  /// Icon
  dynamic get icon {
    switch (this) {
      case SubService.sensorName:
      case SubService.electricityMeterName:
        return Assets.icons.edit.circle;
      case SubService.buttonType:
        return Assets.icons.multiswitch.circle;
      case SubService.buttonRotation:
        return Assets.icons.lightSwitch.circle;
      case SubService.registerTable:
        return Assets.icons.zgwWifi.circle;
      case SubService.currentValue:
        return Assets.icons.lightning.circle;
    }
  }

  /// On tap
  Function() onTap({
    required BuildContext context,
    required Future<VoidResult> Function() onSave,
    ISensor? sensor,
    ElectricityMeterDetail? electricityMeter,
    IService? service,
  }) {
    switch (this) {
      case SubService.sensorName:
        return () async =>
            sensor != null
                ? Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => NameServiceView(viewModel: SensorNameServiceViewModel(sensor, onSave)),
                  ),
                )
                : EltakoSnackBar(title: l10n.error).show(context);
      case SubService.buttonType:
        return () async =>
            sensor != null
                ? Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) => SensorButtonTypeServiceView(
                          viewModel: SensorButtonTypeServiceViewModel(
                            sensor as EnOceanButton,
                            // TODO(DK): Add onSave instead of Future.value(const VoidResult.success())
                            // return onSave();
                            () => Future.value(const VoidResult.success()),
                          ),
                        ),
                  ),
                )
                : EltakoSnackBar(title: l10n.error).show(context);
      case SubService.buttonRotation:
        return () => const EltakoSnackBar.notImplemented().show(context);
      case SubService.electricityMeterName:
        return () async =>
            electricityMeter != null
                ? Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            NameServiceView(viewModel: ElectricityMeterNameServiceViewModel(electricityMeter, onSave)),
                  ),
                )
                : EltakoSnackBar(title: l10n.error).show(context);
      case SubService.registerTable:
        return () async =>
            electricityMeter != null && service != null
                ? Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) => RegisterTableServiceView(
                          viewModel: RegisterTableServiceViewModel(
                            service as IElectricityMeterService,
                            electricityMeter,
                          ),
                        ),
                  ),
                )
                : EltakoSnackBar(title: l10n.error).show(context);
      case SubService.currentValue:
        return () async =>
            electricityMeter != null && service != null
                ? Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) => CurrentServiceView(
                          viewModel: CurrentServiceViewModel(service as IElectricityMeterService, electricityMeter),
                        ),
                  ),
                )
                : EltakoSnackBar(title: l10n.error).show(context);
    }
  }
}
