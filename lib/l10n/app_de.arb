{"appName": "ELTAKO Connect", "discoveryHint": "Bluetooth am Gerät aktivieren, um zu verbinden.", "devicesFound": "{count, plural, =0 {<PERSON><PERSON> Ger<PERSON>e gefunden} one {1 Gerät gefunden} other {{count} Geräte gefunden}}", "@devicesFound": {"description": "Specifies how many device are found in the discovery overview", "type": "text"}, "discoveryDemodeviceName": "{count, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "discoverySu12Description": "2-<PERSON><PERSON> Bluetooth", "discoveryImprint": "Impressum", "discoveryLegalnotice": "Datenschutzerklärung", "generalSave": "Speichern", "generalCancel": "Abbrechen", "detailsHeaderHardwareversion": "Hardware Version", "detailsHeaderSoftwareversion": "Software Version", "detailsHeaderConnected": "Verbunden", "detailsHeaderDisconnected": "Getrennt", "detailsTimersectionHeader": "Programme", "detailsTimersectionTimercount": "von 60 Programmen verwendet", "detailsConfigurationsectionHeader": "Konfiguration", "detailsConfigurationPin": "Geräte-PIN", "detailsConfigurationChannelsDescription": "Kanal 1: {channel1} und Kanal 2: {channel2}", "settingsCentralHeader": "Zentral Ein/Aus", "detailsConfigurationCentralDescription": "<PERSON><PERSON><PERSON> nur, wenn der Kanal auf Auto steht.", "detailsConfigurationDevicedisplaylock": "Gerätedisplay sperren", "timerOverviewHeader": "Programme", "timerOverviewTimersectionTimerinactivecount": "inaktiv", "timerDetailsListsectionDays1": "Montag", "timerDetailsListsectionDays2": "Dienstag", "timerDetailsListsectionDays3": "Mittwoch", "timerDetailsListsectionDays4": "Don<PERSON><PERSON>", "timerDetailsListsectionDays5": "Freitag", "timerDetailsListsectionDays6": "Samstag", "timerDetailsListsectionDays7": "Sonntag", "timerDetailsHeader": "Programm", "timerDetailsSunrise": "Sonnenaufgang", "generalToggleOff": "Aus", "generalToggleOn": "Ein", "timerDetailsImpuls": "Impuls", "generalTextTime": "Uhrzeit", "generalTextAstro": "Astro", "generalTextAuto": "Auto", "timerDetailsOffset": "Zeitversatz", "timerDetailsPlausibility": "Plausibilitätsprüfung aktivieren", "timerDetailsPlausibilityDescription": "Liegt die 'Aus'-Zeit vor der 'Ein'-Zeit werden beide Zeiten ignoriert, z.<PERSON><PERSON>e zu Sonnenaufgang ein und um 6:00 morgens aus. Es gibt auch Konstellationen zu denen die Prüfung ungewollt ist, z.<PERSON><PERSON> Schalte zu Sonnenuntergang ein und um 1:00 nachts aus.", "generalDone": "<PERSON><PERSON><PERSON>", "generalDelete": "Löschen", "timerDetailsImpulsDescription": "Ändere die globale Impuls Konfiguration", "settingsNameHeader": "G<PERSON><PERSON><PERSON><PERSON>", "settingsNameDescription": "Dieser Name dient zur Identifikation des Geräts.", "settingsFactoryresetHeader": "Werkseinstellungen", "settingsFactoryresetDescription": "Welche Inhalte sollen zurückgesetzt werden?", "settingsFactoryresetResetbluetooth": "Bluetooth Einstellungen zurücksetzen", "settingsFactoryresetResettime": "Uhrzeit Einstellungen zurücksetzen", "settingsFactoryresetResetall": "Auf Werkseinstellungen zurücksetzen", "settingsDeletetimerHeader": "Programme löschen", "settingsDeletetimerDescription": "<PERSON><PERSON> wirklich alle Programme gelöscht werden?", "settingsDeletetimerAllchannels": "Alle Kanäle", "settingsImpulseHeader": "Impuls-Schaltzeit", "settingsImpulseDescription": "Die Impuls-Schaltzeit gibt die Dauer des Impulses an.", "generalTextRandommode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsChannelsTimeoffsetHeader": "Zeitversatz Sonnenwende", "settingsChannelsTimeoffsetDescription": "Sommer: {summerOffset} Min | Winter: {winterOffset} Min", "settingsLocationHeader": "<PERSON><PERSON>", "settingsLocationDescription": "Lege deinen Standort fest, um Astro-Funktionen zu nutzen.", "settingsLanguageHeader": "Gerätesprache", "settingsLanguageSetlanguageautomatically": "Sprache automatisch einstellen", "settingsLanguageDescription": "W<PERSON>hle die Sprache der {deviceType}", "settingsLanguageGerman": "De<PERSON>ch", "settingsLanguageFrench": "Franzö<PERSON><PERSON>", "settingsLanguageEnglish": "<PERSON><PERSON><PERSON>", "settingsLanguageItalian": "Italienisch", "settingsLanguageSpanish": "Spanisch", "settingsDatetimeHeader": "Datum und Uhrzeit", "settingsDatetimeSettimeautomatically": "Systemzeit übernehmen", "settingsDatetimeSettimezoneautomatically": "Zeitzone automatisch einstellen", "generalTextTimezone": "Zeitzone", "settingsDatetime24Hformat": "24-Stunden-Format", "settingsDatetimeSetsummerwintertimeautomatically": "Sommer-/Winterzeit automatisch", "settingsDatetimeWinter": "Winter", "settingsDatetimeSummer": "Sommer", "settingsPasskeyHeader": "Aktuelle Geräte-PIN", "settingsPasskeyDescription": "Aktuelle PIN des Geräts eingeben", "timerDetailsActiveprogram": "Programm aktiv", "timerDetailsActivedays": "Aktive Tage", "timerDetailsSuccessdialogHeader": "Erfolgreich", "timerDetailsSuccessdialogDescription": "Programm erfolgreich hinzugefügt.", "settingsRandommodeDescription": "Der Zufallsmodus funktioniert nur bei Zeit-Programmen, nicht bei Impuls- oder Astro-Programmen (Sonnenauf- oder untergang).", "settingsSolsticeHeader": "Zeitversatz Sonnenwende", "settingsSolsticeDescription": "Die Zeit gibt den Zeitversatz zum Sonnenuntergang an. Der Sonnenaufgang wird entsprechend invertiert.", "settingsSolsticeHint": "Beispiel: \n<PERSON><PERSON> Winter wird 30 Minuten vor Sonnenuntergang geschalten, dadurch wird ebenfalls 30 Minuten nach Sonnenaufgang geschalten.", "generalTextMinutesShort": "Min", "settingsPinDescription": "Die PIN wird für die Verbindung benötigt.", "settingsPinHeader": "Neue Geräte-PIN", "settingsPinNewpinDescription": "Neue PIN eingeben", "settingsPinNewpinRepeat": "Neue PIN wiederholen", "detailsProductinfo": "Produktinformation", "settingsDatetimeSettimeautodescription": "Wähle die gewünschte Uhrzeit", "minutes": "{count, plural, one {Minute} other {Minuten}}", "hours": "{count, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "seconds": "{count, plural, one {Sekunde} other {Sekunden}}", "generalTextChannel": "{count, plural, one {Kanal} other {<PERSON><PERSON><PERSON><PERSON>}}", "generalLabelChannel": "Kanal {number}", "generalTextDate": "Datum", "settingsDatetime24HformatDescription": "Wähle das bevorzugte Format", "settingsDatetimeSetsummerwintertime": "Sommer-/Winterzeit", "settingsDatetime24HformatValue24": "24-<PERSON><PERSON><PERSON>", "settingsDatetime24HformatValue12": "12-<PERSON><PERSON><PERSON>", "detailsEdittimer": "Programme bearbeiten", "settingsPinOldpinRepeat": "Bitte die alte PIN wiederholen", "settingsPinCheckpin": "PIN wird überprüft", "detailsDevice": "{count, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "detailsDisconnect": "Verbindung trennen", "settingsCentralDescription": "Der Eingang A1 steuert den Zentral Ein/Aus.\nZentral Ein/Aus greift nur, wenn der Kanal auf Zentral Ein/Aus steht.", "settingsCentralHint": "Beispiel:\nKanal 1 = Zentral Ein/Aus\nKanal 2 = Aus\nA1 = Zentral Ein -> Nur K1 schaltet auf Ein, K2 bleibt Aus.", "settingsCentralToggleheader": "Zentral Eingang schaltet", "settingsCentralActivechannelsdescription": "Aktuelle Kanäle mit der Einstellung Zentral Ein/Aus:", "settingsSolsticeSign": "Vorzeichen", "settingsDatetimeTimezoneDescription": "Mitteleuropäische Zeit", "generalButtonContinue": "<PERSON><PERSON>", "settingsPinConfirmationDescription": "PIN Änderung erfolgreich.", "settingsPinFailDescription": "PIN Änderung fehlgeschlagen.", "settingsPinFailHeader": "Fehlgeschlagen", "settingsPinFailShort": "Die PIN muss exakt 6 <PERSON><PERSON><PERSON> lang sein", "settingsPinFailWrong": "Die aktuelle PIN ist nicht korrekt", "settingsPinFailMatch": "Die PINs stimmen nicht überein", "discoveryLostconnectionHeader": "Verbindung verloren", "discoveryLostconnectionDescription": "Die Verbindung zum Gerät wurde getrennt.", "settingsChannelConfigCentralDescription": "Verh<PERSON>lt sich wie AUTO und reagiert zusätzlich auf die drahtgebundenen Zentraleingänge.", "settingsChannelConfigOnDescription": "Schaltet den Kanal auf Dauer-Ein und ignoriert die Programme.", "settingsChannelConfigOffDescription": "Schaltet den Kanal auf Dauer-Aus und ignoriert die Programme.", "settingsChannelConfigAutoDescription": "Schaltet entsprechend der Zeit- und Astro-Programme.", "bluetoothPermissionDescription": "Bluetooth wird für die Konfiguration der Geräte benötigt.", "timerListitemOn": "Einschalten", "timerListitemOff": "Ausschalten", "timerListitemUnknown": "Unbekannt", "timerDetailsAstroHint": "Der Standort muss in den Einstellungen gesetzt sein, damit die Astro-Programme korrekt funktionieren.", "timerDetailsTrigger": "Auslöser", "timerDetailsSunset": "Sonnenuntergang", "settingsLocationCoordinates": "Koordinaten", "settingsLocationLatitude": "Breitengrad", "settingsLocationLongitude": "Längengrad", "timerOverviewEmptyday": "<PERSON><PERSON>r {day} werden momentan keine Programme verwendet.", "timerOverviewProgramloaded": "Programme werden geladen...", "timerOverviewProgramchanged": "Programm wurde geändert.", "settingsDatetimeProcessing": "Datum und Uhrzeit wird geändert.", "deviceNameEmpty": "Die Eingabe darf nicht leer sein.", "deviceNameHint": "Die Eingabe darf nicht mehr als {count} Zei<PERSON> enthalten.", "deviceNameChanged": "Gerätename wird geändert.", "deviceNameChangedSuccessfully": "Gerätename wurde erfolgreich geändert.", "deviceNameChangedFailed": "Ein Fehler ist aufgetreten.", "settingsPinConfirm": "Bestätigen", "deviceShowInstructions": "1. Blue<PERSON> der Uhr mit SET aktivieren\n2. <PERSON><PERSON> oben antippen, um die Suche zu starten", "deviceNameNew": "Neuen Gerätename eingeben", "settingsLanguageRetrieved": "Sprache wird abgerufen", "detailsProgramsShow": "Programme anzeigen", "generalTextProcessing": "Bitte warten", "generalTextRetrieving": "werden abgerufen.", "settingsLocationPermission": "Erlaube der ELTAKO Connect den Zugriff auf den Standort dieses Gerätes.", "timerOverviewChannelloaded": "Kanäle werden geladen", "generalTextRandommodeChanged": "Zufallsmodus wird geändert", "detailsConfigurationsectionChanged": "Konfiguration wird geändert", "settingsSettimeFunctions": "Zeitfunktionen werden geändert", "imprintContact": "Kontakt", "imprintPhone": "Telefon", "imprintMail": "E-Mail", "imprintRegistrycourt": "Registergericht", "imprintRegistrynumber": "Registernummer", "imprintCeo": "Geschäftsführer", "imprintTaxnumber": "Umsatzsteuer-Identifikationsnummer", "settingsLocationCurrent": "Aktueller Standort", "generalTextReset": "Z<PERSON>ücksetzen", "discoverySearchStart": "<PERSON><PERSON> starten", "discoverySearchStop": "<PERSON><PERSON> stoppen", "settingsImpulsSaved": "Impuls-Schaltzeit wird gespeichert", "settingsCentralNochannel": "<PERSON>s gibt keine Kanäle mit der Einstellung Zentral Ein/Aus.", "settingsFactoryresetBluetoothConfirmationDescription": "Bluetooth Verbindung wurde erfolgreich zurückgesetzt.", "settingsFactoryresetBluetoothFailDescription": "Bluetooth Verbindungen zurücksetzen fehlgeschlagen.", "imprintPublisher": "Herausgeber", "discoveryDeviceConnecting": "Verbindung wird hergestellt", "discoveryDeviceRestarting": "Neu starten...", "generalTextConfigurationsaved": "Kanalkonfiguration gespeichert.", "timerOverviewChannelssaved": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>iche<PERSON>", "timerOverviewSaved": "<PERSON><PERSON><PERSON>", "timerSectionList": "Listenansicht", "timerSectionDayview": "Tagesansicht", "generalTextChannelInstructions": "Kanaleinstellungen", "generalTextPublisher": "Herausgeber", "settingsDeletetimerDialog": "Wollen Si<PERSON> wirklich alle Programme löschen?", "settingsFactoryresetResetbluetoothDialog": "<PERSON><PERSON> wirklich alle Bluetooth Einstellungen zurückgesetzt werden?", "settingsCentralTogglecentral": "Zentral\nEin/Aus", "generalTextConfirmation": "{serviceName} wurde erfolgreich geändert.", "generalTextFailed": "{serviceName} konnte nicht geändert werden.", "settingsChannelConfirmationDescription": "Ka<PERSON><PERSON>le wurden erfolgreich geändert.", "timerDetailsSaveHeader": "<PERSON><PERSON> s<PERSON>iche<PERSON>", "timerDetailsDeleteHeader": "Programm löschen", "timerDetailsSaveDescription": "Programm wurde erfolgreich gespeichert.", "timerDetailsDeleteDescription": "Programm wurde erfolgreich gelöscht.", "timerDetailsAlertweekdays": "Das Programm kann nicht gespeichert werden, da kein Wochentag ausgewählt wurde.", "generalTextOk": "OK", "settingsDatetimeChangesuccesfull": "Datum und Uhrzeit wurden erfolgreich geändert.", "discoveryConnectionFailed": "Verbindung fehlgeschlagen", "discoveryDeviceResetrequired": "Es konnte keine Verbindung mit dem Gerät hergestellt werden, um dieses Problem zu beheben lösche das Gerät aus deinen Bluetooth Einstellungen. Sollte das Problem weiterhin bestehen wende dich bitte an unseren technischen Support.", "generalTextSearch": "<PERSON><PERSON><PERSON><PERSON>en", "generalTextOr": "oder", "settingsFactoryresetProgramsConfirmationDescription": "Alle Programme wurden erfolgreich gelöscht.", "generalTextManualentry": "<PERSON><PERSON>", "settingsLocationSaved": "Gespeicherter Standort", "settingsLocationAutosearch": "Automatische Standortsuche", "imprintPhoneNumber": "+49 711 / 9435 0000", "imprintMailAddress": "<EMAIL>", "settingsFactoryresetResetallDialog": "Soll das Gerät wirklich auf Werkseinstellungen zurückgesetzt werden?", "settingsFactoryresetFactoryConfirmationDescription": "Das Gerät wurde erfolgreich auf Werkseinstellungen zurückgesetzt.", "settingsFactoryresetFactoryFailDescription": "Gerät zurücksetzen fehlgeschlagen.", "imprintPhoneNumberIos": "+49711/94350000", "mfzFunctionA2Title": "2-Stufen-Ansprechverzögerung (A2)", "mfzFunctionA2TitleShort": "2-Stufen-Ansprechverzögerung (A2)", "mfzFunctionA2Description": "Mit dem Anlegen der Steuerspannung beginnt der Zeitablauf T1 zwischen 0 und 60 Sekunden. An dessen Ende schließt der Kontakt 1-2 und es beginnt der Zeitablauf T2 zwischen 0 und 60 Sekunden. An dessen Ende schließt der Kontakt 3-4. Nach einer Unterbrechung beginnt der Zeitablauf erneut mit T1.", "mfzFunctionRvTitle": "Rückfallverzögerung (RV; Ausschaltverzögerung)", "mfzFunctionRvTitleShort": "RV | Rückfallverzögerung", "mfzFunctionRvDescription": "Beim Anlegen der Steuerspannung wechselt der Arbeitskontakt nach 15-18. \n<PERSON><PERSON> Unterbrechung der Steuerspannung beginnt der Zeitablauf, an dessen Ende der Arbeitskontakt in die Ruhelage zurückkehrt. Nachschaltbar während des Zeitablaufs.\n", "mfzFunctionTiTitle": "Taktgeber mit Impuls beginnend (TI; Blinkrelais)", "mfzFunctionTiTitleShort": "TI | Taktgeber mit Impuls beginnend", "mfzFunctionTiDescription": "Solange die Steuerspannung anliegt, schließt und öffnet der Arbeitskontakt. Die Umschaltzeit in beide Richtungen ist getrennt einstellbar. Beim Anlegen der Steuerspannung wechselt der Arbeitskontakt sofort nach 15-18.", "mfzFunctionAvTitle": "Ansprechverzögerung (AV; Einschaltverzögerung)", "mfzFunctionAvTitleShort": "AV | Ansprechverzögerung", "mfzFunctionAvDescription": "Mit dem Anlegen der Steuerspannung beginnt der Zeitablauf, an dessen Ende der Arbeitskontakt nach 15-18 wechs<PERSON><PERSON>. Nach einer Unterbrechung beginnt der Zeitablauf erneut.", "mfzFunctionAvPlusTitle": "Additive Ansprechverzögerung (AV+; Einschaltverzögerung)", "mfzFunctionAvPlusTitleShort": "AV+ | Additive Ansprechverzögerung", "mfzFunctionAvPlusDescription": "Funktion wie AV, nach einer Unterbrechung bleibt jedoch die bereits abgelaufene Zeit gespeichert.", "mfzFunctionAwTitle": "Ausschaltwischrelais (AW)", "mfzFunctionAwTitleShort": "AW | Ausschaltwischrelais", "mfzFunctionAwDescription": "Bei Unterbrechung der Steuerspannung wechselt der Arbeitskontakt nach 15-18 und kehrt nach Ablauf der Wischzeit zurück. Beim Anlegen der Steuerspannung während der Wischzeit kehrt der Arbeitskontakt sofort in die Ruhelage zurück, und die Restzeit wird gelöscht.", "mfzFunctionIfTitle": "Impulsformer (IF; nur MFZ12.1)", "mfzFunctionIfTitleShort": "IF | Impulsformer", "mfzFunctionIfDescription": "Mit dem Anlegen der Steuerspannung wechselt der Arbeitskontakt für die eingestellte Zeit nach 15-18. <PERSON>tere Ansteuerungen werden erst nach dem Ablauf der eingestellten Zeit ausgewertet.", "mfzFunctionEwTitle": "Einschaltwischrelais (EW)", "mfzFunctionEwTitleShort": "EW | Einschaltwischrelais", "mfzFunctionEwDescription": "Mit dem Anlegen der Steuerspannung wechselt der Arbeitskontakt nach 15-18 und kehrt nach Ablauf der Wischzeit zurück. Bei Wegnahme der Steuerspannung, während der Wischzeit kehrt der Arbeitskontakt sofort in die Ruhelage zurück und die Restzeit wird gelöscht.\n", "mfzFunctionEawTitle": "Einschalt- und Ausschaltwischrelais (EAW; nur MFZ12.1)", "mfzFunctionEawTitleShort": "EAW | Einschalt- und Ausschaltwischrelais", "mfzFunctionEawDescription": "Mit dem Anlegen und Unterbrechen der Steuerspannung wechselt der Arbeitskontakt nach 15-18 und kehrt nach der eingestellten Wischzeit zurück.", "mfzFunctionTpTitle": "Taktgeber mit Pause beginnend (TP; Blinkrelais, nur MFZ12.1)", "mfzFunctionTpTitleShort": "TP | Taktgeber mit Pause beginnend", "mfzFunctionTpDescription": "Funktionsbeschreibungen wie TI, beim Anlegen der Steuerspannung wechselt der Kontakt jedoch nicht nach 15-18, sondern bleibt zunächst bei 15-16 bzw. offen.", "mfzFunctionIaTitle": "Impulsgesteuerte Ansprechverzögerung und Impulsformer (IA; nur MFZ12.1)", "mfzFunctionIaTitleShort": "IA | Impulsgesteuerte Ansprechverzögerung", "mfzFunctionIaDescription": "Mit dem Beginn eines Steuerimpulses ab 20 ms beginnt der Zeitablauf T1, an dessen\nEnde der Arbeitskontakt für die Zeit T2 nach 15-18 wechs<PERSON>t (z. B. für automatische Türöffner). Wird T1 auf die kürzeste Zeit 0,1 s g<PERSON><PERSON><PERSON>, arbeitet IA als Impulsformer, bei welchem T2 abläuft, unab<PERSON><PERSON><PERSON><PERSON> von der Länge des Steuersignals (mind. 150 ms).", "mfzFunctionArvTitle": "Ansprech- und Rückfallverzögerung (ARV)", "mfzFunctionArvTitleShort": "ARV | Ansprech- und Rückfallverzögerung", "mfzFunctionArvDescription": "Mit dem Anlegen der Steuerspannung beginnt der Zeitablauf, an dessen Ende der Arbeitskontakt nach 15 -18 wechs<PERSON><PERSON>. Wird danach die Steuerspannung unterbrochen, beginnt ein weiterer Zeitablauf, an dessen Ende der Arbeitskontakt in die Ruhelage zurückkehrt.\nNach einer Unterbrechung der Ansprechverzögerung beginnt der Zeitablauf erneut.", "mfzFunctionArvPlusTitle": "Additive Ansprech- und Rückfallverzögerung (ARV+)", "mfzFunctionArvPlusTitleShort": "ARV+ | Additive Ansprech- und Rückfallverzögerung", "mfzFunctionArvPlusDescription": "Funktion wie ARV, nach einer Unterbrechung der Ansprechverzögerung bleibt jedoch die bereits abgelaufene Zeit gespeichert.", "mfzFunctionEsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ES)", "mfzFunctionEsTitleShort": "ES | Stromstoßschalter", "mfzFunctionEsDescription": "<PERSON><PERSON> ab 50 ms schaltet der Arbeitskontakt hin und her.", "mfzFunctionEsvTitle": "Stromstoßschalter mit Rückfallverzögerung und Ausschaltvorwarnung (ESV)", "mfzFunctionEsvTitleShort": "ESV | Stromstoßschalter mit Rückfallverzögerung", "mfzFunctionEsvDescription": "Funktion wie SRV. Zusätzlich mit Ausschaltvorwarnung: ca. 30 Sekunden vor Zeitablauf beginnend flackert die Beleuchtung 3-mal in kürzer werdenden Zeitabständen.", "mfzFunctionErTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ER)", "mfzFunctionErTitleShort": "ER | Relais-Funktion", "mfzFunctionErDescription": "Solange der Steuerkontakt geschlossen ist, schaltet der Arbeitskontakt von 15-16 auf 15-18.", "mfzFunctionSrvTitle": "Stromstoßschalter mit Rückfallverzögerung (SRV)", "mfzFunctionSrvTitleShort": "SRV | Stromstoßschalter mit Rückfallverzögerung", "mfzFunctionSrvDescription": "<PERSON><PERSON> Steuerimp<PERSON>n ab 50ms schaltet der Arbeitskontakt hin und her. In der Kontaktstellung 15-18 schaltet das Gerät nach Ablauf der Verzögerungszeit selbsttätig in die Ruhestellung 15-16 zurück.", "detailsFunctionsHeader": "Funktionen", "mfzFunctionTimeHeader": "Zeit (t{index})", "mfzFunctionOnDescription": "<PERSON><PERSON> Ein.", "mfzFunctionOffDescription": "<PERSON><PERSON>.", "mfzFunctionMultiplier": "<PERSON><PERSON><PERSON>", "discoveryMfz12Description": "Multifunktions-Zeitrelais Bluetooth", "mfzFunctionOnTitle": "Ein (ON)", "mfzFunctionOnTitleShort": "Ein (ON)", "mfzFunctionOffTitle": "Aus (OFF)", "mfzFunctionOffTitleShort": "Aus (OFF)", "mfzMultiplierSecondsFloatingpoint": "0.1 Sekunden", "mfzMultiplierMinutesFloatingpoint": "0.1 Minuten", "mfzMultiplierHoursFloatingpoint": "0.1 Stunden", "mfzOverviewFunctionsloaded": "Funktionen werden geladen", "mfzOverviewSaved": "Funktion gespeichert", "settingsBluetoothHeader": "Bluetooth", "bluetoothChangedSuccessfully": "Bluetooth-Einstellung wurde erfolgreich geändert", "settingsBluetoothInformation": "Hinweis: Wen<PERSON> diese Einstellung aktiviert wird, ist das Gerät über Bluetooth dauerhaft für jeden sichtbar!\nEs wird empfohlen den Geräte-PIN zu ändern.", "settingsBluetoothContinuousconnection": "Dauerhafte Sichtbarkeit", "settingsBluetoothContinuousconnectionDescription": "Durch das Aktivieren der dauerhaften Sichtbarkeit, bleibt Bluetooth am Gerät ({deviceType}) aktiv und muss vor dem Verbindungsaufbau nicht manuell aktiviert werden.", "settingsBluetoothTimeout": "Verbindungs-Timeout", "settingsBluetoothPinlimit": "PIN-Beschränkung", "settingsBluetoothTimeoutDescription": "Die Verbindung wird nach {timeout} Minuten Inaktivität getrennt.", "settingsBluetoothPinlimitDescription": "Aus Sicherheitsgründen hast du maximal {attempts} Versuche für die PIN-Eingabe. Anschließend wird Bluetooth deaktiviert und muss für eine neue Verbindung erst manuell wieder aktiviert werden.", "settingsBluetoothPinAttempts": "<PERSON><PERSON><PERSON>", "settingsResetfunctionHeader": "Funktionen zurücksetzen", "settingsResetfunctionDialog": "<PERSON><PERSON> wirklich alle Funktionen zurückgesetzt werden?", "settingsFactoryresetFunctionsConfirmationDescription": "Alle Funktionen wurden erfolgreich zurückgesetzt.", "mfzFunctionTime": "Zeit (t)", "discoveryConnectionFailedInfo": "<PERSON><PERSON>-Verbindung.", "detailsConfigurationDevicedisplaylockDialogtext": "Wenn du das Gerätedisplay sperrst, wird im Anschluss Bluetooth deaktiviert und muss für eine neue Verbindung erst manuell wieder aktiviert werden.", "detailsConfigurationDevicedisplaylockDialogquestion": "Möchtest du das Gerätedisplay wirklich sperren?", "settingsDemodevices": "Demo-Geräte anzeigen", "generalTextSettings": "Einstellungen", "discoveryWifi": "WiFi", "settingsInformations": "Informationen", "detailsConfigurationDimmingbehavior": "Dimmverhalten", "detailsConfigurationSwitchbehavior": "Tasterverhalten", "detailsConfigurationBrightness": "Helligkeit", "detailsConfigurationMinimum": "Mindesthelligkeit", "detailsConfigurationMaximum": "Maximalhelligkeit", "detailsConfigurationSwitchesGr": "Gruppenrelais (GR)", "detailsConfigurationSwitchesGs": "G<PERSON>pensch<PERSON>ter (GS)", "detailsConfigurationSwitchesCloserer": "<PERSON><PERSON><PERSON><PERSON><PERSON> (ER)", "detailsConfigurationSwitchesClosererDescription": "Aus -> <PERSON><PERSON><PERSON><PERSON><PERSON> halten (An) -> Loslassen (Aus)", "detailsConfigurationSwitchesOpenerer": "<PERSON><PERSON><PERSON> (ER-Invers)", "detailsConfigurationSwitchesOpenererDescription": "An -> <PERSON><PERSON><PERSON><PERSON><PERSON> halten (Aus) -> Loslassen (An)", "detailsConfigurationSwitchesSwitch": "Wechselschalter", "detailsConfigurationSwitchesSwitchDescription": "<PERSON>t jedem Schalten wird das Licht an- und ausgeschalten.", "detailsConfigurationSwitchesImpulsswitch": "Stromstoß-Schalter", "detailsConfigurationSwitchesImpulsswitchDescription": "Taster wird kurz gedr<PERSON>t und losgelassen, um Licht an- bzw. auszuschalten.", "detailsConfigurationSwitchesClosererDescription2": "Schalter gedrückt halten. Beim loslassen stoppt der Motor.", "detailsConfigurationSwitchesImpulsswitchDescription2": "Taster wird kurz gedr<PERSON>, um den Motor zu starten und kurz gedr<PERSON>t, um ihn wieder zu stoppen.", "detailsConfigurationWifiloginScan": "QR-Code scannen", "detailsConfigurationWifiloginScannotvalid": "Der gescannte Code ist ungültig", "detailsConfigurationWifiloginDescription": "Code eingeben", "detailsConfigurationWifiloginPassword": "Passwort", "discoveryEsbipDescription": "Rolladen- und Beschattungsaktor IP", "discoveryEsripDescription": "Stromstoß-Schaltrelais IP", "discoveryEudipDescription": "Universal-Dimmschalter IP", "generalTextLoad": "<PERSON><PERSON><PERSON>", "wifiBasicautomationsNotFound": "Keine Automation gefunden.", "wifiCodeInvalid": "Ungültiger Code", "wifiCodeValid": "Gültiger Code", "wifiAuthorizationLogin": "Verbinden", "wifiAuthorizationLoginFailed": "<PERSON><PERSON> fehlgeschlagen", "wifiAuthorizationSerialnumber": "Seriennummer", "wifiAuthorizationProductiondate": "Produktionsdatum", "wifiAuthorizationProofofpossession": "PoP", "generalTextWifipassword": "WiFi-Passwort", "generalTextUsername": "<PERSON><PERSON><PERSON><PERSON>", "generalTextEnter": "ODER MANUELL EINGEBEN", "wifiAuthorizationScan": "<PERSON>anne den ELTAKO-Code.", "detailsConfigurationDevicesNofunctionshinttext": "Dieses Gerät unterstützt aktuell keine weiteren Einstellungen. ", "settingsUsedemodelay": "Demo-Verzögerung verwenden", "settingsImpulsLoad": "Impuls-Schaltzeit wird geladen", "settingsBluetoothLoad": "Bluetooth-Einstellung wird geladen.", "detailsConfigurationsectionLoad": "Konfigurationen werden geladen", "generalTextLogin": "Anmelden", "generalTextAuthentication": "Authentifizieren", "wifiAuthorizationScanDescription": "Suche auf dem WiFi-Gerät oder auf der beiliegenden Info-Karte nach dem ELTAKO-Code und scanne diesen mit deiner Kamera.", "wifiAuthorizationScanShort": "ELTAKO-Code scannen", "detailsConfigurationEdgemode": "Dimmkurven", "detailsConfigurationEdgemodeLeadingedge": "Phasenansch<PERSON>tt", "generalTextNetwork": "Netzwerk", "wifiAuthenticationSuccessful": "Authentifizierung erfolgreich", "detailsConfigurationsectionSavechange": "Konfiguration geändert", "discoveryWifiAdddevice": "WiFi Gerät hinzufügen", "wifiAuthenticationDelay": "Dies kann bis zu 1 Minute dauern.", "generalTextRetry": "<PERSON><PERSON><PERSON> versuchen", "wifiAuthenticationCredentials": "Bitte gib die Daten deines WiFis ein.", "wifiAuthenticationSsid": "SSID", "wifiAuthenticationDelaylong": "Es kann bis zu 1 Minute dauern, bis das Gerät bereit ist und in der App erscheint.", "wifiAuthenticationCredentialsShort": "<PERSON>i<PERSON><PERSON> e<PERSON>", "wifiAuthenticationTeachin": "Gerät in WiFi einlernen", "wifiAuthenticationEstablish": "Verbindung zum Gerät herstellen", "wifiAuthenticationEstablishLong": "Gerät verbindet sich mit WiFi {ssid}", "wifiAuthenticationFailed": "Verbindung fehlgeschlagen. Trenne das Gerät für einige Sekunden vom Strom und verbinde es erneut.", "wifiAuthenticationReset": "Authentifizierung zurücksetzen", "wifiAuthenticationResetHint": "Alle Authentifizierungsdaten werden gelöscht.", "wifiAuthenticationInvaliddata": "Authentifizierungsdaten ungültig", "wifiAuthenticationReauthenticate": "Erneut authentifizieren", "wifiAddhkdeviceHeader": "Ger<PERSON> hinzufügen", "wifiAddhkdeviceDescription": "Verbinde dein neues ELTAKO-Gerät über die Apple Home App mit deinem WiFi.", "wifiAddhkdeviceStep1": "1. Öffne die Apple Home App.", "wifiAddhkdeviceStep2": "2. <PERSON><PERSON><PERSON> oben rechts in der App auf das Plus und wähle **Gerät hinzufügen**.", "wifiAddhkdeviceStep3": "3. Folge den Anweisungen der App.", "wifiAddhkdeviceStep4": "4. <PERSON><PERSON> kann dein Gerät in der ELTAKO-Connect App konfiguriert werden.", "detailsConfigurationRuntime": "Laufzeit", "detailsConfigurationRuntimeMode": "Modus", "generalTextManually": "<PERSON><PERSON>", "detailsConfigurationRuntimeAutoDescription": "Der Beschattungsaktor bestimmt die Laufzeit des Beschattungsmotors selbständig bei jeder <PERSON><PERSON><PERSON> von der unteren zur oberen Endlage (empfohlen).\nNach Inbetriebnahme oder Änderungen sollte eine Fahrt von unten nach oben ohne Unterbrechung durchgeführt werden.", "detailsConfigurationRuntimeManuallyDescription": "Die Laufzeit des Beschattungsmotors wird manuell über die Dauer unten eingestellt.\nBitte achte darauf, dass die eingestellte Laufzeit der tatsächlichen Laufzeit deines Beschattungsmotors entspricht.\nNach Inbetriebnahme oder Änderungen sollte eine Fahrt von unten nach oben ohne Unterbrechung durchgeführt werden.", "detailsConfigurationRuntimeDemoDescription": "Der LCD-Display-Modus ist nur via REST API verfügbar.", "generalTextDemomodeActive": "Demo-Modus aktiv", "detailsConfigurationRuntimeDuration": "<PERSON><PERSON>", "detailsConfigurationSwitchesGs4": "Gruppenschalter mit Tipp-Wendefunktion (GS4)", "detailsConfigurationSwitchesGs4Description": "Gruppenschalter mit Tipp-Wendefunktion zur Steuerung von Jalousien", "screenshotSu12": "<PERSON><PERSON><PERSON><PERSON>", "screenshotS2U12": "<PERSON><PERSON><PERSON><PERSON>", "screenshotMfz12": "<PERSON><PERSON><PERSON>", "screenshotEsr62": "Lam<PERSON>", "screenshotEud62": "Deckenleuchte", "screenshotEsb62": "<PERSON><PERSON><PERSON>", "detailsConfigurationEdgemodeLeadingedgeDescription": "LC1-LC3 sind Comfort-Stellungen mit verschiedenen Dimmkurven für dimmbare 230 V-LED-Lampen, welche sich auf AUTO konstruktionsbedingt nicht weit genug abdimmen lassen und daher auf Phasenanschnitt gezwungen werden müssen.", "detailsConfigurationEdgemodeAutoDescription": "AUTO lässt das Dimmen aller Lampenarten zu.", "detailsConfigurationEdgemodeTrailingedge": "Phasenabschnitt", "detailsConfigurationEdgemodeTrailingedgeDescription": "LC4-LC6 sind Comfort-Stellungen mit verschiedenen Dimmkurven für dimmbare 230 V-LED-Lampen.", "updateHeader": "Firmware Update", "updateTitleStepSearch": "Update wird gesucht", "updateTitleStepFound": "Ein Update wurde gefunden", "updateTitleStepDownload": "Update wird her<PERSON><PERSON><PERSON><PERSON>n", "updateTitleStepInstall": "Update wird installiert", "updateTitleStepSuccess": "Update er<PERSON><PERSON>g<PERSON>ich", "updateTitleStepUptodate": "Bereits auf dem neuesten Stand", "updateTitleStepFailed": "Update fehlgeschlagen", "updateButtonSearch": "<PERSON>ch Updates suchen", "updateButtonInstall": "Update installieren", "updateCurrentversion": "Aktuelle Version", "updateNewversion": "Neues Firmware Update verfügbar", "updateHintPower": "Das Update startet nur, wenn der Ausgang des Gerätes nicht aktiv ist. Das Gerät darf während des Updates nicht vom Stromnetz getrennt werden und die App nicht verlassen werden!", "updateButton": "Update", "updateHintCompatibility": "Ein Update wird em<PERSON><PERSON><PERSON>, da sonst einige Funktionen in der App nur eingeschränkt zur Verfügung stehen.", "generalTextDetails": "Details", "updateMessageStepMetadata": "Lade Update Informationen", "updateMessageStepPrepare": "Update wird vorbereitet", "updateTitleStepUpdatesuccessful": "Update wird gepr<PERSON>ft", "updateTextStepFailed": "Le<PERSON> ist beim Update etwas schief gelaufen, probiere es in ein paar <PERSON>uten erneut oder warte bis sich dein Gerät automatisch aktualisiert (Internetverbindung erforderlich).", "configurationsNotavailable": "Es sind noch keine Konfigurationen vorhanden", "configurationsAddHint": "Lege neue Konfigurationen an, indem du dich mit einem Gerät verbindest und eine Konfiguration speicherst.", "@configurationsAddHint": {"description": "Now available for all devices not only Bluetooth", "type": "text"}, "configurationsEdit": "Konfiguration bearbeiten", "generalTextName": "Name", "configurationsDelete": "Konfiguration löschen", "configurationsDeleteHint": "Soll die Konfiguration: {configName} wirklich gelöscht werden?", "configurationsSave": "Konfiguration speichern", "configurationsSaveHint": "Hier kannst du die Konfiguration deines aktuellen Geräts speichern, oder eine bereits gespeicherte Konfiguration laden.", "configurationsImport": "Konfiguration importieren", "configurationsImportHint": "Soll die Konfiguration {configName} wirklich übertragen werden?", "generalTextConfigurations": "{count, plural, one {Konfiguration} other {Konfigurationen}}", "configurationsStepPrepare": "Konfiguration wird vorbereitet", "configurationsStepName": "G<PERSON>e einen Namen für die Konfiguration ein.", "configurationsStepSaving": "Konfiguration wird ges<PERSON><PERSON>rt", "configurationsStepSavedsuccessfully": "Konfiguration wurde erfolgreich gespeichert", "configurationsStepSavingfailed": "Konfiguration speichern fehlgeschlagen", "configurationsStepChoose": "<PERSON><PERSON>hle eine Konfiguration", "configurationsStepImporting": "Konfiguration wird importiert", "configurationsStepImportedsuccessfully": "Konfiguration wurde erfolgreich importiert", "configurationsStepImportingfailed": "Konfiguration importieren fehlgeschlagen", "discoveryAssuDescription": "Außensteckdosen Schaltuhr Bluetooth 230V", "settingsDatetimeDevicetime": "Aktuelle Gerätezeit", "settingsDatetimeLoading": "Zeiteinstellungen werden geladen", "discoveryEud12Description": "Universal-Dimmschalter Bluetooth", "generalTextOffdelay": "Rückfallverzögerung", "generalTextRemainingbrightness": "Resthelligkeit", "generalTextSwitchonvalue": "Einschaltwert", "motionsensorTitleNoremainingbrightness": "<PERSON><PERSON>", "motionsensorTitleAlwaysremainingbrightness": "Mit Resthelligkeit", "motionsensorTitleRemainingbrightnesswithprogram": "Resthelligkeit über Schaltprogramm", "motionsensorTitleRemainingbrightnesswithprogramandzea": "Resthelligkeit über ZE und ZA", "motionsensorTitleNoremainingbrightnessauto": "<PERSON><PERSON> (Halbautomatik)", "generalTextMotionsensor": "Bewegungsmelder", "generalTextLightclock": "<PERSON><PERSON><PERSON><PERSON>", "generalTextSnoozeclock": "Schlummerschaltung", "generalDescriptionLightclock": "<PERSON><PERSON> ({mode}) wird nach ca. 1 Sekunde mit kleinster Helligkeit eingeschaltet und langsam hochgedimmt, ohne die zuletzt gespeicherte Helligkeitsstufe zu verändern.", "generalDescriptionSnoozeclock": "<PERSON><PERSON> ({mode}) wird die Beleuchtung von der aktuellen Dimmstellung bis zur Mindesthelligkeit abgedimmt und ausgeschaltet. Durch kurzes Tasten kann während des Abdimmvorgangs jederzeit ausgeschaltet werden. Ein langes Tasten während des Abdimmvorgangs dimmt hoch und beendet die Schlummerschaltung.", "generalTextImmediately": "Sofort", "generalTextPercentage": "Prozent", "generalTextSwitchoffprewarning": "<PERSON>ss<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "generalDescriptionSwitchoffprewarning": "Langsames Abdimmen bis zur minimalen Helligkeit.", "generalDescriptionOffdelay": "<PERSON>im Anlegen der Steuerspannung schaltet das Gerät ein. Wenn die Steuerspannung unterbrochen wird, beginnt der Zeitablauf, nach dem das Gerät ausschaltet. Während des Zeitablaufs kann das Gerät nachgeschaltet werden.", "generalDescriptionBrightness": "Die Helligkeit, mit der die Lampe vom Dimmer eingeschaltet wird.", "generalDescriptionRemainingbrightness": "Der Dimmwert in Prozent, auf den die Lampe nach Abschaltung des Bewegungsmelders gedimmt wird.", "generalDescriptionRuntime": "Laufzeit der Lichtweckerfunktion von Minimalhelligkeit bis zur Maximalhelligkeit.", "generalTextUniversalbutton": "Universaltaster", "generalTextDirectionalbutton": "Richtungstaster", "eud12DescriptionAuto": "Automatische Erkennung UT/RT (mit Richtungstasterdiode RTD)", "eud12DescriptionRt": "mit Richtungstasterdiode RTD", "generalTextProgram": "Programm", "eud12MotionsensorOff": "Bei Bewegungsmelder auf Aus", "eud12ClockmodeTitleProgramze": "Programm und ZE", "eud12ClockmodeTitleProgramza": "Programm und ZA", "eud12ClockmodeTitleProgrambuttonon": "Programm und UT/RT Ein", "eud12ClockmodeTitleProgrambuttonoff": "Programm und UT/RT Aus", "eud12TiImpulseTitle": "Impulszeit Ein (t1)", "eud12TiImpulseHeader": "Dimmwert Impulszeit Ein", "eud12TiImpulseDescription": "Der Dimmwert in Prozent, auf den die Lampe bei Impulszeit EIN gedimmt wird.", "eud12TiOffTitle": "Impulszeit Aus (t2)", "eud12TiOffHeader": "Dimmwert Impulszeit Aus", "eud12TiOffDescription": "Der Dimmwert in Prozent, auf den die Lampe bei Impulszeit AUS gedimmt wird.", "generalTextButtonpermanentlight": "Tasterdauerlicht", "generalDescriptionButtonpermanentlight": "Einstellung des Tasterdauerlichts von 0 bis 10 Stunden in 0,5-Stunden-Schritten. Aktivierung durch Tastendruck länger als 1 Sekunde (1xFlackern), Deaktivierung durch Tastendruck länger als 2 Sekunden.", "generalTextNobuttonpermanentlight": "<PERSON><PERSON>", "generalTextBasicsettings": "Grundeinstellungen", "generalTextInputswitch": "<PERSON><PERSON><PERSON><PERSON> (A1)", "generalTextOperationmode": "<PERSON><PERSON><PERSON><PERSON>", "generalTextDimvalue": "Einschaltverhalten", "eud12TitleUsememory": "Memorywert verwenden", "eud12DescriptionUsememory": "Der Memorywert entspricht dem zuletzt eingestellten Dimmwert. Wird der Memorywert deaktiviert, wird immer auf den Einschaltwert gedimmt.", "generalTextStartup": "Einschalthelligkeit", "generalDescriptionSwitchonvalue": "Der Einschaltwert ist ein einstellbarer Helligkeitswert, der das sichere Einschalten garantiert.", "generalTitleSwitchontime": "Einschaltzeit", "generalDescriptionSwitchontime": "Nach Ablauf der Einschaltzeit wird die Lampe vom Einschaltwert zum Memorywert gedimmt.", "generalDescriptionStartup": "Einige LED-Leuchtmittel benötigen einen höheren Einschaltstrom, um zuverlässig einzuschalten. Die Lampe wird auf diesem Einschaltwert eingeschaltet und anschließend nach der Einschaltzeit zum Memorywert gedimmt.", "eud12ClockmodeSubtitleProgramze": "Kurzklick auf Zentral Ein", "eud12ClockmodeSubtitleProgramza": "Kurzklick auf Zentral Aus", "eud12ClockmodeSubtitleProgrambuttonon": "Doppelklick auf Universaltaster/Richtungstaster Ein", "eud12ClockmodeSubtitleProgrambuttonoff": "Doppelklick auf Universaltaster/Richtungstaster Aus", "eud12FunctionStairlighttimeswitchTitleShort": "TLZ | Treppenlichtzeitschalter", "eud12FunctionMinTitleShort": "MIN", "eud12FunctionMmxTitleShort": "MMX", "eud12FunctionTiDescription": "Taktgeber mit einstellbarer Einschalt- und Ausschaltzeit von 0,5 Sekunden bis 9,9 Minuten. Die Helligkeit kann von Mindesthelligkeit bis Maximalhelligkeit eingestellt werden.", "eud12FunctionAutoDescription": "Universal-Dimmschalter mit Einstellung für Bewegungsmelder, Lichtwecker und Schlummerschaltung.", "eud12FunctionErDescription": "<PERSON><PERSON><PERSON><PERSON>, die Helligkeit kann von Mindesthelligkeit bis Maximalhelligkeit eingestellt werden.", "eud12FunctionEsvDescription": "Universal-Dimmschalter mit Einstellung einer Rückfallverzögerung von 1 bis 120 Minuten. Ausschaltvorwarnung am Ende durch Abdimmen wählbar und einstellbar von 1 bis 3 Minuten. Beide Zentraleingänge aktiv.", "eud12FunctionTlzDescription": "Einstellung des Tasterdauerlichts von 0 bis 10 Stunden in 0,5-Stunden-Schritten. Aktivierung durch Tastendruck länger als 1 Sekunde (1xFlackern), Deaktivierung durch Tastendruck länger als 2 Sekunden.", "eud12FunctionMinDescription": "Universal-<PERSON><PERSON><PERSON><PERSON><PERSON>, schaltet bei dem Anlegen der Steuerspannung auf die eingestellte Mindesthelligkeit. In der eingestellten Dimmzeit von 1 bis 120 Minuten wird zur Maximalhelligkeit gedimmt. Beim Wegnehmen der Steuerspannung wird sofort ausgeschaltet, auch während der Dimmzeit. Beide Zentraleingänge aktiv.", "eud12FunctionMmxDescription": "Universal-<PERSON><PERSON><PERSON><PERSON><PERSON>, schaltet bei dem Anlegen der Steuerspannung auf die eingestellte Mindesthelligkeit. In der eingestellten Dimmzeit von 1 bis 120 Minuten wird zur Maximalhelligkeit gedimmt. Beim Wegnehmen der Steuerspannung wird jedoch bis zur eingestellten Mindesthelligkeit abgedimmt. Danach wird ausgeschaltet. Beide Zentraleingänge aktiv.", "motionsensorSubtitleNoremainingbrightness": "Bei Bewegungsmelder auf Aus", "motionsensorSubtitleAlwaysremainingbrightness": "Bei Bewegungsmelder auf Aus", "motionsensorSubtitleRemainingbrightnesswithprogram": "Schaltprogramm aktiviert und deaktiviert bei BM-Aus", "motionsensorSubtitleRemainingbrightnesswithprogramandzea": "ZE aktiviert BM, ZA deaktiviert BM, sowie per Schaltprogramm", "motionsensorSubtitleNoremainingbrightnessauto": "Bewegungsmelder schaltet nur Aus", "detailsDimsectionHeader": "<PERSON><PERSON><PERSON>", "generalTextFast": "<PERSON><PERSON><PERSON>", "generalTextSlow": "Langsam", "eud12TextDimspeed": "Dimmgeschwindigkeit", "eud12TextSwitchonspeed": "Einschaltgeschwindigkeit", "eud12TextSwitchoffspeed": "Ausschaltgeschwindigkeit", "eud12DescriptionDimspeed": "Die Dimmgeschwindigkeit ist die Geschwindigkeit, mit der der Dimmer von der aktuellen Helligkeit zur Zielhelligkeit dimmt.", "eud12DescriptionSwitchonspeed": "Die Einschaltgeschwindigkeit ist die Geschwindigkeit, die der Dimmer benötigt, um vollständig einzuschalten.", "eud12DescriptionSwitchoffspeed": "Die Ausschaltgeschwindigkeit ist die Geschwindigkeit, die der Dimmer benötigt, um vollständig auszuschalten.", "settingsFactoryresetResetdimHeader": "Dimm Einstellungen zurücksetzen", "settingsFactoryresetResetdimDescription": "<PERSON><PERSON> wirklich alle Dimm Einstellungen zurückgesetzt werden?", "settingsFactoryresetResetdimConfirmationDescription": "Dimm Einstellungen wurden erfolgreich zurückgesetzt", "eud12TextSwitchonoffspeed": "Ein-/Ausschaltgeschwindigkeit", "eud12DescriptionSwitchonoffspeed": "Die Ein-/Ausschaltgeschwindigkeit ist die Geschwindigkeit, die der Dimmer benötigt, um vollständig ein- bzw. auszuschalten.", "timerDetailsDimtoval": "Ein mit Dimmwert in %", "timerDetailsDimtovalDescription": "Der Dimmer schaltet immer mit dem festen Dimmwert in % Ein.", "timerDetailsDimtovalSubtitle": "Einschalten mit {brightness}%", "timerDetailsDimtomem": "Ein mit Memorywert", "timerDetailsDimtomemSubtitle": "Einschalten mit Memorywert", "timerDetailsMotionsensorwithremainingbrightness": "Resthelligkeit (BM) Ein", "timerDetailsMotionsensornoremainingbrightness": "Resthelligkeit (BM) Aus", "settingsRandommodeHint": "Bei eingeschaltetem Zufallsmodus werden alle Schaltzeitpunkte des Kanals zufällig verschoben. Bei Einschaltzeiten bis zu 15 Minuten früher und Ausschaltzeiten bis zu 15 Minuten später.", "runtimeOffsetDescription": "Zusätzlicher Nachlauf, nach Ablauf der Fahrtdauer. Dieser kann verwendet werden um ein erreichen der Endlage sicherzustellen.", "loadingTextDimvalue": "<PERSON><PERSON><PERSON>t wird geladen", "discoveryEudipmDescription": "Universal-Dimmschalter IP Matter", "generalTextOffset": "<PERSON><PERSON><PERSON><PERSON>", "eud12DimvalueTestText": "Helligkeit senden", "eud12DimvalueTestDescription": "<PERSON><PERSON> wird die aktuell eingestellte Dimgeschwindigkeit berücksichtigt.", "eud12DimvalueLoadText": "Helligkeit laden", "settingsDatetimeNotime": "Die Datum und Uhrzeit Einstellungen müssen über das Gerätedisplay ausgelesen werden.", "generalMatterText": "Matter", "generalMatterMessage": "Bitte lerne dein Matter-Gerät mithilfe einer der folgenden Apps ein.", "generalMatterOpengooglehome": "Google Home öffnen", "generalMatterOpenamazonalexa": "Amazon Alexa öffnen", "generalMatterOpensmartthings": "SmartThings <PERSON>", "generalLabelProgram": "Programm {number}", "generalTextDone": "<PERSON><PERSON><PERSON>", "settingsRandommodeDescriptionShort": "Bei eingeschaltetem Zufallsmodus werden alle Schaltzeitpunkte des Kanals zufällig verschoben. Bei Einschaltzeiten bis zu 15 Minuten früher und Ausschaltzeiten bis zu 15 Minuten später.", "all": "Alle", "discoveryBluetooth": "Bluetooth", "success": "Erfolgreich", "error": "<PERSON><PERSON>", "timeProgramAdd": "Zeitprogramm hinzufügen", "noConnection": "<PERSON><PERSON>", "timeProgramOnlyActive": "Konfigurierte Programme", "timeProgramAll": "Alle Programme", "active": "Aktiv", "inactive": "Inaktiv", "timeProgramSaved": "Programm {number} ges<PERSON><PERSON>rt", "deviceLanguageSaved": "Gerätesprache gespeichert", "generalTextTimeShort": "{time} <PERSON>r", "programDeleteHint": "Soll Programm {index} wirklich gelöscht werden?", "milliseconds": "{count, plural, one {Millisekunde} other {Millisekunden}}", "millisecondsWithValue": "{count, plural, one {{count} Millisekunde} other {{count} Millisekunden}}", "secondsWithValue": "{count, plural, one {{count} Sekunde} other {{count} Sekunden}}", "minutesWithValue": "{count, plural, one {{count} Minute} other {{count} Minuten}}", "hoursWithValue": "{count, plural, one {{count} Stunde} other {{count} St<PERSON><PERSON>}}", "settingsPinFailEmpty": "Die PIN darf nicht leer sein.", "detailsConfigurationWifiloginScanNoMatch": "Der gescannte Code passt nicht zum Gerät.", "wifiAuthorizationPopIsEmpty": "PoP darf nicht leer sein.", "wifiAuthenticationCredentialsHint": "Da die App nicht auf dein privates WLAN-Passwort zugreifen kann, lässt sich die Richtigkeit der Eingabe nicht prüfen. Sollte keine Verbindung zustande kommen, überprüfe das Passwort und gebe es erneut ein.", "generalMatterOpenApplehome": "Apple Home öffnen", "timeProgramNoActive": "<PERSON>ine konfigurierten Programme.", "timeProgramNoEmpty": "<PERSON><PERSON> freies Zeitprogramm verfügbar.", "nameOfConfiguration": "Name der Konfiguration", "currentDevice": "Aktuelles Gerät", "export": "Exportieren", "import": "Importieren", "savedConfigurations": "Gespeicherte Konfigurationen", "importableServicesLabel": "Folgende Einstellungen können importiert werden:", "notImportableServicesLabel": "Inkompatible Einstellungen:", "deviceCategoryMeterGateway": "Zähler-Gateway", "deviceCategory2ChannelTimeSwitch": "2-Kanal-Zeitschaltuhr Bluetooth", "devicategoryOutdoorTimeSwitchBluetooth": "Außensteckdosen-Zeitschaltuhr Bluetooth", "settingsModbusHeader": "Modbus", "settingsModbusDescription": "Passe die Baudrate, Parität und den Timeout an, um die Übertragungsgeschwindigkeit, Fehlererkennung und Wartezeit zu konfigurieren.", "settingsModbusRTU": "Modbus RTU", "settingsModbusBaudrate": "Baudrate", "settingsModbusParity": "Parität", "settingsModbusTimeout": "Modbus Timeout", "locationServiceDisabled": "Standort ist deaktiviert", "locationPermissionDenied": "Bitte erlaube die Standort Berechtigung, um deine aktuelle Position abzurufen.", "locationPermissionDeniedPermanently": "Die Standortberechtigung ist dauerhaft verweigert. Bitte erlaube die Standortberechtigung in deinen Geräteeinstellungen, um deine aktuelle Position abzurufen.", "lastSync": "Zuletzt synchronisiert", "dhcpActive": "DHCP aktiv", "ipAddress": "IP", "subnetMask": "Subnetzmaske", "standardGateway": "Standard Gateway", "dns": "DNS", "alternateDNS": "Alternativen DNS", "errorNoNetworksFound": "<PERSON><PERSON> WiFi Netzwerke gefunden", "availableNetworks": "Verfügbare Netzwerke", "enableWifiInterface": "WiFi Schnittstelle aktivieren", "enableLANInterface": "LAN Schnittstelle aktivieren", "hintDontDisableAllInterfaces": "<PERSON><PERSON> sicher, dass nicht alle Schnittstellen deaktiviert sind. Die zuletzt aktivierte Schnittstelle hat Priorität.", "ssid": "SSID", "searchNetworks": "WiFi Netzwerke suchen", "errorNoNetworkEnabled": "Mindestens eine Schnittstelle muss aktiviert sein.", "errorActiveNetworkInvalid": "Nicht alle aktiven Stationen sind gültig.", "invalidNetworkConfiguration": "Ungültige Netzwerkkonfiguration", "generalDefault": "Standard", "mqttHeader": "MQTT", "mqttConnected": "Verbindung zum MQTT-Broker", "mqttDisconnected": "<PERSON><PERSON> Verbindung zum MQTT-Broker.", "mqttBrokerURI": "Broker <PERSON>", "mqttBrokerURIHint": "MQTT-Broker U<PERSON>", "mqttPort": "Port", "mqttPortHint": "MQTT-Port", "mqttClientId": "Client-ID", "mqttClientIdHint": "MQTT Client-ID", "mqttUsername": "<PERSON><PERSON><PERSON><PERSON>", "mqttUsernameHint": "MQTT-Benutzername", "mqttPassword": "Password", "mqttPasswordHint": "MQTT-Password", "mqttCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON> (optional)", "mqttCertificateHint": "MQTT-Zertifikat", "mqttTopic": "Topic", "mqttTopicHint": "MQTT-Topic", "electricityMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "electricityMeterCurrent": "Aktuell", "electricityMeterHistory": "<PERSON><PERSON><PERSON><PERSON>", "electricityMeterReading": "Zählerstand", "connectivity": "Konnektivität", "electricMeter": "{count, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "discoveryZGW16Description": "Modbus-Stromzähler-MQTT-Gateway", "bluetoothConnectionLost": "Bluetooth Verbindung verloren.", "bluetoothConnectionLostDescription": "Die Bluetooth Verbindung zum Gerät wurde unterbrochen. <PERSON>te stelle sicher, dass das Gerät in Reichweite ist.", "openBluetoothSettings": "Einstellungen öffnen", "password": "Passwort", "setInitialPassword": "Initiales Passwort vergeben", "initialPasswordMinimumLength": "Das Passwort muss mindestens {length} <PERSON><PERSON><PERSON> lang sein.", "repeatPassword": "Passwort wiederholen", "passwordsDoNotMatch": "Die Passwörter stimmen nicht überein.", "savePassword": "Passwort speichern", "savePasswordHint": "Das Passwort wird für zukünftige Anmeldungen auf deinem Gerät gespeichert.", "retrieveNtpServer": "Zeit von NTP-Server abrufen", "retrieveNtpServerFailed": "Die Verbindung zum NTP-Server konnte nicht hergestellt werden.", "retrieveNtpServerSuccess": "Die Verbindung zum NTP-Server war erfolgreich.", "settingsPasswordNewPasswordDescription": "Neues Passwort eingeben", "settingsPasswordConfirmationDescription": "Passwort ändern erfolgreich", "dhcpRangeStart": "DHCP Startbereich", "dhcpRangeEnd": "DHCP Endbereich", "forwardOnMQTT": "Weiterleitung auf MQTT", "showAll": "Alle anzeigen", "hide": "Ausblenden", "changeToAPMode": "Wechseln in den AP-Modus", "changeToAPModeDescription": "Du bist dabei dein Gerät mit einem WiFi Netzwerk zu verbinden. In diesem Fall wird die Verbindung zum Gerät getrennt und du musst dich erneut über das konfigurierte Netzwerk mit deinem Gerät verbinden.", "consumption": "<PERSON><PERSON><PERSON><PERSON>", "currentDay": "Aktueller Tag", "twoWeeks": "2 Wochen", "oneYear": "1 Jahr", "threeYears": "3 Jahre", "passwordMinLength": "Das Passwort muss mindestens {length} <PERSON><PERSON><PERSON> lang sein.", "passwordNeedsLetter": "Das Passwort muss mindestens einen Buchstaben enthalten.", "passwordNeedsNumber": "Das Passwort muss mindestens eine Zahl enthalten.", "portEmpty": "Port darf nicht leer sein", "portInvalid": "Ungültiger Port", "portOutOfRange": "Port muss zwischen {rangeStart} und {rangeEnd} sein.", "ipAddressEmpty": "IP-<PERSON><PERSON><PERSON> darf nicht leer sein.", "ipAddressInvalid": "Ungültige IP-Adresse", "subnetMaskEmpty": "Subnetzmaske darf nicht leer sein.", "subnetMaskInvalid": "Ungültige Subnetzmaske", "gatewayEmpty": "Gateway darf nicht leer sein.", "gatewayInvalid": "Ungültiges Gateway", "dnsEmpty": "DNS darf nicht leer sein.", "dnsInvalid": "Ungültiger DNS", "uriEmpty": "URI darf nicht leer sein.", "uriInvalid": "Ungültige URI", "electricityMeterChangedSuccessfully": "Stromzähler wurde erfolgreich geändert.", "networkChangedSuccessfully": "Netzwerk Konfiguration wurde erfolgreich geändert.", "mqttChangedSuccessfully": "MQTT-Konfiguration wurde erfolgreich geändert.", "modbusChangedSuccessfully": "Modbus Einstellungen wurden erfolgreich geändert.", "loginData": "Login Daten löschen", "valueConfigured": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "electricityMeterHistoryNoData": "<PERSON><PERSON> verfüg<PERSON>.", "locationChangedSuccessfully": "Standort wurde erfolgreich geändert.", "settingsNameFailEmpty": "Name darf nicht leer sein.", "settingsNameFailLength": "Der Name darf nicht länger als {length} <PERSON><PERSON><PERSON> sein.", "solsticeChangedSuccesfully": "Zeitversatz Sonnenwende wurde erfolgreich geändert.", "relayFunctionChangedSuccesfully": "Relaisfunktion wurde erfolgreich geändert.", "relayFunctionHeader": "Relaisfunktion", "dimmerValueChangedSuccesfully": "Einschaltverhalten wurde erfolgreich geändert.", "dimmerBehaviourChangedSuccesfully": "Dimmverhalten wurde erfolgreich geändert.", "dimmerBrightnessDescription": "Die Mindest- und Maximalhelligkeit hat Auswirkung auf alle einstellbaren Helligkeiten des Dimmers.", "dimmerSettingsChangedSuccesfully": "Grundeinstellungen wurden erfolgreich geändert.", "liveUpdateEnabled": "Live Test aktiviert", "liveUpdateDisabled": "Live Test deaktiviert", "liveUpdateDescription": "Der zuletzt geänderte Slider-Wert wird an das Gerät gesendet.", "demoDevices": "<PERSON><PERSON> Ger<PERSON>", "showDemoDevices": "Demo Geräte anzeigen", "deviceCategoryTimeSwitch": "Zeitschaltuhr", "deviceCategoryMultifunctionalRelay": "Multifunktions-Zeitrelais", "deviceCategoryDimmer": "<PERSON><PERSON>", "deviceCategoryShutter": "Rolladen- und Beschattungsaktor", "deviceCategoryRelay": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "configurationsHeader": "Konfigurationen", "configurationsDescription": "Hier kannst du deine gespeicherten Konfigurationen verwalten.", "configurationsNameFailEmpty": "Name der Konfiguration darf nicht leer sein.", "configurationDeleted": "Konfiguration gelöscht", "codeFound": "{codeType} Code erkannt", "errorCameraPermission": "Bitte erlaube den Zugriff auf die Kamera, um den ELTAKO-Code zu scannen.", "authorizationSuccessful": "Erfolgreich am Gerät autorisiert", "wifiAuthenticationResetConfirmationDescription": "Authentifizierung wurde erfolgreich zurückgesetzt.", "settingsResetConnectionHeader": "Verbindung zurücksetzen", "settingsResetConnectionDescription": "Soll die Verbindung wirklich zurückgesetzt werden?", "settingsResetConnectionConfirmationDescription": "Verbindung wurde erfolgreich zurückgesetzt.", "wiredInputChangedSuccesfully": "Tasterverhalten wurde erfolgreich geändert.", "runtimeChangedSuccesfully": "Laufzeit wurde erfolgreich geändert.", "expertModeActivated": "Experten-Modus aktiviert", "expertModeDeactivated": "Experten-<PERSON><PERSON>", "license": "<PERSON><PERSON><PERSON>", "retry": "<PERSON><PERSON><PERSON> versuchen", "provisioningConnectingHint": "Verbindung zum Gerät wird hergestellt. Dies kann bis zu 1 Minute dauern.", "serialnumberEmpty": "<PERSON><PERSON><PERSON>mer darf nicht leer sein.", "interfaceStateInactiveDescriptionBLE": "Bluetooth ist deaktiviert. Bitte aktiviere es, um Bluetooth Geräte zu finden.", "interfaceStateDeniedDescriptionBLE": "Bluetooth Berechtigungen wurden nicht erteilt.", "interfaceStatePermanentDeniedDescriptionBLE": "Bluetooth Berechtigungen wurden nicht erteilt. Bitte erlaube Bluetooth Verbindungen in deinen Geräte Einstellungen.", "requestPermission": "Berechtigung anfordern", "goToSettings": "Zu den Einstellungen", "enableBluetooth": "Bluetooth aktivieren", "installed": "Installiert", "teachInDialogDescription": "Möchtest du dein Gerät via {type} einlernen?", "useMatter": "<PERSON> verwenden", "relayMode": "Relaismodus aktivieren", "whatsNew": "Neu in dieser Version", "migrationHint": "Damit du die neuen Funktionen nutzen kannst, müssen wir deine Daten migrieren.", "migrationHeader": "Migration", "migrationProgress": "Wir räumen auf...", "letsGo": "<PERSON> geht's", "noDevicesFound": "<PERSON>ine Geräte gefunden. Überprüfe ob sich dein Gerät im Pairing-Modus befindet.", "interfaceStateEmpty": "<PERSON><PERSON> G<PERSON> gefunden", "ssidEmpty": "SSID darf nicht leer sein", "passwordEmpty": "Password darf nicht leer sein", "settingsDeleteSettingsHeader": "Einstellungen zurücksetzen", "settingsDeleteSettingsDescription": "<PERSON><PERSON> wirklich alle Einsetllungen zurückgesetzt werden?", "settingsDeleteSettingsConfirmationDescription": "Alle Einstellungen wurden erfolgreich zurückgesetzt.", "locationNotFound": "Standort nicht gefunden", "timerProgramEmptySaveHint": "Das Zeitprogramm ist leer und kann nicht gespeichert werden. Bearbeitung beenden?", "timerProgramDaysEmptySaveHint": "Keine Tage ausgewählt. Das Zeitprogramm trotzdem speichern?", "timeProgramNoDays": "Ein Programm ohne aktive Tage kann nicht aktiviert werden.", "timeProgramColliding": "Zeitprogramm kollidiert mit Programm {program}.", "timeProgramDuplicated": "Das Zeitprogramm ist ein Duplikat von Programm {program}.", "screenshotZgw16": "Einfamilienhaus", "interfaceStateUnknown": "<PERSON><PERSON> G<PERSON> gefunden", "settingsPinChange": "PIN <PERSON>", "timeProgrammOneTime": "Ein<PERSON>ig", "timeProgrammRepeating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "generalIgnore": "Ignorieren", "timeProgramChooseDay": "Tag wählen", "generalToday": "<PERSON><PERSON>", "generalTomorrow": "<PERSON><PERSON>", "bluetoothAndPINChangedSuccessfully": "Bluetooth und PIN wurden erfolgreich geändert.", "generalTextDimTime": "Dimmzeit", "discoverySu62Description": "1-<PERSON><PERSON>uhr Bluetooth", "bluetoothAlwaysOnTitle": "<PERSON><PERSON><PERSON><PERSON>", "bluetoothAlwaysOnDescription": "Bluetooth ist dauerhaft aktiviert.", "bluetoothAlwaysOnHint": "Hinweis: Wen<PERSON> diese Einstellung aktiviert wird, ist das Gerät über Bluetooth permanent für jeden sichtbar! Es wird empfohlen den Standart PIN zu ändern.", "bluetoothManualStartupOnTitle": "Zeitweilig-An", "bluetoothManualStartupOnDescription": "Nach anlegen der Spannung wird Bluetooth für 3 Minuten aktiviert.", "bluetoothManualStartupOnHint": "Hinweis: Die Kopplungsbereitschaft ist für 3 Minuten aktiviert und schaltet sich anschließend aus. Wenn eine neue Verbindung aufgebaut werden soll, muss der Taster für circa 5 Sekunden gedrückt gehalten werden.", "bluetoothManualStartupOffTitle": "Manuell-An", "bluetoothManualStartupOffDescription": "Bluetooth wird manuell über den Tasteingang aktiviert und ist dann für 3 Minuten aktiv.", "bluetoothManualStartupOffHint": "Hin<PERSON>s: Um Bluetooth zu aktivieren, muss der Taster am Tasteingang für ca. 5 Sekunden gedrückt gehalten werden.", "timeProgrammOneTimeRepeatingDescription": "Programme können entweder wiederholt ausgeführt werden, indem sie an den konfigurierten Tagen und Zeiten immer einen Schaltvorgang ausführen, oder sie werden nur einmalig zum konfigurierten Schaltzeitpunkt ausgeführt.", "versionHeader": "Version {version}", "releaseNotesHeader": "Release Notes", "release30Header": "Die neue ELTAKO Connect-App ist da!", "release30FeatureDesignHeader": "Neues Design", "release30FeatureDesignDescription": "Die App wurde komplett überarbeitet und erstrahlt in einem neuen Design. Die Bedienung ist jetzt noch einfacher und intuitiver.", "release30FeaturePerformanceHeader": "Verbesserte Performance", "release30FeaturePerformanceDescription": "Genieße eine flüssigere Erfahrung und reduzierte Ladezeiten – für ein besseres Nutzererlebnis.", "release30FeatureConfigurationHeader": "Geräteübergreifende Konfigurationen", "release30FeatureConfigurationDescription": "Speichere Gerätekonfigurationen und übertrage diese auf andere Geräte. Sel<PERSON>t wenn diese nicht die gleiche Hardware haben, kannst du z.B. die Konfiguration deiner S2U12DBT1+1-UC auf eine ASSU-BT übertragen oder andersherum.", "release31Header": "Die neue Unterputz 1-<PERSON><PERSON> Schaltuhr mit Bluetooth ist da!", "release31Description": "Was kann die SU62PF-BT/UC?", "release31SU62Name": "SU62PF-BT/UC", "release31DeviceNote1": "Bis zu 60 Schaltprogramme.", "release31DeviceNote2": "Die Uhr kann Geräte nach festen Zeiten oder über die Astro-Funktion basierend auf Sonnenaufgang und Sonnenuntergang schalten.", "release31DeviceNote3": "Zufallsmodus: Schaltzeiten können zufällig um bis zu 15 Minuten verschoben werden.", "release31DeviceNote4": "Sommer-/Winterzeitumstellung: Die Uhr stellt automatisch auf Sommer- bzw. Winterzeit um.", "release31DeviceNote5": "Universelle Versorgungs- und Steuerspannung 12-230V UC.", "release31DeviceNote6": "Tastereingang für manuelles Schalten.", "release31DeviceNote7": "1 Schließer potenzialfrei 10 A/250 V AC.", "release31DeviceNote8": "Einmaliges ausführen von Zeitprogrammen.", "generalNew": "<PERSON>eu", "yearsAgo": "{count, plural, one {<PERSON><PERSON><PERSON>} other {Vor {count} <PERSON><PERSON><PERSON>}}", "monthsAgo": "{count, plural, one {<PERSON><PERSON><PERSON>} other {Vor {count} <PERSON><PERSON>}}", "weeksAgo": "{count, plural, one {<PERSON><PERSON><PERSON>} other {Vor {count} <PERSON><PERSON><PERSON>}}", "daysAgo": "{count, plural, one {<PERSON><PERSON><PERSON>} other {Vor {count} <PERSON><PERSON>}}", "minutesAgo": "{count, plural, one {Vor einer Minute} other {Vor {count} Minuten}}", "hoursAgo": "{count, plural, one {Vor einer St<PERSON>} other {Vor {count} <PERSON><PERSON><PERSON>}}", "secondsAgo": "{count, plural, one {Vor einer Sekunde} other {Vor {count} Sekunden}}", "justNow": "Gerade eben", "discoveryEsripmDescription": "Stromstoß-Schaltrelais IP Matter", "generalTextKidsRoom": "Kinderzimmerschaltung", "generalDescriptionKidsRoom": "<PERSON><PERSON> ({mode}) wird nach ca. 1 Sekunde mit kleinster Helligkeit eingeschaltet und, solange weiter getastet wird, langsam hochgedim<PERSON>t, ohne die zuletzt gespeicherte Helligkeitsstufe zu verändern.", "generalTextSceneButton": "Szenentaster", "settingsEnOceanConfigHeader": "EnOcean Konfiguration", "enOceanConfigChangedSuccessfully": "EnOcean Konfiguration wurde erfolgreich geändert.", "activateEnOceanRepeater": "EnOcean Repeater aktivieren", "enOceanRepeaterLevel": "Repeater Level", "enOceanRepeaterLevel1": "1-Level", "enOceanRepeaterLevel2": "2-Level", "enOceanRepeaterOffDescription": "Es werden keine Funksignale von Sensoren repeatet.", "enOceanRepeaterLevel1Description": "Es werden nur die Funksignale von Sensoren empfangen, geprüft und mit voller Sendeleistung weitergesendet. Funksignale anderer Repeater werden ignoriert, um die Datenmenge zu reduzieren.", "enOceanRepeaterLevel2Description": "<PERSON>s werden außer den Funksignalen von Sensoren auch die Funksignale von 1-Level-Repeatern verarbeitet. Ein Funksignal kann damit maximal 2-mal empfangen und verstärkt werden. Funkrepeater müssen nicht eingelernt werden. Sie empfangen und verstärken die Funksignale von allen Funksensoren in ihrem Empfangsbereich.", "settingsSensorHeader": "Sensoren", "sensorChangedSuccessfully": "Sensoren wurden erfolgreich geändert", "wiredButton": "Drahtgebundener Taster", "enOceanId": "EnOcean-ID", "enOceanAddManually": "EnOcean-ID eingeben oder scannen", "enOceanIdInvalid": "Ungültige EnOcean-ID", "enOceanAddAutomatically": "<PERSON>t EnOcean-Telegram e<PERSON>lernen", "enOceanAddDescription": "Das EnOcean Funk-Protokoll ermöglicht es Taster in deinen Aktor einzulernen und zu bedienen.\n\nWähle entweder das automatische Einlernen mit EnOcean Telegram, um Taster via Tastendruck einzulernen oder wähle die manuelle Variante, um die EnOcean-ID deines Tasters einzuscannen oder einzutippen.", "enOceanTelegram": "Telegramm", "enOceanCodeScan": "Gib die EnOcean-ID deines {sensorType} ein oder scanne den EnOcean-QR-Code, deines {sensorType}, um ihn hinzuzufügen.", "enOceanCode": "EnOcean QR-Code", "enOceanCodeScanDescription": "Suche nach dem EnOcean QR-Code auf deinem {sensorType} und scanne ihn mit deiner Kamera.", "enOceanButton": "EnOcean Taster", "enOceanBackpack": "EnOcean-Adapter", "sensorNotAvailable": "<PERSON>s wurden noch keine Sen<PERSON>en einge<PERSON>nt", "sensorAdd": "Sensor hinzufügen", "sensorCancel": "Einlernen abbrechen", "sensorCancelDescription": "Möchtest du den Einlernvorgang wirklich abbrechen?", "getEnOceanBackpack": "Hol dir deinen EnOcean-Adapter", "enOceanBackpackMissing": "Um in die fantastische Welt der perfekten Konnektivität und Kommunikation einsteigen zu können, benötigst du einen EnOcean-Adapter.\n<PERSON><PERSON><PERSON> hier, um mehr Informationen zu erhalten", "sensorEditChangedSuccessfully": "{sensorName} wurde erfolgreich geändert", "sensorConnectedVia": "verbunden über {deviceName}", "lastSeen": "Zuletzt g<PERSON>hen", "setButtonOrientation": "Orientierung festlegen", "setButtonType": "Taster-<PERSON><PERSON> festlegen", "button1Way": "1-Kanal Taster", "button2Way": "2-Kanal Taster", "button4Way": "4-<PERSON><PERSON> Taster", "buttonUnset": "<PERSON><PERSON> belegt", "button": "Taster", "sensor": "Sensor", "sensorsFound": "{count, plural, =0 {Keine <PERSON> gefunden} one {1 Sensor gefunden} other {{count} Sensoren gefunden}}", "sensorSearch": "<PERSON><PERSON> Sen<PERSON>en suchen", "searchAgain": "<PERSON><PERSON><PERSON> suchen", "sensorTeachInHeader": "{sensorType} e<PERSON><PERSON><PERSON>", "sensorChooseHeader": "{sensorType} auswählen", "sensorChooseDescription": "<PERSON><PERSON><PERSON><PERSON> einen Taster aus, den du einlernen möchtest.", "sensorCategoryDescription": "<PERSON><PERSON><PERSON>e eine Kategorie aus, die du einlernen möchten.", "sensorName": "Tastername", "sensorNameFooter": "<PERSON><PERSON> dem <PERSON> einen Namen", "sensorAddedSuccessfully": "{sensorName} wurde erfolg<PERSON>ich e<PERSON>nt", "sensorDelete": "{sensorType} löschen", "sensorDeleteHint": "Soll der {sensorType} {sensorName} wirklich gelöscht werden?", "sensorDeletedSuccessfully": "{sensorName} wurde erfolgreich gelö<PERSON>t", "buttonTapDescription": "<PERSON><PERSON><PERSON> auf den Taster, den du einlernen möchtest.", "waitingForTelegram": "Der Aktor wartet auf das Telegramm", "copied": "<PERSON><PERSON><PERSON>", "pairingFailed": "{sensorType} bereits eingelernt", "generalDescriptionUniversalbutton": "<PERSON><PERSON> erfolgt die Richtungsumkehr durch kurzes Loslassen des Tasters. Kurze Steuerbefehle schalten ein bzw. aus. (Toggle)", "generalDescriptionDirectionalbutton": "Richtungstaster ist oben 'einschalten und aufdimmen' sowie unten 'ausschalten und abdimmen'.", "matterForwardingDescription": "Der Tastendruck wird an Matter weitergeleitet.", "none": "<PERSON><PERSON>", "buttonNoneDescription": "Der Taster hat keine Funktion.", "buttonUnsetDescription": "Dem Taster ist kein Verhalten zugewiesen.", "sensorButtonTypeChangedSuccessfully": "Taster-<PERSON><PERSON> wurde erfolgreich geändert", "forExample": "z.B. {example}", "enOceanQRCodeInvalidDescription": "<PERSON><PERSON> ab Produktionsdatum 44/20 möglich", "input": "Eingang", "buttonSceneValueOverride": "Szenentasterwert überschreiben", "buttonSceneValueOverrideDescription": "Der Szenentasterwert wird mit dem aktuellen Dimmwert durch gedrückt halten der Taste überschrieben", "buttonSceneDescription": "Der Szenentaster schaltet mit einem festen Dimmwert ein", "buttonPress": "Tastendruck", "triggerOn": "mit längerer Tasterbetätigung am Universaltaster oder Richtungstaster auf der Einschaltseite", "triggerOff": "mit einem Doppelimpuls am Universaltaster oder Richtungstaster auf der Ausschaltseite", "centralOn": "Zentral Ein", "centralOff": "Zentral Aus", "centralButton": "Zentraltaster", "enOceanAdapterNotFound": "<PERSON><PERSON> Adapter gefunden", "updateRequired": "Update er<PERSON><PERSON><PERSON>", "updateRequiredDescription": "<PERSON><PERSON> A<PERSON> benötigt ein Update, um dieses neue Gerät zu unterstützen.", "release32Header": "Die neue BR64 mit Matter und EnOcean sowie die neue Bluetooth-Unterputz-Schaltuhr SU62PF-BT/UC sind jetzt verfügbar!", "release32EUD64Header": "Der neue Unterputz-Dimmer mit Matter über Wi-Fi und bis zu 300 W ist da!", "release32EUD64Note1": "Konfiguration von Dimmgeschwindigkeit, Ein-/Ausschaltgeschwindigkeit, Kinderzimmer-/Schlummerschaltung und vielem mehr.", "release32EUD64Note2": "Der Funktionsumfang des EUD64NPN-IPM kann durch Adapter, z. B. den EnOcean-Adapter EOA64, erwei<PERSON><PERSON> werden.", "release32EUD64Note3": "Bis zu 30 EnOcean-Funktaster können in Verbindung mit dem EnOcean-Adapter EOA64 mit dem EUD64NPN-IPM direkt verknüpft und an Matter weitergeleitet werden.", "release32EUD64Note4": "Zwei drahtgebundene Tasteingänge können mit dem EUD64NPN-IPM direkt verknüpft oder an Matter weitergeleitet werden.", "release32ESR64Header": "Der neue potenzialfreie Unterputz-1-Kanal-Schaltaktor mit Matter über Wi-Fi und bis zu 16 A ist da!", "release32ESR64Note1": "Konfiguration von verschiedenen Funktionen wie Stromstoßschalter (ES), <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ER), <PERSON><PERSON><PERSON> (ER-Invers) und vielem mehr.", "release32ESR64Note2": "Der Funktionsumfang des ESR64PF-IPM kann durch Adapter, z. B. den EnOcean-Adapter EOA64, erwei<PERSON><PERSON> werden.", "release32ESR64Note3": "Bis zu 30 EnOcean-Funktaster können in Verbindung mit dem EnOcean-Adapter EOA64 mit dem ESR64PF-IPM direkt verknüpft und an Matter weitergeleitet werden.", "release32ESR64Note4": "Ein drahtgebundener Tasteingang kann mit dem ESR64PF-IPM direkt verknüpft oder an Matter weitergeleitet werden.", "buttonsFound": "{count, plural, =0 {Keine Taster gefunden} one {1 Taster gefunden} other {{count} Taster gefunden}}", "doubleImpuls": "mit einem Doppelimpuls", "impulseDescription": "<PERSON>n der Kanal eingeschaltet ist, wird dieser durch einen Impuls ausgeschaltet.", "locationServiceEnable": "Standort aktivieren", "locationServiceDisabledDescription": "Standort ist deaktiviert. Deine Betriebssystemversion benöti<PERSON> den Standort, um Bluetooth Geräte finden zu können.", "locationPermissionDeniedNoPosition": "Standort Berechtigungen wurden nicht erteilt. Deine Betriebssystemversion benötigt die Standortberechtigung, um Bluetooth Geräte finden zu können. Bitte erlaube die Standortberechtigung in deinen Geräteeinstellungen.", "interfaceStatePermanentDeniedDescriptionDevicesAround": "Die Geräte in der Nähe Berechtigung wurde nicht erteilt. Bitte erlaube die Berechtigung Geräte in der Nähe in deinen Geräteeinstellungen.", "permissionNearbyDevices": "Geräte in der Nähe", "release320Header": "Der neue leistungsstarke Universaldimmer EUD12NPN-BT/600W-230V ist da!", "release320EUD600Header": "Was kann der neue Universaldimmer?", "release320EUD600Note1": "Universaldimmer mit bis zu 600W Leistung", "release320EUD600Note2": "Erweiterbar mit Leistungszusatz LUD12 auf bis zu 3800W", "release320EUD600Note3": "Örtliche Bedienung mit Universal- oder Richtungstaster", "release320EUD600Note4": "Zentralfunktionen Ein / Aus", "release320EUD600Note5": "Bewegungsmelder Eingang für weiteren Komfort", "release320EUD600Note6": "Integrierte Schaltuhr mit 10 Schaltprogramme", "release320EUD600Note7": "Astrofunktion", "release320EUD600Note8": "Individuelle Einschalthelligkeit", "mqttClientCertificate": "<PERSON><PERSON>", "mqttClientCertificateHint": "MQTT Client Zertifikat", "mqttClientKey": "Client Key", "mqttClientKeyHint": "MQTT Client Key", "mqttClientPassword": "Client Passwort", "mqttClientPasswordHint": "MQTT Client Passwort", "mqttEnableHomeAssistantDiscovery": "HomeAssistant MQTT Discovery aktivieren", "modbusTcp": "Modbus TCP", "enableInterface": "Schnittstelle aktivieren", "busAddress": "Bus-Adresse", "busAddressWithAddress": "Bus-Adresse {index}", "deviceType": "Gerätetyp", "registerTable": "{count, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "currentValues": "Aktuelle Werte", "requestRTU": "Abfrage RTU", "requestPriority": "Abfragepriorität", "mqttForwarding": "MQTT Weiterleitung", "historicData": "Historische Daten", "dataFormat": "Datenformat", "dataType": "Datentyp", "description": "Beschreibung", "readWrite": "Lesen/Schreiben", "unit": "Einheit", "registerTableReset": "Registertabelle zurücksetzen", "registerTableResetDescription": "Soll die Registertabelle wirklich zurückgesetzt werden?", "notConfigured": "<PERSON>cht konfiguriert", "release330ZGW16Header": "Umfangreiches Update für das ZGW16WL-IP", "release330Header": "Das ZGW16WL-<PERSON> mit bis zu 16 Stromzählern", "release330ZGW16Note1": "Unterstützung von bis zu 16 ELTAKO Modbus-Stromzählern", "release330ZGW16Note2": "Unterstützung von Modbus TCP", "release330ZGW16Note3": "Unterstützung von MQTT Discovery", "screenshotButtonLivingRoom": "<PERSON><PERSON>", "registerChangedSuccessfully": "Register wurde erfolgreich geändert.", "serverCertificateEmpty": "Server <PERSON><PERSON><PERSON><PERSON><PERSON> darf nicht leer sein.", "registerTemplates": "Register Templates", "registerTemplateChangedSuccessfully": "Register Template wurde erfolgreich geändert.", "registerTemplateReset": "Register Template zurücksetzen", "registerTemplateResetDescription": "Soll das Register Template wirklich zurückgesetzt werden?", "registerTemplateNotAvailable": "Keine Register Templates verfügbar", "rename": "Umbenennen", "registerName": "Register Name", "registerRenameDescription": "Gib dem <PERSON> einen benutzerdefinierten Namen", "restart": "Gerät neu starten", "restartDescription": "<PERSON>öchten Sie das Gerät wirklich neu starten?", "restartConfirmationDescription": "Das Gerät wird nun neu gestartet", "deleteAllElectricityMeters": "Alle Stromzähler löschen", "deleteAllElectricityMetersDescription": "Möchten Si<PERSON> wirklich alle Stromzähler löschen?", "deleteAllElectricityMetersConfirmationDescription": "Alle Stromzähler wurden erfolgreich gelöscht", "resetAllElectricityMeters": "Alle Stromzähler-Konfigurationen zurücksetzen", "resetAllElectricityMetersDescription": "Möchten Si<PERSON> wirklich alle Stromzähler-Konfigurationen zurücksetzen?", "resetAllElectricityMetersConfirmationDescription": "Alle Stromzähler-Konfigurationen wurden erfolgreich zurückgesetzt", "deleteElectricityMeterHistories": "Alle Stromzähler-Verläufe löschen", "deleteElectricityMeterHistoriesDescription": "Möchten Si<PERSON> wirklich alle Stromzähler-Verläufe löschen?", "deleteElectricityMeterHistoriesConfirmationDescription": "Alle Stromzähler-Verläufe wurden erfolgreich <PERSON>", "multipleElectricityMetersSupportMissing": "Dein Gerät unterstützt derzeit nur einen Stromzähler. Bitte aktualisiere deine Firmware.", "consumptionWithUnit": "Verbrauch (kWh)", "exportWithUnit": "Lieferung (kWh)", "importWithUnit": "Bezug (kWh)", "resourceWarningHeader": "Begrenzte Ressourcen", "mqttAndTcpResourceWarning": "Ein gleichzeitiger Betrieb von MQTT und Modbus TCP ist wegen begrenzter Systemressourcen nicht möglich. Deaktiviere zuerst {protocol}.", "mqttEnabled": "MQTT aktiviert", "redirectMQTT": "Zu den MQTT Einstellungen", "redirectModbus": "Zu den Modbus Einstellungen", "unsupportedSettingDescription": "Mit deiner aktuellen Firmware-Version werden einige der Geräte-Einstellungen nicht unterstützt. Bitte aktualisiere deine Firmware, um die neuen Funktionen nutzen zu können.", "updateNow": "Jetzt aktualisieren", "zgw241Hint": "Mit diesem Update wird Modbus TCP standardmäßig aktiviert und MQTT deaktiviert. Dies kann in den Einstellungen geändert werden. Mit der Unterstützung von bis zu 16 Zählern wurden viele Optimierungen vorgenommen; dies kann zu Veränderungen in den Geräteeinstellungen führen. Bitte führe nach Anpassungen der Einstellungen einen Neustart des Geräts durch.", "deviceConfigChangedSuccesfully": "Geräte Konfiguration wurde erfolgreich geändert.", "deviceConfiguration": "Geräte Konfiguration", "tiltModeToggle": "Lamellenfunktion", "tiltModeToggleFooter": "<PERSON><PERSON> das Gerät in Matter eingelernt wird, müssen dort alle Funktionen neu eingestellt werden", "shaderMovementDirection": "Umpolung Auf/Ab", "shaderMovementDirectionDescription": "Umpolung der Richtung für Auf-/Ab-Bewegung des Motors", "tiltTime": "Lamellenzeit", "changeTiltModeDialogTitle": "Lamellenfunktion {target, select, true {aktivieren} false {deaktivieren} other {ändern}}", "changeTiltModeDialogConfirmation": "{target, select, true {Aktivieren} false {Deaktivieren} other {Ändern}}", "generalTextSlatSetting": "Einstellung der Lamellen", "generalTextPosition": "Position", "generalTextSlatPosition": "Position der Lamellen", "slatSettingDescription": "Aktivieren Sie die Lamellenfunktion, um den Neigungswinkel Ihrer Jalousien einzustellen.", "scenePositionSliderDescription": "Die Position bestimmt, wie weit der Rollladen geöffnet oder geschlossen ist.", "sceneSlatPositionSliderDescription": "Die Lamellenposition legt den Neigungswinkel der Rollladenlamellen fest, wenn die Lamellenfunktion aktiviert ist.", "referenceRun": "Kalibrierungslauf", "slatAutoSettingHint": "In diesem Modus spielt die Position der Jalousien keine Rolle, bevor sich die Lamellen auf die gewünschte Neigungsposition einstellen.", "slatReversalSettingHint": "In diesem Modus werden die Jalousien vollständig geschlossen, bevor sich die Lamellen auf die gewünschte Neigungsposition einstellen.", "release340Header": "Der neue Unterputz Matter-Beschattungsaktor ESB64NP-IPM ist da!", "release340ESB64Header": "Was kann der ESB64NP-IPM?", "release340ESB64Note1": "Unser Matter-Gateway zertifizierter Beschattungsaktor mit optionaler Lamellenfunktion", "release340ESB64Note2": "Zwei drahtgebundene Tasteingänge für manuelles Schalten und Weiterleitung an Matter", "release340ESB64Note3": "Erweiterbar mit EnOcean-Adapter (EOA64). Z.B mit EnOcean-Funktaster F4T55", "release340ESB64Note4": "Offen für Integrationen dank REST-API nach OpenAPI Standard", "activateTiltModeDialogText": "Wenn die Lamellenfunktion aktiviert wird, gehen alle Einstellungen verloren. <PERSON><PERSON> du sicher, dass du die Lamellenfunktion aktivieren möchtest?", "deactivateTiltModeDialogText": "Wenn die Lamellenfunktion deaktiviert wird, gehen alle Einstellungen verloren. <PERSON><PERSON> du sicher, dass du die Lamellenfunktion deaktivieren möchtest?", "buttonSceneESBDescription": "Der Szenentaster fährt den Rollladen auf eine feste Position", "sceneValueOverride": "Halte den Taster 4 Sekunden lang gedrückt, um die Position mit dem aktuellen Wert (Anfangswert) zu überschreiben.", "sceneCalibration": "Die Kalibrierungsfahrt bewegt den Rollladen einmal vollständig nach unten und wieder nach oben, um die Endlage zu ermitteln und die Positionserkennung zu ermöglichen.", "up": "<PERSON><PERSON>", "down": "Ab"}