import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fi.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_it.dart';
import 'app_localizations_nl.dart';
import 'app_localizations_sv.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('de'),
    Locale('es'),
    Locale('fi'),
    Locale('fr'),
    Locale('it'),
    Locale('nl'),
    Locale('nl', 'BE'),
    Locale('sv'),
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'ELTAKO Connect'**
  String get appName;

  /// No description provided for @discoveryHint.
  ///
  /// In en, this message translates to:
  /// **'Activate Bluetooth on the device to connect'**
  String get discoveryHint;

  /// Specifies how many device are found in the discovery overview
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =0 {No devices found} one {1 device found} other {{count} devices found}}'**
  String devicesFound(num count);

  /// No description provided for @discoveryDemodeviceName.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Demo device} other {Demo devices}}'**
  String discoveryDemodeviceName(num count);

  /// No description provided for @discoverySu12Description.
  ///
  /// In en, this message translates to:
  /// **'2-Channel Time Switch Bluetooth'**
  String get discoverySu12Description;

  /// No description provided for @discoveryImprint.
  ///
  /// In en, this message translates to:
  /// **'Imprint'**
  String get discoveryImprint;

  /// No description provided for @discoveryLegalnotice.
  ///
  /// In en, this message translates to:
  /// **'Legal notice'**
  String get discoveryLegalnotice;

  /// No description provided for @generalSave.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get generalSave;

  /// No description provided for @generalCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get generalCancel;

  /// No description provided for @detailsHeaderHardwareversion.
  ///
  /// In en, this message translates to:
  /// **'Hardware version'**
  String get detailsHeaderHardwareversion;

  /// No description provided for @detailsHeaderSoftwareversion.
  ///
  /// In en, this message translates to:
  /// **'Software version'**
  String get detailsHeaderSoftwareversion;

  /// No description provided for @detailsHeaderConnected.
  ///
  /// In en, this message translates to:
  /// **'Connected'**
  String get detailsHeaderConnected;

  /// No description provided for @detailsHeaderDisconnected.
  ///
  /// In en, this message translates to:
  /// **'Disconnected'**
  String get detailsHeaderDisconnected;

  /// No description provided for @detailsTimersectionHeader.
  ///
  /// In en, this message translates to:
  /// **'Programs'**
  String get detailsTimersectionHeader;

  /// No description provided for @detailsTimersectionTimercount.
  ///
  /// In en, this message translates to:
  /// **'of 60 programs used'**
  String get detailsTimersectionTimercount;

  /// No description provided for @detailsConfigurationsectionHeader.
  ///
  /// In en, this message translates to:
  /// **'Configuration'**
  String get detailsConfigurationsectionHeader;

  /// No description provided for @detailsConfigurationPin.
  ///
  /// In en, this message translates to:
  /// **'Device PIN'**
  String get detailsConfigurationPin;

  /// No description provided for @detailsConfigurationChannelsDescription.
  ///
  /// In en, this message translates to:
  /// **'Channel 1: {channel1} | Channel 2: {channel2}'**
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  );

  /// No description provided for @settingsCentralHeader.
  ///
  /// In en, this message translates to:
  /// **'Central On/Off'**
  String get settingsCentralHeader;

  /// No description provided for @detailsConfigurationCentralDescription.
  ///
  /// In en, this message translates to:
  /// **'Only applies if the channel is set to AUTO'**
  String get detailsConfigurationCentralDescription;

  /// No description provided for @detailsConfigurationDevicedisplaylock.
  ///
  /// In en, this message translates to:
  /// **'Lock device display'**
  String get detailsConfigurationDevicedisplaylock;

  /// No description provided for @timerOverviewHeader.
  ///
  /// In en, this message translates to:
  /// **'Programs'**
  String get timerOverviewHeader;

  /// No description provided for @timerOverviewTimersectionTimerinactivecount.
  ///
  /// In en, this message translates to:
  /// **'inactive'**
  String get timerOverviewTimersectionTimerinactivecount;

  /// No description provided for @timerDetailsListsectionDays1.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get timerDetailsListsectionDays1;

  /// No description provided for @timerDetailsListsectionDays2.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get timerDetailsListsectionDays2;

  /// No description provided for @timerDetailsListsectionDays3.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get timerDetailsListsectionDays3;

  /// No description provided for @timerDetailsListsectionDays4.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get timerDetailsListsectionDays4;

  /// No description provided for @timerDetailsListsectionDays5.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get timerDetailsListsectionDays5;

  /// No description provided for @timerDetailsListsectionDays6.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get timerDetailsListsectionDays6;

  /// No description provided for @timerDetailsListsectionDays7.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get timerDetailsListsectionDays7;

  /// No description provided for @timerDetailsHeader.
  ///
  /// In en, this message translates to:
  /// **'Program'**
  String get timerDetailsHeader;

  /// No description provided for @timerDetailsSunrise.
  ///
  /// In en, this message translates to:
  /// **'Sunrise'**
  String get timerDetailsSunrise;

  /// No description provided for @generalToggleOff.
  ///
  /// In en, this message translates to:
  /// **'Off'**
  String get generalToggleOff;

  /// No description provided for @generalToggleOn.
  ///
  /// In en, this message translates to:
  /// **'On'**
  String get generalToggleOn;

  /// No description provided for @timerDetailsImpuls.
  ///
  /// In en, this message translates to:
  /// **'Impulse'**
  String get timerDetailsImpuls;

  /// No description provided for @generalTextTime.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get generalTextTime;

  /// No description provided for @generalTextAstro.
  ///
  /// In en, this message translates to:
  /// **'Astro'**
  String get generalTextAstro;

  /// No description provided for @generalTextAuto.
  ///
  /// In en, this message translates to:
  /// **'Auto'**
  String get generalTextAuto;

  /// No description provided for @timerDetailsOffset.
  ///
  /// In en, this message translates to:
  /// **'Time Offset'**
  String get timerDetailsOffset;

  /// No description provided for @timerDetailsPlausibility.
  ///
  /// In en, this message translates to:
  /// **'Activate plausibility check'**
  String get timerDetailsPlausibility;

  /// No description provided for @timerDetailsPlausibilityDescription.
  ///
  /// In en, this message translates to:
  /// **'If the off time is set to an earlier time than the on time, both programs are ignored, e.g. switch on at sunrise and switch off at 6:00 am. There are also situations where the check is not wanted, e.g. switching on at sunset and switching off at 1:00 am.'**
  String get timerDetailsPlausibilityDescription;

  /// No description provided for @generalDone.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get generalDone;

  /// No description provided for @generalDelete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get generalDelete;

  /// No description provided for @timerDetailsImpulsDescription.
  ///
  /// In en, this message translates to:
  /// **'Change global impulse configuration'**
  String get timerDetailsImpulsDescription;

  /// No description provided for @settingsNameHeader.
  ///
  /// In en, this message translates to:
  /// **'Device name'**
  String get settingsNameHeader;

  /// No description provided for @settingsNameDescription.
  ///
  /// In en, this message translates to:
  /// **'This name is also displayed in Discovery.'**
  String get settingsNameDescription;

  /// No description provided for @settingsFactoryresetHeader.
  ///
  /// In en, this message translates to:
  /// **'Factory settings'**
  String get settingsFactoryresetHeader;

  /// No description provided for @settingsFactoryresetDescription.
  ///
  /// In en, this message translates to:
  /// **'Which content should be reset?'**
  String get settingsFactoryresetDescription;

  /// No description provided for @settingsFactoryresetResetbluetooth.
  ///
  /// In en, this message translates to:
  /// **'Reset Bluetooth settings'**
  String get settingsFactoryresetResetbluetooth;

  /// No description provided for @settingsFactoryresetResettime.
  ///
  /// In en, this message translates to:
  /// **'Reset time settings'**
  String get settingsFactoryresetResettime;

  /// No description provided for @settingsFactoryresetResetall.
  ///
  /// In en, this message translates to:
  /// **'Set to factory settings'**
  String get settingsFactoryresetResetall;

  /// No description provided for @settingsDeletetimerHeader.
  ///
  /// In en, this message translates to:
  /// **'Delete programs'**
  String get settingsDeletetimerHeader;

  /// No description provided for @settingsDeletetimerDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to delete all timers?'**
  String get settingsDeletetimerDescription;

  /// No description provided for @settingsDeletetimerAllchannels.
  ///
  /// In en, this message translates to:
  /// **'All channels'**
  String get settingsDeletetimerAllchannels;

  /// No description provided for @settingsImpulseHeader.
  ///
  /// In en, this message translates to:
  /// **'Impulse Switch Time'**
  String get settingsImpulseHeader;

  /// No description provided for @settingsImpulseDescription.
  ///
  /// In en, this message translates to:
  /// **'The impulse Switch Time sets the duration of the impulse.'**
  String get settingsImpulseDescription;

  /// No description provided for @generalTextRandommode.
  ///
  /// In en, this message translates to:
  /// **'Random mode'**
  String get generalTextRandommode;

  /// No description provided for @settingsChannelsTimeoffsetHeader.
  ///
  /// In en, this message translates to:
  /// **'Solar time offset'**
  String get settingsChannelsTimeoffsetHeader;

  /// No description provided for @settingsChannelsTimeoffsetDescription.
  ///
  /// In en, this message translates to:
  /// **'Summer: {summerOffset} min | Winter: {winterOffset} min'**
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  );

  /// No description provided for @settingsLocationHeader.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get settingsLocationHeader;

  /// No description provided for @settingsLocationDescription.
  ///
  /// In en, this message translates to:
  /// **'Set your location to use astro functions.'**
  String get settingsLocationDescription;

  /// No description provided for @settingsLanguageHeader.
  ///
  /// In en, this message translates to:
  /// **'Device language'**
  String get settingsLanguageHeader;

  /// No description provided for @settingsLanguageSetlanguageautomatically.
  ///
  /// In en, this message translates to:
  /// **'Set language automatically'**
  String get settingsLanguageSetlanguageautomatically;

  /// No description provided for @settingsLanguageDescription.
  ///
  /// In en, this message translates to:
  /// **'Choose the language for the {deviceType}'**
  String settingsLanguageDescription(Object deviceType);

  /// No description provided for @settingsLanguageGerman.
  ///
  /// In en, this message translates to:
  /// **'German'**
  String get settingsLanguageGerman;

  /// No description provided for @settingsLanguageFrench.
  ///
  /// In en, this message translates to:
  /// **'French'**
  String get settingsLanguageFrench;

  /// No description provided for @settingsLanguageEnglish.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get settingsLanguageEnglish;

  /// No description provided for @settingsLanguageItalian.
  ///
  /// In en, this message translates to:
  /// **'Italian'**
  String get settingsLanguageItalian;

  /// No description provided for @settingsLanguageSpanish.
  ///
  /// In en, this message translates to:
  /// **'Spanish'**
  String get settingsLanguageSpanish;

  /// No description provided for @settingsDatetimeHeader.
  ///
  /// In en, this message translates to:
  /// **'Date and time'**
  String get settingsDatetimeHeader;

  /// No description provided for @settingsDatetimeSettimeautomatically.
  ///
  /// In en, this message translates to:
  /// **'Apply system time'**
  String get settingsDatetimeSettimeautomatically;

  /// No description provided for @settingsDatetimeSettimezoneautomatically.
  ///
  /// In en, this message translates to:
  /// **'Set timezone automatically'**
  String get settingsDatetimeSettimezoneautomatically;

  /// No description provided for @generalTextTimezone.
  ///
  /// In en, this message translates to:
  /// **'Timezone'**
  String get generalTextTimezone;

  /// No description provided for @settingsDatetime24Hformat.
  ///
  /// In en, this message translates to:
  /// **'24 hour format'**
  String get settingsDatetime24Hformat;

  /// No description provided for @settingsDatetimeSetsummerwintertimeautomatically.
  ///
  /// In en, this message translates to:
  /// **'Summer-/Wintertime automatically'**
  String get settingsDatetimeSetsummerwintertimeautomatically;

  /// No description provided for @settingsDatetimeWinter.
  ///
  /// In en, this message translates to:
  /// **'Winter'**
  String get settingsDatetimeWinter;

  /// No description provided for @settingsDatetimeSummer.
  ///
  /// In en, this message translates to:
  /// **'Summer'**
  String get settingsDatetimeSummer;

  /// No description provided for @settingsPasskeyHeader.
  ///
  /// In en, this message translates to:
  /// **'Current device PIN'**
  String get settingsPasskeyHeader;

  /// No description provided for @settingsPasskeyDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter the current device PIN'**
  String get settingsPasskeyDescription;

  /// No description provided for @timerDetailsActiveprogram.
  ///
  /// In en, this message translates to:
  /// **'Program active'**
  String get timerDetailsActiveprogram;

  /// No description provided for @timerDetailsActivedays.
  ///
  /// In en, this message translates to:
  /// **'Active days'**
  String get timerDetailsActivedays;

  /// No description provided for @timerDetailsSuccessdialogHeader.
  ///
  /// In en, this message translates to:
  /// **'Successful'**
  String get timerDetailsSuccessdialogHeader;

  /// No description provided for @timerDetailsSuccessdialogDescription.
  ///
  /// In en, this message translates to:
  /// **'Program added successfully'**
  String get timerDetailsSuccessdialogDescription;

  /// No description provided for @settingsRandommodeDescription.
  ///
  /// In en, this message translates to:
  /// **'The random mode only works with timer based programs but not with impulse based programs or astro based programs (sunrise / sunset).'**
  String get settingsRandommodeDescription;

  /// No description provided for @settingsSolsticeHeader.
  ///
  /// In en, this message translates to:
  /// **'Solar time offset'**
  String get settingsSolsticeHeader;

  /// No description provided for @settingsSolsticeDescription.
  ///
  /// In en, this message translates to:
  /// **'The set time defines the time-offset to solar time and it gets inverted respectively.'**
  String get settingsSolsticeDescription;

  /// No description provided for @settingsSolsticeHint.
  ///
  /// In en, this message translates to:
  /// **'For Example: \nIn winter the switch occurs 30 minutes before sunset in response the switch at sunrise also occurs 30 minutes in advance.'**
  String get settingsSolsticeHint;

  /// No description provided for @generalTextMinutesShort.
  ///
  /// In en, this message translates to:
  /// **'min'**
  String get generalTextMinutesShort;

  /// No description provided for @settingsPinDescription.
  ///
  /// In en, this message translates to:
  /// **'The PIN is required for the connection.'**
  String get settingsPinDescription;

  /// No description provided for @settingsPinHeader.
  ///
  /// In en, this message translates to:
  /// **'New device PIN'**
  String get settingsPinHeader;

  /// No description provided for @settingsPinNewpinDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter a new PIN'**
  String get settingsPinNewpinDescription;

  /// No description provided for @settingsPinNewpinRepeat.
  ///
  /// In en, this message translates to:
  /// **'Repeat the new PIN'**
  String get settingsPinNewpinRepeat;

  /// No description provided for @detailsProductinfo.
  ///
  /// In en, this message translates to:
  /// **'Product information'**
  String get detailsProductinfo;

  /// No description provided for @settingsDatetimeSettimeautodescription.
  ///
  /// In en, this message translates to:
  /// **'Choose the preferred time'**
  String get settingsDatetimeSettimeautodescription;

  /// No description provided for @minutes.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Minute} other {Minutes}}'**
  String minutes(num count);

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Hour} other {Hours}}'**
  String hours(num count);

  /// No description provided for @seconds.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Second} other {Seconds}}'**
  String seconds(num count);

  /// No description provided for @generalTextChannel.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Channel} other {Channels}}'**
  String generalTextChannel(num count);

  /// No description provided for @generalLabelChannel.
  ///
  /// In en, this message translates to:
  /// **'Channel {number}'**
  String generalLabelChannel(Object number);

  /// No description provided for @generalTextDate.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get generalTextDate;

  /// No description provided for @settingsDatetime24HformatDescription.
  ///
  /// In en, this message translates to:
  /// **'Choose the preferred format'**
  String get settingsDatetime24HformatDescription;

  /// No description provided for @settingsDatetimeSetsummerwintertime.
  ///
  /// In en, this message translates to:
  /// **'Summer-/Winter time'**
  String get settingsDatetimeSetsummerwintertime;

  /// No description provided for @settingsDatetime24HformatValue24.
  ///
  /// In en, this message translates to:
  /// **'24h'**
  String get settingsDatetime24HformatValue24;

  /// No description provided for @settingsDatetime24HformatValue12.
  ///
  /// In en, this message translates to:
  /// **'AM/PM'**
  String get settingsDatetime24HformatValue12;

  /// No description provided for @detailsEdittimer.
  ///
  /// In en, this message translates to:
  /// **'Edit programs'**
  String get detailsEdittimer;

  /// No description provided for @settingsPinOldpinRepeat.
  ///
  /// In en, this message translates to:
  /// **'Please repeat the current PIN'**
  String get settingsPinOldpinRepeat;

  /// No description provided for @settingsPinCheckpin.
  ///
  /// In en, this message translates to:
  /// **'Checking PIN'**
  String get settingsPinCheckpin;

  /// No description provided for @detailsDevice.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Device} other {Devices}}'**
  String detailsDevice(num count);

  /// No description provided for @detailsDisconnect.
  ///
  /// In en, this message translates to:
  /// **'Disconnect'**
  String get detailsDisconnect;

  /// No description provided for @settingsCentralDescription.
  ///
  /// In en, this message translates to:
  /// **'The input A1 controls the Central On/Off.\nCentral On/Off only applies if the channel is set to Central On/Off.'**
  String get settingsCentralDescription;

  /// No description provided for @settingsCentralHint.
  ///
  /// In en, this message translates to:
  /// **'Example:\nChannel 1 = Central On/Off\nChannel 2 = Off\nA1 = Central On -> Only C1 switches to On, C2 stays Off'**
  String get settingsCentralHint;

  /// No description provided for @settingsCentralToggleheader.
  ///
  /// In en, this message translates to:
  /// **'Central Input switches'**
  String get settingsCentralToggleheader;

  /// No description provided for @settingsCentralActivechannelsdescription.
  ///
  /// In en, this message translates to:
  /// **'Current channels with the setting Central On/Off:'**
  String get settingsCentralActivechannelsdescription;

  /// No description provided for @settingsSolsticeSign.
  ///
  /// In en, this message translates to:
  /// **'Sign'**
  String get settingsSolsticeSign;

  /// No description provided for @settingsDatetimeTimezoneDescription.
  ///
  /// In en, this message translates to:
  /// **'Central European Time'**
  String get settingsDatetimeTimezoneDescription;

  /// No description provided for @generalButtonContinue.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get generalButtonContinue;

  /// No description provided for @settingsPinConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'PIN change successful'**
  String get settingsPinConfirmationDescription;

  /// No description provided for @settingsPinFailDescription.
  ///
  /// In en, this message translates to:
  /// **'PIN change failed'**
  String get settingsPinFailDescription;

  /// No description provided for @settingsPinFailHeader.
  ///
  /// In en, this message translates to:
  /// **'Failure'**
  String get settingsPinFailHeader;

  /// No description provided for @settingsPinFailShort.
  ///
  /// In en, this message translates to:
  /// **'The PIN has to be exactly 6 digits long'**
  String get settingsPinFailShort;

  /// No description provided for @settingsPinFailWrong.
  ///
  /// In en, this message translates to:
  /// **'The current PIN is incorrect'**
  String get settingsPinFailWrong;

  /// No description provided for @settingsPinFailMatch.
  ///
  /// In en, this message translates to:
  /// **'The PINs do not match'**
  String get settingsPinFailMatch;

  /// No description provided for @discoveryLostconnectionHeader.
  ///
  /// In en, this message translates to:
  /// **'Connection lost'**
  String get discoveryLostconnectionHeader;

  /// No description provided for @discoveryLostconnectionDescription.
  ///
  /// In en, this message translates to:
  /// **'The device has been disconnected.'**
  String get discoveryLostconnectionDescription;

  /// No description provided for @settingsChannelConfigCentralDescription.
  ///
  /// In en, this message translates to:
  /// **'Behaves like AUTO and also listens to the wired central inputs'**
  String get settingsChannelConfigCentralDescription;

  /// No description provided for @settingsChannelConfigOnDescription.
  ///
  /// In en, this message translates to:
  /// **'Switches the channel permanently to ON and ignores the programs'**
  String get settingsChannelConfigOnDescription;

  /// No description provided for @settingsChannelConfigOffDescription.
  ///
  /// In en, this message translates to:
  /// **'Switches the channel permanently to OFF and ignores the programs'**
  String get settingsChannelConfigOffDescription;

  /// No description provided for @settingsChannelConfigAutoDescription.
  ///
  /// In en, this message translates to:
  /// **'Switches in relation to the time and astro programs'**
  String get settingsChannelConfigAutoDescription;

  /// No description provided for @bluetoothPermissionDescription.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth is required for the configuration of the devices.'**
  String get bluetoothPermissionDescription;

  /// No description provided for @timerListitemOn.
  ///
  /// In en, this message translates to:
  /// **'Turn on'**
  String get timerListitemOn;

  /// No description provided for @timerListitemOff.
  ///
  /// In en, this message translates to:
  /// **'Turn off'**
  String get timerListitemOff;

  /// No description provided for @timerListitemUnknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get timerListitemUnknown;

  /// No description provided for @timerDetailsAstroHint.
  ///
  /// In en, this message translates to:
  /// **'The location must be set in the settings for the astro programs to work correctly.'**
  String get timerDetailsAstroHint;

  /// No description provided for @timerDetailsTrigger.
  ///
  /// In en, this message translates to:
  /// **'Trigger'**
  String get timerDetailsTrigger;

  /// No description provided for @timerDetailsSunset.
  ///
  /// In en, this message translates to:
  /// **'Sunset'**
  String get timerDetailsSunset;

  /// No description provided for @settingsLocationCoordinates.
  ///
  /// In en, this message translates to:
  /// **'Coordinates'**
  String get settingsLocationCoordinates;

  /// No description provided for @settingsLocationLatitude.
  ///
  /// In en, this message translates to:
  /// **'Latitude'**
  String get settingsLocationLatitude;

  /// No description provided for @settingsLocationLongitude.
  ///
  /// In en, this message translates to:
  /// **'Longitude'**
  String get settingsLocationLongitude;

  /// No description provided for @timerOverviewEmptyday.
  ///
  /// In en, this message translates to:
  /// **'No programs are currently used for {day}'**
  String timerOverviewEmptyday(Object day);

  /// No description provided for @timerOverviewProgramloaded.
  ///
  /// In en, this message translates to:
  /// **'Programs are loaded'**
  String get timerOverviewProgramloaded;

  /// No description provided for @timerOverviewProgramchanged.
  ///
  /// In en, this message translates to:
  /// **'Program was changed'**
  String get timerOverviewProgramchanged;

  /// No description provided for @settingsDatetimeProcessing.
  ///
  /// In en, this message translates to:
  /// **'Date and time is changed'**
  String get settingsDatetimeProcessing;

  /// No description provided for @deviceNameEmpty.
  ///
  /// In en, this message translates to:
  /// **'Input must not be empty'**
  String get deviceNameEmpty;

  /// No description provided for @deviceNameHint.
  ///
  /// In en, this message translates to:
  /// **'The input must not contain more than {count} characters.'**
  String deviceNameHint(Object count);

  /// No description provided for @deviceNameChanged.
  ///
  /// In en, this message translates to:
  /// **'Device name is changed'**
  String get deviceNameChanged;

  /// No description provided for @deviceNameChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Device name was successfully changed'**
  String get deviceNameChangedSuccessfully;

  /// No description provided for @deviceNameChangedFailed.
  ///
  /// In en, this message translates to:
  /// **'An error has occurred.'**
  String get deviceNameChangedFailed;

  /// No description provided for @settingsPinConfirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get settingsPinConfirm;

  /// No description provided for @deviceShowInstructions.
  ///
  /// In en, this message translates to:
  /// **'1. Activate the Bluetooth of the watch with SET\n2. Tap the button at the top to start the search.'**
  String get deviceShowInstructions;

  /// No description provided for @deviceNameNew.
  ///
  /// In en, this message translates to:
  /// **'Enter new device name'**
  String get deviceNameNew;

  /// No description provided for @settingsLanguageRetrieved.
  ///
  /// In en, this message translates to:
  /// **'Language is retrieved'**
  String get settingsLanguageRetrieved;

  /// No description provided for @detailsProgramsShow.
  ///
  /// In en, this message translates to:
  /// **'Show programs'**
  String get detailsProgramsShow;

  /// No description provided for @generalTextProcessing.
  ///
  /// In en, this message translates to:
  /// **'Please wait'**
  String get generalTextProcessing;

  /// No description provided for @generalTextRetrieving.
  ///
  /// In en, this message translates to:
  /// **'are retrieved'**
  String get generalTextRetrieving;

  /// No description provided for @settingsLocationPermission.
  ///
  /// In en, this message translates to:
  /// **'Allow ELTAKO Connect to access this device\'s location'**
  String get settingsLocationPermission;

  /// No description provided for @timerOverviewChannelloaded.
  ///
  /// In en, this message translates to:
  /// **'Channels are loaded'**
  String get timerOverviewChannelloaded;

  /// No description provided for @generalTextRandommodeChanged.
  ///
  /// In en, this message translates to:
  /// **'Random mode is changed'**
  String get generalTextRandommodeChanged;

  /// No description provided for @detailsConfigurationsectionChanged.
  ///
  /// In en, this message translates to:
  /// **'Configuration is changed'**
  String get detailsConfigurationsectionChanged;

  /// No description provided for @settingsSettimeFunctions.
  ///
  /// In en, this message translates to:
  /// **'Time functions are changed'**
  String get settingsSettimeFunctions;

  /// No description provided for @imprintContact.
  ///
  /// In en, this message translates to:
  /// **'Contact'**
  String get imprintContact;

  /// No description provided for @imprintPhone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get imprintPhone;

  /// No description provided for @imprintMail.
  ///
  /// In en, this message translates to:
  /// **'Mail'**
  String get imprintMail;

  /// No description provided for @imprintRegistrycourt.
  ///
  /// In en, this message translates to:
  /// **'Register court'**
  String get imprintRegistrycourt;

  /// No description provided for @imprintRegistrynumber.
  ///
  /// In en, this message translates to:
  /// **'Registration number'**
  String get imprintRegistrynumber;

  /// No description provided for @imprintCeo.
  ///
  /// In en, this message translates to:
  /// **'Managing Director'**
  String get imprintCeo;

  /// No description provided for @imprintTaxnumber.
  ///
  /// In en, this message translates to:
  /// **'Sales tax identification number'**
  String get imprintTaxnumber;

  /// No description provided for @settingsLocationCurrent.
  ///
  /// In en, this message translates to:
  /// **'Current location'**
  String get settingsLocationCurrent;

  /// No description provided for @generalTextReset.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get generalTextReset;

  /// No description provided for @discoverySearchStart.
  ///
  /// In en, this message translates to:
  /// **'Start search'**
  String get discoverySearchStart;

  /// No description provided for @discoverySearchStop.
  ///
  /// In en, this message translates to:
  /// **'Stop search'**
  String get discoverySearchStop;

  /// No description provided for @settingsImpulsSaved.
  ///
  /// In en, this message translates to:
  /// **'Impulse Switch Time is stored'**
  String get settingsImpulsSaved;

  /// No description provided for @settingsCentralNochannel.
  ///
  /// In en, this message translates to:
  /// **'There are no channels with the Central On/Off setting'**
  String get settingsCentralNochannel;

  /// No description provided for @settingsFactoryresetBluetoothConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth connection was successfully reset.'**
  String get settingsFactoryresetBluetoothConfirmationDescription;

  /// No description provided for @settingsFactoryresetBluetoothFailDescription.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth connections reset failed.'**
  String get settingsFactoryresetBluetoothFailDescription;

  /// No description provided for @imprintPublisher.
  ///
  /// In en, this message translates to:
  /// **'Publisher'**
  String get imprintPublisher;

  /// No description provided for @discoveryDeviceConnecting.
  ///
  /// In en, this message translates to:
  /// **'Connection is established'**
  String get discoveryDeviceConnecting;

  /// No description provided for @discoveryDeviceRestarting.
  ///
  /// In en, this message translates to:
  /// **'Restarting...'**
  String get discoveryDeviceRestarting;

  /// No description provided for @generalTextConfigurationsaved.
  ///
  /// In en, this message translates to:
  /// **'Channel configuration saved.'**
  String get generalTextConfigurationsaved;

  /// No description provided for @timerOverviewChannelssaved.
  ///
  /// In en, this message translates to:
  /// **'Save channels'**
  String get timerOverviewChannelssaved;

  /// No description provided for @timerOverviewSaved.
  ///
  /// In en, this message translates to:
  /// **'Timer saved'**
  String get timerOverviewSaved;

  /// No description provided for @timerSectionList.
  ///
  /// In en, this message translates to:
  /// **'List view'**
  String get timerSectionList;

  /// No description provided for @timerSectionDayview.
  ///
  /// In en, this message translates to:
  /// **'Day view'**
  String get timerSectionDayview;

  /// No description provided for @generalTextChannelInstructions.
  ///
  /// In en, this message translates to:
  /// **'Channel settings'**
  String get generalTextChannelInstructions;

  /// No description provided for @generalTextPublisher.
  ///
  /// In en, this message translates to:
  /// **'Publisher'**
  String get generalTextPublisher;

  /// No description provided for @settingsDeletetimerDialog.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to delete all programmes?'**
  String get settingsDeletetimerDialog;

  /// No description provided for @settingsFactoryresetResetbluetoothDialog.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to reset all Bluetooth settings?'**
  String get settingsFactoryresetResetbluetoothDialog;

  /// No description provided for @settingsCentralTogglecentral.
  ///
  /// In en, this message translates to:
  /// **'Central\nOn/Off'**
  String get settingsCentralTogglecentral;

  /// No description provided for @generalTextConfirmation.
  ///
  /// In en, this message translates to:
  /// **'{serviceName} change successful.'**
  String generalTextConfirmation(Object serviceName);

  /// No description provided for @generalTextFailed.
  ///
  /// In en, this message translates to:
  /// **'{serviceName} change failed.'**
  String generalTextFailed(Object serviceName);

  /// No description provided for @settingsChannelConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'Channels were successfully changed.'**
  String get settingsChannelConfirmationDescription;

  /// No description provided for @timerDetailsSaveHeader.
  ///
  /// In en, this message translates to:
  /// **'Save program'**
  String get timerDetailsSaveHeader;

  /// No description provided for @timerDetailsDeleteHeader.
  ///
  /// In en, this message translates to:
  /// **'Delete program'**
  String get timerDetailsDeleteHeader;

  /// No description provided for @timerDetailsSaveDescription.
  ///
  /// In en, this message translates to:
  /// **'Saving program successful.'**
  String get timerDetailsSaveDescription;

  /// No description provided for @timerDetailsDeleteDescription.
  ///
  /// In en, this message translates to:
  /// **'Deleting program successful.'**
  String get timerDetailsDeleteDescription;

  /// No description provided for @timerDetailsAlertweekdays.
  ///
  /// In en, this message translates to:
  /// **'The program can\'t be saved, because no weekdays are selected.'**
  String get timerDetailsAlertweekdays;

  /// No description provided for @generalTextOk.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get generalTextOk;

  /// No description provided for @settingsDatetimeChangesuccesfull.
  ///
  /// In en, this message translates to:
  /// **'Date and time were successfully changed.'**
  String get settingsDatetimeChangesuccesfull;

  /// No description provided for @discoveryConnectionFailed.
  ///
  /// In en, this message translates to:
  /// **'Connection failed'**
  String get discoveryConnectionFailed;

  /// No description provided for @discoveryDeviceResetrequired.
  ///
  /// In en, this message translates to:
  /// **'No connection could be established with the device. To solve this problem, delete the device from your Bluetooth settings. If the problem persists, please contact our technical support.'**
  String get discoveryDeviceResetrequired;

  /// No description provided for @generalTextSearch.
  ///
  /// In en, this message translates to:
  /// **'Search devices'**
  String get generalTextSearch;

  /// No description provided for @generalTextOr.
  ///
  /// In en, this message translates to:
  /// **'or'**
  String get generalTextOr;

  /// No description provided for @settingsFactoryresetProgramsConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'All programs were successfully deleted.'**
  String get settingsFactoryresetProgramsConfirmationDescription;

  /// No description provided for @generalTextManualentry.
  ///
  /// In en, this message translates to:
  /// **'Manual entry'**
  String get generalTextManualentry;

  /// No description provided for @settingsLocationSaved.
  ///
  /// In en, this message translates to:
  /// **'Location saved'**
  String get settingsLocationSaved;

  /// No description provided for @settingsLocationAutosearch.
  ///
  /// In en, this message translates to:
  /// **'Search location automatically'**
  String get settingsLocationAutosearch;

  /// No description provided for @imprintPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'+49 711 / 9435 0000'**
  String get imprintPhoneNumber;

  /// No description provided for @imprintMailAddress.
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get imprintMailAddress;

  /// No description provided for @settingsFactoryresetResetallDialog.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to reset the device to factory settings?'**
  String get settingsFactoryresetResetallDialog;

  /// No description provided for @settingsFactoryresetFactoryConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'The device has been successfully reset to factory settings.'**
  String get settingsFactoryresetFactoryConfirmationDescription;

  /// No description provided for @settingsFactoryresetFactoryFailDescription.
  ///
  /// In en, this message translates to:
  /// **'Device reset failed.'**
  String get settingsFactoryresetFactoryFailDescription;

  /// No description provided for @imprintPhoneNumberIos.
  ///
  /// In en, this message translates to:
  /// **'+49711/94350000'**
  String get imprintPhoneNumberIos;

  /// No description provided for @mfzFunctionA2Title.
  ///
  /// In en, this message translates to:
  /// **'2-stage response delay (A2)'**
  String get mfzFunctionA2Title;

  /// No description provided for @mfzFunctionA2TitleShort.
  ///
  /// In en, this message translates to:
  /// **'2-stage response delay (A2)'**
  String get mfzFunctionA2TitleShort;

  /// No description provided for @mfzFunctionA2Description.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied, time lapse T1 between 0 and 60 seconds begins. At the end of this period, contact 1-2 closes and the time lapse t2 between 0 and 60 seconds begins. At the end of this time, contact 3-4 closes. After an interruption, the time sequence starts again with t1.'**
  String get mfzFunctionA2Description;

  /// No description provided for @mfzFunctionRvTitle.
  ///
  /// In en, this message translates to:
  /// **'Off Delay (RV)'**
  String get mfzFunctionRvTitle;

  /// No description provided for @mfzFunctionRvTitleShort.
  ///
  /// In en, this message translates to:
  /// **'RV | Off Delay'**
  String get mfzFunctionRvTitleShort;

  /// No description provided for @mfzFunctionRvDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied the relay contact switches to 15-18. When the control voltage is interrupted the timing period is started; on time-out the relay contact returns to normal position. Resettable during the timing period.'**
  String get mfzFunctionRvDescription;

  /// No description provided for @mfzFunctionTiTitle.
  ///
  /// In en, this message translates to:
  /// **'Clock generator starting with impulse (TI)'**
  String get mfzFunctionTiTitle;

  /// No description provided for @mfzFunctionTiTitleShort.
  ///
  /// In en, this message translates to:
  /// **'TI | clock generator starting with impulse'**
  String get mfzFunctionTiTitleShort;

  /// No description provided for @mfzFunctionTiDescription.
  ///
  /// In en, this message translates to:
  /// **'As long as the control voltage is applied, the operating contact closes and opens. The change-over time in both directions can be set separately. When the control voltage is applied, the operating contact changes immediately to 15-18.'**
  String get mfzFunctionTiDescription;

  /// No description provided for @mfzFunctionAvTitle.
  ///
  /// In en, this message translates to:
  /// **'Operate delay (AV)'**
  String get mfzFunctionAvTitle;

  /// No description provided for @mfzFunctionAvTitleShort.
  ///
  /// In en, this message translates to:
  /// **'AV | operate delay'**
  String get mfzFunctionAvTitleShort;

  /// No description provided for @mfzFunctionAvDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied the timing period is started; on time-out the relay contact changes to 15-18. After an interruption, the timing period is restarted.'**
  String get mfzFunctionAvDescription;

  /// No description provided for @mfzFunctionAvPlusTitle.
  ///
  /// In en, this message translates to:
  /// **'Operate delay additive (AV+)'**
  String get mfzFunctionAvPlusTitle;

  /// No description provided for @mfzFunctionAvPlusTitleShort.
  ///
  /// In en, this message translates to:
  /// **'AV+ | operate delay additive'**
  String get mfzFunctionAvPlusTitleShort;

  /// No description provided for @mfzFunctionAvPlusDescription.
  ///
  /// In en, this message translates to:
  /// **'Function same as AV. However, after an interruption the elapsed time is stored.'**
  String get mfzFunctionAvPlusDescription;

  /// No description provided for @mfzFunctionAwTitle.
  ///
  /// In en, this message translates to:
  /// **'Fleeting NC contact (AW)'**
  String get mfzFunctionAwTitle;

  /// No description provided for @mfzFunctionAwTitleShort.
  ///
  /// In en, this message translates to:
  /// **'AW | fleeting NC contact'**
  String get mfzFunctionAwTitleShort;

  /// No description provided for @mfzFunctionAwDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is interrupted the NO contact changes to 15-18, and reverts on wiping time-out. If the control voltage is applied during the wiping time the NO contact immediately reverts to 15-16 and the residual time is cancelled.'**
  String get mfzFunctionAwDescription;

  /// No description provided for @mfzFunctionIfTitle.
  ///
  /// In en, this message translates to:
  /// **'Pulse shaper (IF)'**
  String get mfzFunctionIfTitle;

  /// No description provided for @mfzFunctionIfTitleShort.
  ///
  /// In en, this message translates to:
  /// **'IF | pulse shaper'**
  String get mfzFunctionIfTitleShort;

  /// No description provided for @mfzFunctionIfDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied the relay contact changes to 15-18 for the set time. Further control impulses are evaluated only after the set time has elapsed.'**
  String get mfzFunctionIfDescription;

  /// No description provided for @mfzFunctionEwTitle.
  ///
  /// In en, this message translates to:
  /// **'Fleeting NO contact (EW)'**
  String get mfzFunctionEwTitle;

  /// No description provided for @mfzFunctionEwTitleShort.
  ///
  /// In en, this message translates to:
  /// **'EW | fleeting NO contact'**
  String get mfzFunctionEwTitleShort;

  /// No description provided for @mfzFunctionEwDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied the NO contact changes to 15-18 and reverts on wiping time-out. If the control voltage is removed during the wiping time the NO contact immediately reverts to 15-16 and the residual time is cancelled.'**
  String get mfzFunctionEwDescription;

  /// No description provided for @mfzFunctionEawTitle.
  ///
  /// In en, this message translates to:
  /// **'Fleeting NO contact and fleeting NC contact (EAW)'**
  String get mfzFunctionEawTitle;

  /// No description provided for @mfzFunctionEawTitleShort.
  ///
  /// In en, this message translates to:
  /// **'EAW | fleeting NO contact and fleeting NC contact'**
  String get mfzFunctionEawTitleShort;

  /// No description provided for @mfzFunctionEawDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied or interrupted the relay contact changes to 15-18 and reverts after the set wiping time.'**
  String get mfzFunctionEawDescription;

  /// No description provided for @mfzFunctionTpTitle.
  ///
  /// In en, this message translates to:
  /// **'Clock generator starting with pause (TP)'**
  String get mfzFunctionTpTitle;

  /// No description provided for @mfzFunctionTpTitleShort.
  ///
  /// In en, this message translates to:
  /// **'TP | clock generator starting with pause'**
  String get mfzFunctionTpTitleShort;

  /// No description provided for @mfzFunctionTpDescription.
  ///
  /// In en, this message translates to:
  /// **'Description of function same as for TI, except that, when the control voltage is applied, the contact initially remains at 15-16 rather than changing to 15-18.'**
  String get mfzFunctionTpDescription;

  /// No description provided for @mfzFunctionIaTitle.
  ///
  /// In en, this message translates to:
  /// **'Impulse controlled pickup delay (e.g. automatic door opener) (IA)'**
  String get mfzFunctionIaTitle;

  /// No description provided for @mfzFunctionIaTitleShort.
  ///
  /// In en, this message translates to:
  /// **'IA | impulse controlled pickup delay'**
  String get mfzFunctionIaTitleShort;

  /// No description provided for @mfzFunctionIaDescription.
  ///
  /// In en, this message translates to:
  /// **'The timing period t1 starts with a control impulse from 50ms; on time-out the relay contact changes for the timing period t2 to 15-18 for 1 second (e.g. for automatic door opener). If t1 is set to t1 min = 0.1 seconds, the IA operates as pulse shaper, when timing period t2 elapses, independent of the duration of the control impulse (min. 150 ms).'**
  String get mfzFunctionIaDescription;

  /// No description provided for @mfzFunctionArvTitle.
  ///
  /// In en, this message translates to:
  /// **'Operate and release delay (ARV)'**
  String get mfzFunctionArvTitle;

  /// No description provided for @mfzFunctionArvTitleShort.
  ///
  /// In en, this message translates to:
  /// **'ARV | operate and release delay'**
  String get mfzFunctionArvTitleShort;

  /// No description provided for @mfzFunctionArvDescription.
  ///
  /// In en, this message translates to:
  /// **'When the control voltage is applied, the timeout begins, at the end of which the operating contact changes to 15 -18. If the control voltage is then interrupted, a new timeout begins, at the end of which the operating contact returns to the rest position.\nAfter an interruption of the response delay, the timeout starts again.'**
  String get mfzFunctionArvDescription;

  /// No description provided for @mfzFunctionArvPlusTitle.
  ///
  /// In en, this message translates to:
  /// **'Operate and release delay additive (ARV+)'**
  String get mfzFunctionArvPlusTitle;

  /// No description provided for @mfzFunctionArvPlusTitleShort.
  ///
  /// In en, this message translates to:
  /// **'ARV+ | operate and release delay additive'**
  String get mfzFunctionArvPlusTitleShort;

  /// No description provided for @mfzFunctionArvPlusDescription.
  ///
  /// In en, this message translates to:
  /// **'Same function as ARV, but after an interruption of the operate delay the elapsed time is stored.'**
  String get mfzFunctionArvPlusDescription;

  /// No description provided for @mfzFunctionEsTitle.
  ///
  /// In en, this message translates to:
  /// **'Impulse switch (ES)'**
  String get mfzFunctionEsTitle;

  /// No description provided for @mfzFunctionEsTitleShort.
  ///
  /// In en, this message translates to:
  /// **'ES | impulse switch'**
  String get mfzFunctionEsTitleShort;

  /// No description provided for @mfzFunctionEsDescription.
  ///
  /// In en, this message translates to:
  /// **'With control impulses from 50ms the make contact switches to and from.'**
  String get mfzFunctionEsDescription;

  /// No description provided for @mfzFunctionEsvTitle.
  ///
  /// In en, this message translates to:
  /// **'Impulse switch with release delay and switch-off early-warning function (ESV)'**
  String get mfzFunctionEsvTitle;

  /// No description provided for @mfzFunctionEsvTitleShort.
  ///
  /// In en, this message translates to:
  /// **'ESV | impulse switch with release delay and switch-off early-warning function'**
  String get mfzFunctionEsvTitleShort;

  /// No description provided for @mfzFunctionEsvDescription.
  ///
  /// In en, this message translates to:
  /// **'Function same as SRV. Additionally with switch-off early warning: approx. 30 sec. before time-out the lighting starts flickering 3 times at gradually shorter time intervals.'**
  String get mfzFunctionEsvDescription;

  /// No description provided for @mfzFunctionErTitle.
  ///
  /// In en, this message translates to:
  /// **'Relay (ER)'**
  String get mfzFunctionErTitle;

  /// No description provided for @mfzFunctionErTitleShort.
  ///
  /// In en, this message translates to:
  /// **'ER | relay'**
  String get mfzFunctionErTitleShort;

  /// No description provided for @mfzFunctionErDescription.
  ///
  /// In en, this message translates to:
  /// **'As long as the control contact is closed the make contact reverts from 15-16 to 15-18.'**
  String get mfzFunctionErDescription;

  /// No description provided for @mfzFunctionSrvTitle.
  ///
  /// In en, this message translates to:
  /// **'Release-delay impulse switch (SRV)'**
  String get mfzFunctionSrvTitle;

  /// No description provided for @mfzFunctionSrvTitleShort.
  ///
  /// In en, this message translates to:
  /// **'SRV | release-delay impulse switch'**
  String get mfzFunctionSrvTitleShort;

  /// No description provided for @mfzFunctionSrvDescription.
  ///
  /// In en, this message translates to:
  /// **'With control impulses from 50ms the make contact switches to and fro. In the contact position 15-18, the device switches automatically to the rest position 15-16 on delay time-out.'**
  String get mfzFunctionSrvDescription;

  /// No description provided for @detailsFunctionsHeader.
  ///
  /// In en, this message translates to:
  /// **'Functions'**
  String get detailsFunctionsHeader;

  /// No description provided for @mfzFunctionTimeHeader.
  ///
  /// In en, this message translates to:
  /// **'Time (t{index})'**
  String mfzFunctionTimeHeader(Object index);

  /// No description provided for @mfzFunctionOnDescription.
  ///
  /// In en, this message translates to:
  /// **'permanent ON'**
  String get mfzFunctionOnDescription;

  /// No description provided for @mfzFunctionOffDescription.
  ///
  /// In en, this message translates to:
  /// **'permanent OFF'**
  String get mfzFunctionOffDescription;

  /// No description provided for @mfzFunctionMultiplier.
  ///
  /// In en, this message translates to:
  /// **'Factor'**
  String get mfzFunctionMultiplier;

  /// No description provided for @discoveryMfz12Description.
  ///
  /// In en, this message translates to:
  /// **'Multifunction time relay Bluetooth'**
  String get discoveryMfz12Description;

  /// No description provided for @mfzFunctionOnTitle.
  ///
  /// In en, this message translates to:
  /// **'permanent ON'**
  String get mfzFunctionOnTitle;

  /// No description provided for @mfzFunctionOnTitleShort.
  ///
  /// In en, this message translates to:
  /// **'permanent ON'**
  String get mfzFunctionOnTitleShort;

  /// No description provided for @mfzFunctionOffTitle.
  ///
  /// In en, this message translates to:
  /// **'permanent OFF'**
  String get mfzFunctionOffTitle;

  /// No description provided for @mfzFunctionOffTitleShort.
  ///
  /// In en, this message translates to:
  /// **'permanent OFF'**
  String get mfzFunctionOffTitleShort;

  /// No description provided for @mfzMultiplierSecondsFloatingpoint.
  ///
  /// In en, this message translates to:
  /// **'0.1 seconds'**
  String get mfzMultiplierSecondsFloatingpoint;

  /// No description provided for @mfzMultiplierMinutesFloatingpoint.
  ///
  /// In en, this message translates to:
  /// **'0.1 minutes'**
  String get mfzMultiplierMinutesFloatingpoint;

  /// No description provided for @mfzMultiplierHoursFloatingpoint.
  ///
  /// In en, this message translates to:
  /// **'0.1 hours'**
  String get mfzMultiplierHoursFloatingpoint;

  /// No description provided for @mfzOverviewFunctionsloaded.
  ///
  /// In en, this message translates to:
  /// **'Functions are loaded'**
  String get mfzOverviewFunctionsloaded;

  /// No description provided for @mfzOverviewSaved.
  ///
  /// In en, this message translates to:
  /// **'Function saved'**
  String get mfzOverviewSaved;

  /// No description provided for @settingsBluetoothHeader.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth'**
  String get settingsBluetoothHeader;

  /// No description provided for @bluetoothChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth setting has been successfully changed.'**
  String get bluetoothChangedSuccessfully;

  /// No description provided for @settingsBluetoothInformation.
  ///
  /// In en, this message translates to:
  /// **'Note: If this setting is activated, the device is permanently visible to everyone via Bluetooth!\nIt is recommended to change the device PIN.'**
  String get settingsBluetoothInformation;

  /// No description provided for @settingsBluetoothContinuousconnection.
  ///
  /// In en, this message translates to:
  /// **'Permanent visibility'**
  String get settingsBluetoothContinuousconnection;

  /// No description provided for @settingsBluetoothContinuousconnectionDescription.
  ///
  /// In en, this message translates to:
  /// **'By enabling persistent visibility, Bluetooth remains active on the device ({deviceType}) and does not need to be manually activated before establishing a connection.'**
  String settingsBluetoothContinuousconnectionDescription(Object deviceType);

  /// No description provided for @settingsBluetoothTimeout.
  ///
  /// In en, this message translates to:
  /// **'Connection timeout'**
  String get settingsBluetoothTimeout;

  /// No description provided for @settingsBluetoothPinlimit.
  ///
  /// In en, this message translates to:
  /// **'PIN limit'**
  String get settingsBluetoothPinlimit;

  /// No description provided for @settingsBluetoothTimeoutDescription.
  ///
  /// In en, this message translates to:
  /// **'The connection is disconnected after {timeout} minutes of inactivity.'**
  String settingsBluetoothTimeoutDescription(Object timeout);

  /// No description provided for @settingsBluetoothPinlimitDescription.
  ///
  /// In en, this message translates to:
  /// **'For security reasons, you have a maximum of {attempts} attempts for entering the PIN. Bluetooth is then deactivated and must be manually reactivated for a new connection.'**
  String settingsBluetoothPinlimitDescription(Object attempts);

  /// No description provided for @settingsBluetoothPinAttempts.
  ///
  /// In en, this message translates to:
  /// **'attempts'**
  String get settingsBluetoothPinAttempts;

  /// No description provided for @settingsResetfunctionHeader.
  ///
  /// In en, this message translates to:
  /// **'Reset functions'**
  String get settingsResetfunctionHeader;

  /// No description provided for @settingsResetfunctionDialog.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to reset all functions?'**
  String get settingsResetfunctionDialog;

  /// No description provided for @settingsFactoryresetFunctionsConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'All functions have been successfully reset.'**
  String get settingsFactoryresetFunctionsConfirmationDescription;

  /// No description provided for @mfzFunctionTime.
  ///
  /// In en, this message translates to:
  /// **'Time (t)'**
  String get mfzFunctionTime;

  /// No description provided for @discoveryConnectionFailedInfo.
  ///
  /// In en, this message translates to:
  /// **'No Bluetooth connection'**
  String get discoveryConnectionFailedInfo;

  /// No description provided for @detailsConfigurationDevicedisplaylockDialogtext.
  ///
  /// In en, this message translates to:
  /// **'Immediately after locking the device display, Bluetooth gets deactivated and has to be reactivated manually in order to establish a new connection.'**
  String get detailsConfigurationDevicedisplaylockDialogtext;

  /// No description provided for @detailsConfigurationDevicedisplaylockDialogquestion.
  ///
  /// In en, this message translates to:
  /// **'Are you sure to lock the device display?'**
  String get detailsConfigurationDevicedisplaylockDialogquestion;

  /// No description provided for @settingsDemodevices.
  ///
  /// In en, this message translates to:
  /// **'Show demo devices'**
  String get settingsDemodevices;

  /// No description provided for @generalTextSettings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get generalTextSettings;

  /// No description provided for @discoveryWifi.
  ///
  /// In en, this message translates to:
  /// **'WiFi'**
  String get discoveryWifi;

  /// No description provided for @settingsInformations.
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get settingsInformations;

  /// No description provided for @detailsConfigurationDimmingbehavior.
  ///
  /// In en, this message translates to:
  /// **'Dimming behaviour'**
  String get detailsConfigurationDimmingbehavior;

  /// No description provided for @detailsConfigurationSwitchbehavior.
  ///
  /// In en, this message translates to:
  /// **'Switch behaviour'**
  String get detailsConfigurationSwitchbehavior;

  /// No description provided for @detailsConfigurationBrightness.
  ///
  /// In en, this message translates to:
  /// **'Brightness'**
  String get detailsConfigurationBrightness;

  /// No description provided for @detailsConfigurationMinimum.
  ///
  /// In en, this message translates to:
  /// **'Minimum'**
  String get detailsConfigurationMinimum;

  /// No description provided for @detailsConfigurationMaximum.
  ///
  /// In en, this message translates to:
  /// **'Maximum'**
  String get detailsConfigurationMaximum;

  /// No description provided for @detailsConfigurationSwitchesGr.
  ///
  /// In en, this message translates to:
  /// **'Group relay (GR)'**
  String get detailsConfigurationSwitchesGr;

  /// No description provided for @detailsConfigurationSwitchesGs.
  ///
  /// In en, this message translates to:
  /// **'Group switch (GS)'**
  String get detailsConfigurationSwitchesGs;

  /// No description provided for @detailsConfigurationSwitchesCloserer.
  ///
  /// In en, this message translates to:
  /// **'Normally opened contact (NO/ER)'**
  String get detailsConfigurationSwitchesCloserer;

  /// No description provided for @detailsConfigurationSwitchesClosererDescription.
  ///
  /// In en, this message translates to:
  /// **'Off -> Pushbutton pressed (On) -> Release (Off)'**
  String get detailsConfigurationSwitchesClosererDescription;

  /// No description provided for @detailsConfigurationSwitchesOpenerer.
  ///
  /// In en, this message translates to:
  /// **'Normally closed contact (NC/ER-Invers)'**
  String get detailsConfigurationSwitchesOpenerer;

  /// No description provided for @detailsConfigurationSwitchesOpenererDescription.
  ///
  /// In en, this message translates to:
  /// **'On -> Pushbutton pressed (Off) -> Release (On)'**
  String get detailsConfigurationSwitchesOpenererDescription;

  /// No description provided for @detailsConfigurationSwitchesSwitch.
  ///
  /// In en, this message translates to:
  /// **'Switch'**
  String get detailsConfigurationSwitchesSwitch;

  /// No description provided for @detailsConfigurationSwitchesSwitchDescription.
  ///
  /// In en, this message translates to:
  /// **'With each switch change, the light is switched on and off'**
  String get detailsConfigurationSwitchesSwitchDescription;

  /// No description provided for @detailsConfigurationSwitchesImpulsswitch.
  ///
  /// In en, this message translates to:
  /// **'Impulse switch'**
  String get detailsConfigurationSwitchesImpulsswitch;

  /// No description provided for @detailsConfigurationSwitchesImpulsswitchDescription.
  ///
  /// In en, this message translates to:
  /// **'Pushbutton is briefly pressed and released to turn the light on or off'**
  String get detailsConfigurationSwitchesImpulsswitchDescription;

  /// No description provided for @detailsConfigurationSwitchesClosererDescription2.
  ///
  /// In en, this message translates to:
  /// **'As long as the pushbutton is pressed, the motor is running.'**
  String get detailsConfigurationSwitchesClosererDescription2;

  /// No description provided for @detailsConfigurationSwitchesImpulsswitchDescription2.
  ///
  /// In en, this message translates to:
  /// **'Pushbutton is briefly pressed to start the motor and briefly pressed to stop it again'**
  String get detailsConfigurationSwitchesImpulsswitchDescription2;

  /// No description provided for @detailsConfigurationWifiloginScan.
  ///
  /// In en, this message translates to:
  /// **'Scan QR-Code'**
  String get detailsConfigurationWifiloginScan;

  /// No description provided for @detailsConfigurationWifiloginScannotvalid.
  ///
  /// In en, this message translates to:
  /// **'Scanned code is not valid'**
  String get detailsConfigurationWifiloginScannotvalid;

  /// No description provided for @detailsConfigurationWifiloginDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter code'**
  String get detailsConfigurationWifiloginDescription;

  /// No description provided for @detailsConfigurationWifiloginPassword.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get detailsConfigurationWifiloginPassword;

  /// No description provided for @discoveryEsbipDescription.
  ///
  /// In en, this message translates to:
  /// **'Shutter and blind actuator IP'**
  String get discoveryEsbipDescription;

  /// No description provided for @discoveryEsripDescription.
  ///
  /// In en, this message translates to:
  /// **'Impulse switching relay IP'**
  String get discoveryEsripDescription;

  /// No description provided for @discoveryEudipDescription.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch IP'**
  String get discoveryEudipDescription;

  /// No description provided for @generalTextLoad.
  ///
  /// In en, this message translates to:
  /// **'Loading'**
  String get generalTextLoad;

  /// No description provided for @wifiBasicautomationsNotFound.
  ///
  /// In en, this message translates to:
  /// **'No automation found.'**
  String get wifiBasicautomationsNotFound;

  /// No description provided for @wifiCodeInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid code'**
  String get wifiCodeInvalid;

  /// No description provided for @wifiCodeValid.
  ///
  /// In en, this message translates to:
  /// **'Valid code'**
  String get wifiCodeValid;

  /// No description provided for @wifiAuthorizationLogin.
  ///
  /// In en, this message translates to:
  /// **'Connect'**
  String get wifiAuthorizationLogin;

  /// No description provided for @wifiAuthorizationLoginFailed.
  ///
  /// In en, this message translates to:
  /// **'Log in failed'**
  String get wifiAuthorizationLoginFailed;

  /// No description provided for @wifiAuthorizationSerialnumber.
  ///
  /// In en, this message translates to:
  /// **'Serial number'**
  String get wifiAuthorizationSerialnumber;

  /// No description provided for @wifiAuthorizationProductiondate.
  ///
  /// In en, this message translates to:
  /// **'Production date'**
  String get wifiAuthorizationProductiondate;

  /// No description provided for @wifiAuthorizationProofofpossession.
  ///
  /// In en, this message translates to:
  /// **'PoP'**
  String get wifiAuthorizationProofofpossession;

  /// No description provided for @generalTextWifipassword.
  ///
  /// In en, this message translates to:
  /// **'WiFi password'**
  String get generalTextWifipassword;

  /// No description provided for @generalTextUsername.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get generalTextUsername;

  /// No description provided for @generalTextEnter.
  ///
  /// In en, this message translates to:
  /// **'OR ENTER MANUALLY'**
  String get generalTextEnter;

  /// No description provided for @wifiAuthorizationScan.
  ///
  /// In en, this message translates to:
  /// **'Scan the ELTAKO code.'**
  String get wifiAuthorizationScan;

  /// No description provided for @detailsConfigurationDevicesNofunctionshinttext.
  ///
  /// In en, this message translates to:
  /// **'This device currently does not support any other settings'**
  String get detailsConfigurationDevicesNofunctionshinttext;

  /// No description provided for @settingsUsedemodelay.
  ///
  /// In en, this message translates to:
  /// **'Use demo delay'**
  String get settingsUsedemodelay;

  /// No description provided for @settingsImpulsLoad.
  ///
  /// In en, this message translates to:
  /// **'Impulse switching time is loaded'**
  String get settingsImpulsLoad;

  /// No description provided for @settingsBluetoothLoad.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth setting is being loaded.'**
  String get settingsBluetoothLoad;

  /// No description provided for @detailsConfigurationsectionLoad.
  ///
  /// In en, this message translates to:
  /// **'Configurations are loaded'**
  String get detailsConfigurationsectionLoad;

  /// No description provided for @generalTextLogin.
  ///
  /// In en, this message translates to:
  /// **'Log in'**
  String get generalTextLogin;

  /// No description provided for @generalTextAuthentication.
  ///
  /// In en, this message translates to:
  /// **'Authenticate'**
  String get generalTextAuthentication;

  /// No description provided for @wifiAuthorizationScanDescription.
  ///
  /// In en, this message translates to:
  /// **'Look for the ELTAKO code on the WiFi device or on the included info sheet and align it in the camera frame at the top.'**
  String get wifiAuthorizationScanDescription;

  /// No description provided for @wifiAuthorizationScanShort.
  ///
  /// In en, this message translates to:
  /// **'Scan ELTAKO code'**
  String get wifiAuthorizationScanShort;

  /// No description provided for @detailsConfigurationEdgemode.
  ///
  /// In en, this message translates to:
  /// **'Dimming curve'**
  String get detailsConfigurationEdgemode;

  /// No description provided for @detailsConfigurationEdgemodeLeadingedge.
  ///
  /// In en, this message translates to:
  /// **'Leading edge'**
  String get detailsConfigurationEdgemodeLeadingedge;

  /// No description provided for @generalTextNetwork.
  ///
  /// In en, this message translates to:
  /// **'Network'**
  String get generalTextNetwork;

  /// No description provided for @wifiAuthenticationSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Authentication successful'**
  String get wifiAuthenticationSuccessful;

  /// No description provided for @detailsConfigurationsectionSavechange.
  ///
  /// In en, this message translates to:
  /// **'Configuration Changed'**
  String get detailsConfigurationsectionSavechange;

  /// No description provided for @discoveryWifiAdddevice.
  ///
  /// In en, this message translates to:
  /// **'Add WiFi device'**
  String get discoveryWifiAdddevice;

  /// No description provided for @wifiAuthenticationDelay.
  ///
  /// In en, this message translates to:
  /// **'This can last up to 1 minute'**
  String get wifiAuthenticationDelay;

  /// No description provided for @generalTextRetry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get generalTextRetry;

  /// No description provided for @wifiAuthenticationCredentials.
  ///
  /// In en, this message translates to:
  /// **'Please enter the password of your WiFi'**
  String get wifiAuthenticationCredentials;

  /// No description provided for @wifiAuthenticationSsid.
  ///
  /// In en, this message translates to:
  /// **'SSID'**
  String get wifiAuthenticationSsid;

  /// No description provided for @wifiAuthenticationDelaylong.
  ///
  /// In en, this message translates to:
  /// **'It can last up to 1 minute until the device is ready and appears in the app'**
  String get wifiAuthenticationDelaylong;

  /// No description provided for @wifiAuthenticationCredentialsShort.
  ///
  /// In en, this message translates to:
  /// **'Enter WiFi password'**
  String get wifiAuthenticationCredentialsShort;

  /// No description provided for @wifiAuthenticationTeachin.
  ///
  /// In en, this message translates to:
  /// **'Teach in the device into the WiFi'**
  String get wifiAuthenticationTeachin;

  /// No description provided for @wifiAuthenticationEstablish.
  ///
  /// In en, this message translates to:
  /// **'Establish connection to the device'**
  String get wifiAuthenticationEstablish;

  /// No description provided for @wifiAuthenticationEstablishLong.
  ///
  /// In en, this message translates to:
  /// **'Device connects to WiFi {ssid}'**
  String wifiAuthenticationEstablishLong(Object ssid);

  /// No description provided for @wifiAuthenticationFailed.
  ///
  /// In en, this message translates to:
  /// **'Connection failed. Disconnect the device from power for a few seconds and retry to connect it'**
  String get wifiAuthenticationFailed;

  /// No description provided for @wifiAuthenticationReset.
  ///
  /// In en, this message translates to:
  /// **'Reset authentication'**
  String get wifiAuthenticationReset;

  /// No description provided for @wifiAuthenticationResetHint.
  ///
  /// In en, this message translates to:
  /// **'All authentication data will be deleted.'**
  String get wifiAuthenticationResetHint;

  /// No description provided for @wifiAuthenticationInvaliddata.
  ///
  /// In en, this message translates to:
  /// **'Authentication data invalid'**
  String get wifiAuthenticationInvaliddata;

  /// No description provided for @wifiAuthenticationReauthenticate.
  ///
  /// In en, this message translates to:
  /// **'Authenticate again'**
  String get wifiAuthenticationReauthenticate;

  /// No description provided for @wifiAddhkdeviceHeader.
  ///
  /// In en, this message translates to:
  /// **'Add device'**
  String get wifiAddhkdeviceHeader;

  /// No description provided for @wifiAddhkdeviceDescription.
  ///
  /// In en, this message translates to:
  /// **'Connect your new ELTAKO device to your WiFi via the Apple Home app.'**
  String get wifiAddhkdeviceDescription;

  /// No description provided for @wifiAddhkdeviceStep1.
  ///
  /// In en, this message translates to:
  /// **'1. Open the Apple Home app.'**
  String get wifiAddhkdeviceStep1;

  /// No description provided for @wifiAddhkdeviceStep2.
  ///
  /// In en, this message translates to:
  /// **'2. Click on the plus in the top right corner of the app and select **Add Device**.'**
  String get wifiAddhkdeviceStep2;

  /// No description provided for @wifiAddhkdeviceStep3.
  ///
  /// In en, this message translates to:
  /// **'3. Follow the instructions of the app.'**
  String get wifiAddhkdeviceStep3;

  /// No description provided for @wifiAddhkdeviceStep4.
  ///
  /// In en, this message translates to:
  /// **'4. Now your device can be configured in the ELTAKO Connect app.'**
  String get wifiAddhkdeviceStep4;

  /// No description provided for @detailsConfigurationRuntime.
  ///
  /// In en, this message translates to:
  /// **'Runtime'**
  String get detailsConfigurationRuntime;

  /// No description provided for @detailsConfigurationRuntimeMode.
  ///
  /// In en, this message translates to:
  /// **'Mode'**
  String get detailsConfigurationRuntimeMode;

  /// No description provided for @generalTextManually.
  ///
  /// In en, this message translates to:
  /// **'Manually'**
  String get generalTextManually;

  /// No description provided for @detailsConfigurationRuntimeAutoDescription.
  ///
  /// In en, this message translates to:
  /// **'The shading actuator independently determines the runtime of the shading motor during each movement from the lower to the upper end position (recommended).\nAfter initial startup or changes, such a movement should be performed from the bottom to the top without interruption.'**
  String get detailsConfigurationRuntimeAutoDescription;

  /// No description provided for @detailsConfigurationRuntimeManuallyDescription.
  ///
  /// In en, this message translates to:
  /// **'The runtime of the shading motor is set manually via the duration below.\nPlease make sure that the configured runtime matches the actual runtime of your shading motor. \nAfter initial startup or changes, such a movement should be performed from the bottom to the top without interruption.'**
  String get detailsConfigurationRuntimeManuallyDescription;

  /// No description provided for @detailsConfigurationRuntimeDemoDescription.
  ///
  /// In en, this message translates to:
  /// **'LCD mode is only available via REST API'**
  String get detailsConfigurationRuntimeDemoDescription;

  /// No description provided for @generalTextDemomodeActive.
  ///
  /// In en, this message translates to:
  /// **'Demo mode active'**
  String get generalTextDemomodeActive;

  /// No description provided for @detailsConfigurationRuntimeDuration.
  ///
  /// In en, this message translates to:
  /// **'Duration'**
  String get detailsConfigurationRuntimeDuration;

  /// No description provided for @detailsConfigurationSwitchesGs4.
  ///
  /// In en, this message translates to:
  /// **'Group switch (GS4)'**
  String get detailsConfigurationSwitchesGs4;

  /// No description provided for @detailsConfigurationSwitchesGs4Description.
  ///
  /// In en, this message translates to:
  /// **'Group switch with jog reversing function for controlling blinds'**
  String get detailsConfigurationSwitchesGs4Description;

  /// No description provided for @screenshotSu12.
  ///
  /// In en, this message translates to:
  /// **'Outdoor Light'**
  String get screenshotSu12;

  /// No description provided for @screenshotS2U12.
  ///
  /// In en, this message translates to:
  /// **'Outdoor Light'**
  String get screenshotS2U12;

  /// No description provided for @screenshotMfz12.
  ///
  /// In en, this message translates to:
  /// **'Pump'**
  String get screenshotMfz12;

  /// No description provided for @screenshotEsr62.
  ///
  /// In en, this message translates to:
  /// **'Lamp'**
  String get screenshotEsr62;

  /// No description provided for @screenshotEud62.
  ///
  /// In en, this message translates to:
  /// **'Ceiling light'**
  String get screenshotEud62;

  /// No description provided for @screenshotEsb62.
  ///
  /// In en, this message translates to:
  /// **'Shutters balcony'**
  String get screenshotEsb62;

  /// No description provided for @detailsConfigurationEdgemodeLeadingedgeDescription.
  ///
  /// In en, this message translates to:
  /// **'LC1-LC3 are comfort positions with different dimming curves for dimmable 230 V LED lamps, which cannot be dimmed far enough on AUTO due to their design and must therefore be forced to phase angle control.'**
  String get detailsConfigurationEdgemodeLeadingedgeDescription;

  /// No description provided for @detailsConfigurationEdgemodeAutoDescription.
  ///
  /// In en, this message translates to:
  /// **'AUTO allows dimming of all types of lamps.'**
  String get detailsConfigurationEdgemodeAutoDescription;

  /// No description provided for @detailsConfigurationEdgemodeTrailingedge.
  ///
  /// In en, this message translates to:
  /// **'Trailing edge'**
  String get detailsConfigurationEdgemodeTrailingedge;

  /// No description provided for @detailsConfigurationEdgemodeTrailingedgeDescription.
  ///
  /// In en, this message translates to:
  /// **'LC4-LC6 are comfort positions with different dimming curves for dimmable 230 V LED lamps.'**
  String get detailsConfigurationEdgemodeTrailingedgeDescription;

  /// No description provided for @updateHeader.
  ///
  /// In en, this message translates to:
  /// **'Firmware Update'**
  String get updateHeader;

  /// No description provided for @updateTitleStepSearch.
  ///
  /// In en, this message translates to:
  /// **'Searching for an update'**
  String get updateTitleStepSearch;

  /// No description provided for @updateTitleStepFound.
  ///
  /// In en, this message translates to:
  /// **'An update was found'**
  String get updateTitleStepFound;

  /// No description provided for @updateTitleStepDownload.
  ///
  /// In en, this message translates to:
  /// **'Downloading update'**
  String get updateTitleStepDownload;

  /// No description provided for @updateTitleStepInstall.
  ///
  /// In en, this message translates to:
  /// **'Installing update'**
  String get updateTitleStepInstall;

  /// No description provided for @updateTitleStepSuccess.
  ///
  /// In en, this message translates to:
  /// **'Update successful'**
  String get updateTitleStepSuccess;

  /// No description provided for @updateTitleStepUptodate.
  ///
  /// In en, this message translates to:
  /// **'Already up to date'**
  String get updateTitleStepUptodate;

  /// No description provided for @updateTitleStepFailed.
  ///
  /// In en, this message translates to:
  /// **'Update failed'**
  String get updateTitleStepFailed;

  /// No description provided for @updateButtonSearch.
  ///
  /// In en, this message translates to:
  /// **'Search for updates'**
  String get updateButtonSearch;

  /// No description provided for @updateButtonInstall.
  ///
  /// In en, this message translates to:
  /// **'Install update'**
  String get updateButtonInstall;

  /// No description provided for @updateCurrentversion.
  ///
  /// In en, this message translates to:
  /// **'Current version'**
  String get updateCurrentversion;

  /// No description provided for @updateNewversion.
  ///
  /// In en, this message translates to:
  /// **'New firmware update available'**
  String get updateNewversion;

  /// No description provided for @updateHintPower.
  ///
  /// In en, this message translates to:
  /// **'The update only starts when the output of the device is not active. The device should not be disconnected from the power supply and the app should not be exited during the update!'**
  String get updateHintPower;

  /// No description provided for @updateButton.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get updateButton;

  /// No description provided for @updateHintCompatibility.
  ///
  /// In en, this message translates to:
  /// **'An update is recommended, otherwise some functions in the app will be limited.'**
  String get updateHintCompatibility;

  /// No description provided for @generalTextDetails.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get generalTextDetails;

  /// No description provided for @updateMessageStepMetadata.
  ///
  /// In en, this message translates to:
  /// **'Loading update information'**
  String get updateMessageStepMetadata;

  /// No description provided for @updateMessageStepPrepare.
  ///
  /// In en, this message translates to:
  /// **'Update is being prepared'**
  String get updateMessageStepPrepare;

  /// No description provided for @updateTitleStepUpdatesuccessful.
  ///
  /// In en, this message translates to:
  /// **'Update is being checked'**
  String get updateTitleStepUpdatesuccessful;

  /// No description provided for @updateTextStepFailed.
  ///
  /// In en, this message translates to:
  /// **'Unfortunately something went wrong during the update, try again in a few minutes or wait until your device updates automatically (internet connection required).'**
  String get updateTextStepFailed;

  /// No description provided for @configurationsNotavailable.
  ///
  /// In en, this message translates to:
  /// **'There are no configurations available yet'**
  String get configurationsNotavailable;

  /// Now available for all devices not only Bluetooth
  ///
  /// In en, this message translates to:
  /// **'Create new configurations by connecting to a device and saving a configuration.'**
  String get configurationsAddHint;

  /// No description provided for @configurationsEdit.
  ///
  /// In en, this message translates to:
  /// **'Edit configuration'**
  String get configurationsEdit;

  /// No description provided for @generalTextName.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get generalTextName;

  /// No description provided for @configurationsDelete.
  ///
  /// In en, this message translates to:
  /// **'Delete configuration'**
  String get configurationsDelete;

  /// No description provided for @configurationsDeleteHint.
  ///
  /// In en, this message translates to:
  /// **'Should the configuration: {configName} really be deleted?'**
  String configurationsDeleteHint(Object configName);

  /// No description provided for @configurationsSave.
  ///
  /// In en, this message translates to:
  /// **'Save configuration'**
  String get configurationsSave;

  /// No description provided for @configurationsSaveHint.
  ///
  /// In en, this message translates to:
  /// **'Export the current configuration of your Device, or load one of your existing configurations.'**
  String get configurationsSaveHint;

  /// No description provided for @configurationsImport.
  ///
  /// In en, this message translates to:
  /// **'Import configuration'**
  String get configurationsImport;

  /// No description provided for @configurationsImportHint.
  ///
  /// In en, this message translates to:
  /// **'Should the configuration {configName} really be transferred?'**
  String configurationsImportHint(Object configName);

  /// No description provided for @generalTextConfigurations.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Configuration} other {Configurations}}'**
  String generalTextConfigurations(num count);

  /// No description provided for @configurationsStepPrepare.
  ///
  /// In en, this message translates to:
  /// **'Configuration is being prepared'**
  String get configurationsStepPrepare;

  /// No description provided for @configurationsStepName.
  ///
  /// In en, this message translates to:
  /// **'Enter a name for the configuration'**
  String get configurationsStepName;

  /// No description provided for @configurationsStepSaving.
  ///
  /// In en, this message translates to:
  /// **'Configuration is saving'**
  String get configurationsStepSaving;

  /// No description provided for @configurationsStepSavedsuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Configuration was saved successfully'**
  String get configurationsStepSavedsuccessfully;

  /// No description provided for @configurationsStepSavingfailed.
  ///
  /// In en, this message translates to:
  /// **'Saving the configuration failed'**
  String get configurationsStepSavingfailed;

  /// No description provided for @configurationsStepChoose.
  ///
  /// In en, this message translates to:
  /// **'Select a configuration'**
  String get configurationsStepChoose;

  /// No description provided for @configurationsStepImporting.
  ///
  /// In en, this message translates to:
  /// **'Configuration is importing'**
  String get configurationsStepImporting;

  /// No description provided for @configurationsStepImportedsuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Configuration was imported successfully'**
  String get configurationsStepImportedsuccessfully;

  /// No description provided for @configurationsStepImportingfailed.
  ///
  /// In en, this message translates to:
  /// **'Importing configuration failed'**
  String get configurationsStepImportingfailed;

  /// No description provided for @discoveryAssuDescription.
  ///
  /// In en, this message translates to:
  /// **'Outdoor Socket Time Switch Bluetooth'**
  String get discoveryAssuDescription;

  /// No description provided for @settingsDatetimeDevicetime.
  ///
  /// In en, this message translates to:
  /// **'Actual device time'**
  String get settingsDatetimeDevicetime;

  /// No description provided for @settingsDatetimeLoading.
  ///
  /// In en, this message translates to:
  /// **'Time settings are loaded'**
  String get settingsDatetimeLoading;

  /// No description provided for @discoveryEud12Description.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch Bluetooth'**
  String get discoveryEud12Description;

  /// No description provided for @generalTextOffdelay.
  ///
  /// In en, this message translates to:
  /// **'Off Delay'**
  String get generalTextOffdelay;

  /// No description provided for @generalTextRemainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'Remaining brightness'**
  String get generalTextRemainingbrightness;

  /// No description provided for @generalTextSwitchonvalue.
  ///
  /// In en, this message translates to:
  /// **'Switch on value'**
  String get generalTextSwitchonvalue;

  /// No description provided for @motionsensorTitleNoremainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'No residual brightness'**
  String get motionsensorTitleNoremainingbrightness;

  /// No description provided for @motionsensorTitleAlwaysremainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'With residual brightness'**
  String get motionsensorTitleAlwaysremainingbrightness;

  /// No description provided for @motionsensorTitleRemainingbrightnesswithprogram.
  ///
  /// In en, this message translates to:
  /// **'Residual brightness via switching program'**
  String get motionsensorTitleRemainingbrightnesswithprogram;

  /// No description provided for @motionsensorTitleRemainingbrightnesswithprogramandzea.
  ///
  /// In en, this message translates to:
  /// **'Residual brightness via ZE and ZA'**
  String get motionsensorTitleRemainingbrightnesswithprogramandzea;

  /// No description provided for @motionsensorTitleNoremainingbrightnessauto.
  ///
  /// In en, this message translates to:
  /// **'No residual brightness (semi-automatic)'**
  String get motionsensorTitleNoremainingbrightnessauto;

  /// No description provided for @generalTextMotionsensor.
  ///
  /// In en, this message translates to:
  /// **'Motion detector'**
  String get generalTextMotionsensor;

  /// No description provided for @generalTextLightclock.
  ///
  /// In en, this message translates to:
  /// **'Light alarm clock'**
  String get generalTextLightclock;

  /// No description provided for @generalTextSnoozeclock.
  ///
  /// In en, this message translates to:
  /// **'Snooze function'**
  String get generalTextSnoozeclock;

  /// No description provided for @generalDescriptionLightclock.
  ///
  /// In en, this message translates to:
  /// **'When switching on ({mode}), the light is switched on after approx. 1 second at the lowest brightness and slowly dimmed up without changing the last saved brightness level.'**
  String generalDescriptionLightclock(Object mode);

  /// No description provided for @generalDescriptionSnoozeclock.
  ///
  /// In en, this message translates to:
  /// **'When switching off ({mode}), the lighting is dimmed down from the current dimming position to the minimum brightness and switched off. The lighting can be switched off at any time during the dimming down process by pressing the button briefly. A long press during the dimming process dims up and ends the snooze function.'**
  String generalDescriptionSnoozeclock(Object mode);

  /// No description provided for @generalTextImmediately.
  ///
  /// In en, this message translates to:
  /// **'Immediately'**
  String get generalTextImmediately;

  /// No description provided for @generalTextPercentage.
  ///
  /// In en, this message translates to:
  /// **'Percentage'**
  String get generalTextPercentage;

  /// No description provided for @generalTextSwitchoffprewarning.
  ///
  /// In en, this message translates to:
  /// **'Switch-off prewarning'**
  String get generalTextSwitchoffprewarning;

  /// No description provided for @generalDescriptionSwitchoffprewarning.
  ///
  /// In en, this message translates to:
  /// **'Slow dimming to minimum brightness'**
  String get generalDescriptionSwitchoffprewarning;

  /// No description provided for @generalDescriptionOffdelay.
  ///
  /// In en, this message translates to:
  /// **'The device switches on when the control voltage is applied. If the control voltage is interrupted, the time lapse begins, after which the device switches off. The appliance can be switched on downstream during the time lapse.'**
  String get generalDescriptionOffdelay;

  /// No description provided for @generalDescriptionBrightness.
  ///
  /// In en, this message translates to:
  /// **'The brightness at which the lamp is switched on by the dimmer.'**
  String get generalDescriptionBrightness;

  /// No description provided for @generalDescriptionRemainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'The dimming value in per cent to which the lamp is dimmed after the motion detector is switched off.'**
  String get generalDescriptionRemainingbrightness;

  /// No description provided for @generalDescriptionRuntime.
  ///
  /// In en, this message translates to:
  /// **'Running time of the light alarm function from minimum brightness to maximum brightness.'**
  String get generalDescriptionRuntime;

  /// No description provided for @generalTextUniversalbutton.
  ///
  /// In en, this message translates to:
  /// **'Universal push-button'**
  String get generalTextUniversalbutton;

  /// No description provided for @generalTextDirectionalbutton.
  ///
  /// In en, this message translates to:
  /// **'Direction button'**
  String get generalTextDirectionalbutton;

  /// No description provided for @eud12DescriptionAuto.
  ///
  /// In en, this message translates to:
  /// **'Automatic detection UT/RT (with directional sensor diode RTD)'**
  String get eud12DescriptionAuto;

  /// No description provided for @eud12DescriptionRt.
  ///
  /// In en, this message translates to:
  /// **'with directional sensing diode RTD'**
  String get eud12DescriptionRt;

  /// No description provided for @generalTextProgram.
  ///
  /// In en, this message translates to:
  /// **'Program'**
  String get generalTextProgram;

  /// No description provided for @eud12MotionsensorOff.
  ///
  /// In en, this message translates to:
  /// **'With motion detector set to Off'**
  String get eud12MotionsensorOff;

  /// No description provided for @eud12ClockmodeTitleProgramze.
  ///
  /// In en, this message translates to:
  /// **'Program and Central On'**
  String get eud12ClockmodeTitleProgramze;

  /// No description provided for @eud12ClockmodeTitleProgramza.
  ///
  /// In en, this message translates to:
  /// **'Program and Central Off'**
  String get eud12ClockmodeTitleProgramza;

  /// No description provided for @eud12ClockmodeTitleProgrambuttonon.
  ///
  /// In en, this message translates to:
  /// **'Program and UT/RT On'**
  String get eud12ClockmodeTitleProgrambuttonon;

  /// No description provided for @eud12ClockmodeTitleProgrambuttonoff.
  ///
  /// In en, this message translates to:
  /// **'Program and UT/RT Off'**
  String get eud12ClockmodeTitleProgrambuttonoff;

  /// No description provided for @eud12TiImpulseTitle.
  ///
  /// In en, this message translates to:
  /// **'Pulse time On (t1)'**
  String get eud12TiImpulseTitle;

  /// No description provided for @eud12TiImpulseHeader.
  ///
  /// In en, this message translates to:
  /// **'Dimming value Pulse time On'**
  String get eud12TiImpulseHeader;

  /// No description provided for @eud12TiImpulseDescription.
  ///
  /// In en, this message translates to:
  /// **'The dimming value as a percentage\n to which the lamp is dimmed at pulse time ON.'**
  String get eud12TiImpulseDescription;

  /// No description provided for @eud12TiOffTitle.
  ///
  /// In en, this message translates to:
  /// **'Pulse time Off (t2)'**
  String get eud12TiOffTitle;

  /// No description provided for @eud12TiOffHeader.
  ///
  /// In en, this message translates to:
  /// **'Dimming value Pulse time Off'**
  String get eud12TiOffHeader;

  /// No description provided for @eud12TiOffDescription.
  ///
  /// In en, this message translates to:
  /// **'The dimming value is a percentage to which the lamp is dimmed at pulse time OFF.'**
  String get eud12TiOffDescription;

  /// No description provided for @generalTextButtonpermanentlight.
  ///
  /// In en, this message translates to:
  /// **'Permanent push-button light'**
  String get generalTextButtonpermanentlight;

  /// No description provided for @generalDescriptionButtonpermanentlight.
  ///
  /// In en, this message translates to:
  /// **'Setting the push-button continuous light from 0 to 10 hours in 0.5 hour increments. Activation by pressing the button for longer than 1 second (1x flickering), deactivation by pressing the button for longer than 2 seconds.'**
  String get generalDescriptionButtonpermanentlight;

  /// No description provided for @generalTextNobuttonpermanentlight.
  ///
  /// In en, this message translates to:
  /// **'No TSP'**
  String get generalTextNobuttonpermanentlight;

  /// No description provided for @generalTextBasicsettings.
  ///
  /// In en, this message translates to:
  /// **'Basic settings'**
  String get generalTextBasicsettings;

  /// No description provided for @generalTextInputswitch.
  ///
  /// In en, this message translates to:
  /// **'Local button input (A1)'**
  String get generalTextInputswitch;

  /// No description provided for @generalTextOperationmode.
  ///
  /// In en, this message translates to:
  /// **'Operating mode'**
  String get generalTextOperationmode;

  /// No description provided for @generalTextDimvalue.
  ///
  /// In en, this message translates to:
  /// **'Power on behaviour'**
  String get generalTextDimvalue;

  /// No description provided for @eud12TitleUsememory.
  ///
  /// In en, this message translates to:
  /// **'Use memory value'**
  String get eud12TitleUsememory;

  /// No description provided for @eud12DescriptionUsememory.
  ///
  /// In en, this message translates to:
  /// **'The memory value corresponds to the last dimming value set. If the memory value is deactivated, dimming is always set to the switch-on value.'**
  String get eud12DescriptionUsememory;

  /// No description provided for @generalTextStartup.
  ///
  /// In en, this message translates to:
  /// **'Switch-on brightness'**
  String get generalTextStartup;

  /// No description provided for @generalDescriptionSwitchonvalue.
  ///
  /// In en, this message translates to:
  /// **'The switch-on value is an adjustable brightness value that guarantees safe switch-on.'**
  String get generalDescriptionSwitchonvalue;

  /// No description provided for @generalTitleSwitchontime.
  ///
  /// In en, this message translates to:
  /// **'Switch-on time'**
  String get generalTitleSwitchontime;

  /// No description provided for @generalDescriptionSwitchontime.
  ///
  /// In en, this message translates to:
  /// **'After the switch-on time has elapsed, the lamp is dimmed from the switch-on value to the memory value.'**
  String get generalDescriptionSwitchontime;

  /// No description provided for @generalDescriptionStartup.
  ///
  /// In en, this message translates to:
  /// **'Some LED lamps require a higher inrush current to switch on reliably. The lamp is switched on at this switch-on value and then dimmed to the memory value after the switch-on time.'**
  String get generalDescriptionStartup;

  /// No description provided for @eud12ClockmodeSubtitleProgramze.
  ///
  /// In en, this message translates to:
  /// **'Short click on Central On'**
  String get eud12ClockmodeSubtitleProgramze;

  /// No description provided for @eud12ClockmodeSubtitleProgramza.
  ///
  /// In en, this message translates to:
  /// **'Short click on central off'**
  String get eud12ClockmodeSubtitleProgramza;

  /// No description provided for @eud12ClockmodeSubtitleProgrambuttonon.
  ///
  /// In en, this message translates to:
  /// **'Double-click on universal button/direction button On'**
  String get eud12ClockmodeSubtitleProgrambuttonon;

  /// No description provided for @eud12ClockmodeSubtitleProgrambuttonoff.
  ///
  /// In en, this message translates to:
  /// **'Double-click on universal button/direction button Off'**
  String get eud12ClockmodeSubtitleProgrambuttonoff;

  /// No description provided for @eud12FunctionStairlighttimeswitchTitleShort.
  ///
  /// In en, this message translates to:
  /// **'TLZ | Staircase lighting timer'**
  String get eud12FunctionStairlighttimeswitchTitleShort;

  /// No description provided for @eud12FunctionMinTitleShort.
  ///
  /// In en, this message translates to:
  /// **'MIN'**
  String get eud12FunctionMinTitleShort;

  /// No description provided for @eud12FunctionMmxTitleShort.
  ///
  /// In en, this message translates to:
  /// **'MMX'**
  String get eud12FunctionMmxTitleShort;

  /// No description provided for @eud12FunctionTiDescription.
  ///
  /// In en, this message translates to:
  /// **'Timer with adjustable switch-on and switch-off time from 0.5 seconds to 9.9 minutes. The brightness can be set from minimum brightness to maximum brightness.'**
  String get eud12FunctionTiDescription;

  /// No description provided for @eud12FunctionAutoDescription.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch with setting for motion detector, light alarm and snooze function'**
  String get eud12FunctionAutoDescription;

  /// No description provided for @eud12FunctionErDescription.
  ///
  /// In en, this message translates to:
  /// **'Switching relay, the brightness can be set from minimum brightness to maximum brightness.'**
  String get eud12FunctionErDescription;

  /// No description provided for @eud12FunctionEsvDescription.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch with setting of a switch-off delay from 1 to 120 minutes. Switch-off pre-warning at the end by dimming down selectable and adjustable from 1 to 3 minutes. Both central inputs active.'**
  String get eud12FunctionEsvDescription;

  /// No description provided for @eud12FunctionTlzDescription.
  ///
  /// In en, this message translates to:
  /// **'Setting the button light duration from 0 to 10 hours in 0.5 hour increments. Activation by pressing the button for longer than 1 second (1x flickering), deactivation by pressing the button for longer than 2 seconds.'**
  String get eud12FunctionTlzDescription;

  /// No description provided for @eud12FunctionMinDescription.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch, switches to the set minimum brightness when the control voltage is applied. The light is dimmed to maximum brightness within the set dimming time of 1 to 120 minutes. When the control voltage is removed, the light is switched off immediately, even during the dimming time. Both central inputs active.'**
  String get eud12FunctionMinDescription;

  /// No description provided for @eud12FunctionMmxDescription.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch, switches to the set minimum brightness when the control voltage is applied. During the set dimming time of 1 to 120 minutes, the light is dimmed to the maximum brightness. However, when the control voltage is removed, the dimmer dims down to the set minimum brightness. It is then switched off. Both central inputs active.'**
  String get eud12FunctionMmxDescription;

  /// No description provided for @motionsensorSubtitleNoremainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'With motion detector set to Off'**
  String get motionsensorSubtitleNoremainingbrightness;

  /// No description provided for @motionsensorSubtitleAlwaysremainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'With motion detector set to Off'**
  String get motionsensorSubtitleAlwaysremainingbrightness;

  /// No description provided for @motionsensorSubtitleRemainingbrightnesswithprogram.
  ///
  /// In en, this message translates to:
  /// **'Switching program activated and deactivated with BWM off'**
  String get motionsensorSubtitleRemainingbrightnesswithprogram;

  /// No description provided for @motionsensorSubtitleRemainingbrightnesswithprogramandzea.
  ///
  /// In en, this message translates to:
  /// **'Central On activates motion sensor, Central Off deactivates motion sensor, as well as by switching program'**
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea;

  /// No description provided for @motionsensorSubtitleNoremainingbrightnessauto.
  ///
  /// In en, this message translates to:
  /// **'Motion detector only switches off'**
  String get motionsensorSubtitleNoremainingbrightnessauto;

  /// No description provided for @detailsDimsectionHeader.
  ///
  /// In en, this message translates to:
  /// **'Dimming'**
  String get detailsDimsectionHeader;

  /// No description provided for @generalTextFast.
  ///
  /// In en, this message translates to:
  /// **'Fast'**
  String get generalTextFast;

  /// No description provided for @generalTextSlow.
  ///
  /// In en, this message translates to:
  /// **'Slowly'**
  String get generalTextSlow;

  /// No description provided for @eud12TextDimspeed.
  ///
  /// In en, this message translates to:
  /// **'Dimming speed'**
  String get eud12TextDimspeed;

  /// No description provided for @eud12TextSwitchonspeed.
  ///
  /// In en, this message translates to:
  /// **'Switch-on speed'**
  String get eud12TextSwitchonspeed;

  /// No description provided for @eud12TextSwitchoffspeed.
  ///
  /// In en, this message translates to:
  /// **'Switch-off speed'**
  String get eud12TextSwitchoffspeed;

  /// No description provided for @eud12DescriptionDimspeed.
  ///
  /// In en, this message translates to:
  /// **'The dimming speed is the speed at which the dimmer dims from the current brightness to the target brightness.'**
  String get eud12DescriptionDimspeed;

  /// No description provided for @eud12DescriptionSwitchonspeed.
  ///
  /// In en, this message translates to:
  /// **'The switch-on speed is the speed that the dimmer requires to switch on completely.'**
  String get eud12DescriptionSwitchonspeed;

  /// No description provided for @eud12DescriptionSwitchoffspeed.
  ///
  /// In en, this message translates to:
  /// **'The switch-off speed is the speed that the dimmer requires to switch off completely.'**
  String get eud12DescriptionSwitchoffspeed;

  /// No description provided for @settingsFactoryresetResetdimHeader.
  ///
  /// In en, this message translates to:
  /// **'Reset dimming settings'**
  String get settingsFactoryresetResetdimHeader;

  /// No description provided for @settingsFactoryresetResetdimDescription.
  ///
  /// In en, this message translates to:
  /// **'Should all dimming settings really be reset?'**
  String get settingsFactoryresetResetdimDescription;

  /// No description provided for @settingsFactoryresetResetdimConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'Dimming settings have been successfully reset'**
  String get settingsFactoryresetResetdimConfirmationDescription;

  /// No description provided for @eud12TextSwitchonoffspeed.
  ///
  /// In en, this message translates to:
  /// **'On/off speed'**
  String get eud12TextSwitchonoffspeed;

  /// No description provided for @eud12DescriptionSwitchonoffspeed.
  ///
  /// In en, this message translates to:
  /// **'The switch-on/switch-off speed is the speed that the dimmer requires to switch on or off completely.'**
  String get eud12DescriptionSwitchonoffspeed;

  /// No description provided for @timerDetailsDimtoval.
  ///
  /// In en, this message translates to:
  /// **'On with dimming value in %'**
  String get timerDetailsDimtoval;

  /// No description provided for @timerDetailsDimtovalDescription.
  ///
  /// In en, this message translates to:
  /// **'The dimmer always switches on with the fixed dimming value in %.'**
  String get timerDetailsDimtovalDescription;

  /// No description provided for @timerDetailsDimtovalSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Switch on with {brightness}%'**
  String timerDetailsDimtovalSubtitle(Object brightness);

  /// No description provided for @timerDetailsDimtomem.
  ///
  /// In en, this message translates to:
  /// **'On with memory value'**
  String get timerDetailsDimtomem;

  /// No description provided for @timerDetailsDimtomemSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Switching on with memory value'**
  String get timerDetailsDimtomemSubtitle;

  /// No description provided for @timerDetailsMotionsensorwithremainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'Residual brightness (BWM) On'**
  String get timerDetailsMotionsensorwithremainingbrightness;

  /// No description provided for @timerDetailsMotionsensornoremainingbrightness.
  ///
  /// In en, this message translates to:
  /// **'Residual brightness (BWM) Off'**
  String get timerDetailsMotionsensornoremainingbrightness;

  /// No description provided for @settingsRandommodeHint.
  ///
  /// In en, this message translates to:
  /// **'With random mode activated all programs of this channel are randomly offset by up to 15 minutes. On-timers are offset in advance, Off-timers are delayed.'**
  String get settingsRandommodeHint;

  /// No description provided for @runtimeOffsetDescription.
  ///
  /// In en, this message translates to:
  /// **'Additional overrun, after the journey time has elapsed'**
  String get runtimeOffsetDescription;

  /// No description provided for @loadingTextDimvalue.
  ///
  /// In en, this message translates to:
  /// **'Dimming value is loaded'**
  String get loadingTextDimvalue;

  /// No description provided for @discoveryEudipmDescription.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer switch IP Matter'**
  String get discoveryEudipmDescription;

  /// No description provided for @generalTextOffset.
  ///
  /// In en, this message translates to:
  /// **'Overrun'**
  String get generalTextOffset;

  /// No description provided for @eud12DimvalueTestText.
  ///
  /// In en, this message translates to:
  /// **'Send brightness'**
  String get eud12DimvalueTestText;

  /// No description provided for @eud12DimvalueTestDescription.
  ///
  /// In en, this message translates to:
  /// **'The currently set dim speed is taken into account during testing.'**
  String get eud12DimvalueTestDescription;

  /// No description provided for @eud12DimvalueLoadText.
  ///
  /// In en, this message translates to:
  /// **'Load brightness'**
  String get eud12DimvalueLoadText;

  /// No description provided for @settingsDatetimeNotime.
  ///
  /// In en, this message translates to:
  /// **'The date and time settings must be read out via the device display.'**
  String get settingsDatetimeNotime;

  /// No description provided for @generalMatterText.
  ///
  /// In en, this message translates to:
  /// **'Matter'**
  String get generalMatterText;

  /// No description provided for @generalMatterMessage.
  ///
  /// In en, this message translates to:
  /// **'Please teach in your Matter device using one of the following apps'**
  String get generalMatterMessage;

  /// No description provided for @generalMatterOpengooglehome.
  ///
  /// In en, this message translates to:
  /// **'Open Google Home'**
  String get generalMatterOpengooglehome;

  /// No description provided for @generalMatterOpenamazonalexa.
  ///
  /// In en, this message translates to:
  /// **'Open Amazon Alexa'**
  String get generalMatterOpenamazonalexa;

  /// No description provided for @generalMatterOpensmartthings.
  ///
  /// In en, this message translates to:
  /// **'Open SmartThings'**
  String get generalMatterOpensmartthings;

  /// No description provided for @generalLabelProgram.
  ///
  /// In en, this message translates to:
  /// **'Program {number}'**
  String generalLabelProgram(Object number);

  /// No description provided for @generalTextDone.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get generalTextDone;

  /// No description provided for @settingsRandommodeDescriptionShort.
  ///
  /// In en, this message translates to:
  /// **'With random mode activated all programs of this channel are randomly offset by up to 15 minutes. On-timers are offset in advance, Off-timers are delayed.'**
  String get settingsRandommodeDescriptionShort;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @discoveryBluetooth.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth'**
  String get discoveryBluetooth;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @timeProgramAdd.
  ///
  /// In en, this message translates to:
  /// **'Add time program'**
  String get timeProgramAdd;

  /// No description provided for @noConnection.
  ///
  /// In en, this message translates to:
  /// **'No connection'**
  String get noConnection;

  /// No description provided for @timeProgramOnlyActive.
  ///
  /// In en, this message translates to:
  /// **'Configured programs'**
  String get timeProgramOnlyActive;

  /// No description provided for @timeProgramAll.
  ///
  /// In en, this message translates to:
  /// **'All programs'**
  String get timeProgramAll;

  /// No description provided for @active.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get active;

  /// No description provided for @inactive.
  ///
  /// In en, this message translates to:
  /// **'Inactive'**
  String get inactive;

  /// No description provided for @timeProgramSaved.
  ///
  /// In en, this message translates to:
  /// **'Time program {number} saved'**
  String timeProgramSaved(Object number);

  /// No description provided for @deviceLanguageSaved.
  ///
  /// In en, this message translates to:
  /// **'Device language saved'**
  String get deviceLanguageSaved;

  /// No description provided for @generalTextTimeShort.
  ///
  /// In en, this message translates to:
  /// **'{time}'**
  String generalTextTimeShort(Object time);

  /// No description provided for @programDeleteHint.
  ///
  /// In en, this message translates to:
  /// **'Should program {index} really be deleted?'**
  String programDeleteHint(Object index);

  /// No description provided for @milliseconds.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Millisecond} other {Milliseconds}}'**
  String milliseconds(num count);

  /// No description provided for @millisecondsWithValue.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {{count} Millisecond} other {{count} Milliseconds}}'**
  String millisecondsWithValue(num count);

  /// No description provided for @secondsWithValue.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {{count} Second} other {{count} Seconds}}'**
  String secondsWithValue(num count);

  /// No description provided for @minutesWithValue.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {{count} Minute} other {{count} Minutes}}'**
  String minutesWithValue(num count);

  /// No description provided for @hoursWithValue.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {{count} Hour} other {{count} Hours}}'**
  String hoursWithValue(num count);

  /// No description provided for @settingsPinFailEmpty.
  ///
  /// In en, this message translates to:
  /// **'The PIN must not be empty'**
  String get settingsPinFailEmpty;

  /// No description provided for @detailsConfigurationWifiloginScanNoMatch.
  ///
  /// In en, this message translates to:
  /// **'Scanned code does not match the device'**
  String get detailsConfigurationWifiloginScanNoMatch;

  /// No description provided for @wifiAuthorizationPopIsEmpty.
  ///
  /// In en, this message translates to:
  /// **'PoP cannot be empty'**
  String get wifiAuthorizationPopIsEmpty;

  /// No description provided for @wifiAuthenticationCredentialsHint.
  ///
  /// In en, this message translates to:
  /// **'As the app cannot access your private Wi-Fi password, it is not possible to check the correctness of the entry. If no connection is established, check the password and enter it again.'**
  String get wifiAuthenticationCredentialsHint;

  /// No description provided for @generalMatterOpenApplehome.
  ///
  /// In en, this message translates to:
  /// **'Open Apple Home'**
  String get generalMatterOpenApplehome;

  /// No description provided for @timeProgramNoActive.
  ///
  /// In en, this message translates to:
  /// **'No configured programs'**
  String get timeProgramNoActive;

  /// No description provided for @timeProgramNoEmpty.
  ///
  /// In en, this message translates to:
  /// **'No free time program available'**
  String get timeProgramNoEmpty;

  /// No description provided for @nameOfConfiguration.
  ///
  /// In en, this message translates to:
  /// **'Configuration name'**
  String get nameOfConfiguration;

  /// No description provided for @currentDevice.
  ///
  /// In en, this message translates to:
  /// **'Current device'**
  String get currentDevice;

  /// No description provided for @export.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// No description provided for @import.
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get import;

  /// No description provided for @savedConfigurations.
  ///
  /// In en, this message translates to:
  /// **'Saved configurations'**
  String get savedConfigurations;

  /// No description provided for @importableServicesLabel.
  ///
  /// In en, this message translates to:
  /// **'The following settings can be imported:'**
  String get importableServicesLabel;

  /// No description provided for @notImportableServicesLabel.
  ///
  /// In en, this message translates to:
  /// **'Incompatible settings'**
  String get notImportableServicesLabel;

  /// No description provided for @deviceCategoryMeterGateway.
  ///
  /// In en, this message translates to:
  /// **'Meter Gateway'**
  String get deviceCategoryMeterGateway;

  /// No description provided for @deviceCategory2ChannelTimeSwitch.
  ///
  /// In en, this message translates to:
  /// **'2-channel time switch'**
  String get deviceCategory2ChannelTimeSwitch;

  /// No description provided for @devicategoryOutdoorTimeSwitchBluetooth.
  ///
  /// In en, this message translates to:
  /// **'Outdoor time switch Bluetooth'**
  String get devicategoryOutdoorTimeSwitchBluetooth;

  /// No description provided for @settingsModbusHeader.
  ///
  /// In en, this message translates to:
  /// **'Modbus'**
  String get settingsModbusHeader;

  /// No description provided for @settingsModbusDescription.
  ///
  /// In en, this message translates to:
  /// **'Adjust the baud rate, parity, and timeout to configure the transmission speed, error detection and waiting time.'**
  String get settingsModbusDescription;

  /// No description provided for @settingsModbusRTU.
  ///
  /// In en, this message translates to:
  /// **'Modbus RTU'**
  String get settingsModbusRTU;

  /// No description provided for @settingsModbusBaudrate.
  ///
  /// In en, this message translates to:
  /// **'Baudrate'**
  String get settingsModbusBaudrate;

  /// No description provided for @settingsModbusParity.
  ///
  /// In en, this message translates to:
  /// **'Parity'**
  String get settingsModbusParity;

  /// No description provided for @settingsModbusTimeout.
  ///
  /// In en, this message translates to:
  /// **'Modbus Timeout'**
  String get settingsModbusTimeout;

  /// No description provided for @locationServiceDisabled.
  ///
  /// In en, this message translates to:
  /// **'Location is disabled'**
  String get locationServiceDisabled;

  /// No description provided for @locationPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Please allow the location permission to request your current position.'**
  String get locationPermissionDenied;

  /// No description provided for @locationPermissionDeniedPermanently.
  ///
  /// In en, this message translates to:
  /// **'Location permissions are permanently denied, please allow the location permission in your device settings to request your current position.'**
  String get locationPermissionDeniedPermanently;

  /// No description provided for @lastSync.
  ///
  /// In en, this message translates to:
  /// **'Last sync'**
  String get lastSync;

  /// No description provided for @dhcpActive.
  ///
  /// In en, this message translates to:
  /// **'DHCP active'**
  String get dhcpActive;

  /// No description provided for @ipAddress.
  ///
  /// In en, this message translates to:
  /// **'IP'**
  String get ipAddress;

  /// No description provided for @subnetMask.
  ///
  /// In en, this message translates to:
  /// **'Subnet mask'**
  String get subnetMask;

  /// No description provided for @standardGateway.
  ///
  /// In en, this message translates to:
  /// **'Default gateway'**
  String get standardGateway;

  /// No description provided for @dns.
  ///
  /// In en, this message translates to:
  /// **'DNS'**
  String get dns;

  /// No description provided for @alternateDNS.
  ///
  /// In en, this message translates to:
  /// **'Alternate DNS'**
  String get alternateDNS;

  /// No description provided for @errorNoNetworksFound.
  ///
  /// In en, this message translates to:
  /// **'No wifi network found'**
  String get errorNoNetworksFound;

  /// No description provided for @availableNetworks.
  ///
  /// In en, this message translates to:
  /// **'Available networks'**
  String get availableNetworks;

  /// No description provided for @enableWifiInterface.
  ///
  /// In en, this message translates to:
  /// **'Enable WiFi interface'**
  String get enableWifiInterface;

  /// No description provided for @enableLANInterface.
  ///
  /// In en, this message translates to:
  /// **'Enable LAN interface'**
  String get enableLANInterface;

  /// No description provided for @hintDontDisableAllInterfaces.
  ///
  /// In en, this message translates to:
  /// **'Make sure that not all interfaces are disabled. The last activated interface has priority.'**
  String get hintDontDisableAllInterfaces;

  /// No description provided for @ssid.
  ///
  /// In en, this message translates to:
  /// **'SSID'**
  String get ssid;

  /// No description provided for @searchNetworks.
  ///
  /// In en, this message translates to:
  /// **'Search networks'**
  String get searchNetworks;

  /// No description provided for @errorNoNetworkEnabled.
  ///
  /// In en, this message translates to:
  /// **'At least one station must be active'**
  String get errorNoNetworkEnabled;

  /// No description provided for @errorActiveNetworkInvalid.
  ///
  /// In en, this message translates to:
  /// **'Not all active stations are valid'**
  String get errorActiveNetworkInvalid;

  /// No description provided for @invalidNetworkConfiguration.
  ///
  /// In en, this message translates to:
  /// **'Invalid network configuration'**
  String get invalidNetworkConfiguration;

  /// No description provided for @generalDefault.
  ///
  /// In en, this message translates to:
  /// **'Default'**
  String get generalDefault;

  /// No description provided for @mqttHeader.
  ///
  /// In en, this message translates to:
  /// **'MQTT'**
  String get mqttHeader;

  /// No description provided for @mqttConnected.
  ///
  /// In en, this message translates to:
  /// **'Connected to MQTT broker'**
  String get mqttConnected;

  /// No description provided for @mqttDisconnected.
  ///
  /// In en, this message translates to:
  /// **'No connection to MQTT broker'**
  String get mqttDisconnected;

  /// No description provided for @mqttBrokerURI.
  ///
  /// In en, this message translates to:
  /// **'Broker URI'**
  String get mqttBrokerURI;

  /// No description provided for @mqttBrokerURIHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT broker URI'**
  String get mqttBrokerURIHint;

  /// No description provided for @mqttPort.
  ///
  /// In en, this message translates to:
  /// **'Port'**
  String get mqttPort;

  /// No description provided for @mqttPortHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT port'**
  String get mqttPortHint;

  /// No description provided for @mqttClientId.
  ///
  /// In en, this message translates to:
  /// **'Client-ID'**
  String get mqttClientId;

  /// No description provided for @mqttClientIdHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT Client-ID'**
  String get mqttClientIdHint;

  /// No description provided for @mqttUsername.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get mqttUsername;

  /// No description provided for @mqttUsernameHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT username'**
  String get mqttUsernameHint;

  /// No description provided for @mqttPassword.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get mqttPassword;

  /// No description provided for @mqttPasswordHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT password'**
  String get mqttPasswordHint;

  /// No description provided for @mqttCertificate.
  ///
  /// In en, this message translates to:
  /// **'Certificate'**
  String get mqttCertificate;

  /// No description provided for @mqttCertificateHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT certificate'**
  String get mqttCertificateHint;

  /// No description provided for @mqttTopic.
  ///
  /// In en, this message translates to:
  /// **'Topic'**
  String get mqttTopic;

  /// No description provided for @mqttTopicHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT topic'**
  String get mqttTopicHint;

  /// No description provided for @electricityMeter.
  ///
  /// In en, this message translates to:
  /// **'Electricity meter'**
  String get electricityMeter;

  /// No description provided for @electricityMeterCurrent.
  ///
  /// In en, this message translates to:
  /// **'Current'**
  String get electricityMeterCurrent;

  /// No description provided for @electricityMeterHistory.
  ///
  /// In en, this message translates to:
  /// **'History'**
  String get electricityMeterHistory;

  /// No description provided for @electricityMeterReading.
  ///
  /// In en, this message translates to:
  /// **'Meter reading'**
  String get electricityMeterReading;

  /// No description provided for @connectivity.
  ///
  /// In en, this message translates to:
  /// **'Connectivity'**
  String get connectivity;

  /// No description provided for @electricMeter.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Electric meter} other {Electric meters}}'**
  String electricMeter(num count);

  /// No description provided for @discoveryZGW16Description.
  ///
  /// In en, this message translates to:
  /// **'Modbus-Energy-Meters-MQTT-Gateway'**
  String get discoveryZGW16Description;

  /// No description provided for @bluetoothConnectionLost.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth connection lost'**
  String get bluetoothConnectionLost;

  /// No description provided for @bluetoothConnectionLostDescription.
  ///
  /// In en, this message translates to:
  /// **'The Bluetooth connection to the device has been lost. Please check the connection to the device.'**
  String get bluetoothConnectionLostDescription;

  /// No description provided for @openBluetoothSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Bluetooth settings'**
  String get openBluetoothSettings;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @setInitialPassword.
  ///
  /// In en, this message translates to:
  /// **'Set initial password'**
  String get setInitialPassword;

  /// No description provided for @initialPasswordMinimumLength.
  ///
  /// In en, this message translates to:
  /// **'The password must be at least {length} characters long'**
  String initialPasswordMinimumLength(Object length);

  /// No description provided for @repeatPassword.
  ///
  /// In en, this message translates to:
  /// **'Repeat password'**
  String get repeatPassword;

  /// No description provided for @passwordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// No description provided for @savePassword.
  ///
  /// In en, this message translates to:
  /// **'Save password'**
  String get savePassword;

  /// No description provided for @savePasswordHint.
  ///
  /// In en, this message translates to:
  /// **'The password is saved for future connections on your device.'**
  String get savePasswordHint;

  /// No description provided for @retrieveNtpServer.
  ///
  /// In en, this message translates to:
  /// **'Retrieve time from NTP server'**
  String get retrieveNtpServer;

  /// No description provided for @retrieveNtpServerFailed.
  ///
  /// In en, this message translates to:
  /// **'The connection to the NTP server could not be established.'**
  String get retrieveNtpServerFailed;

  /// No description provided for @retrieveNtpServerSuccess.
  ///
  /// In en, this message translates to:
  /// **'The connection to the NTP server was successful.'**
  String get retrieveNtpServerSuccess;

  /// No description provided for @settingsPasswordNewPasswordDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter new password'**
  String get settingsPasswordNewPasswordDescription;

  /// No description provided for @settingsPasswordConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'Password change successful'**
  String get settingsPasswordConfirmationDescription;

  /// No description provided for @dhcpRangeStart.
  ///
  /// In en, this message translates to:
  /// **'DHCP range start'**
  String get dhcpRangeStart;

  /// No description provided for @dhcpRangeEnd.
  ///
  /// In en, this message translates to:
  /// **'DHCP range end'**
  String get dhcpRangeEnd;

  /// No description provided for @forwardOnMQTT.
  ///
  /// In en, this message translates to:
  /// **'Forward to MQTT'**
  String get forwardOnMQTT;

  /// No description provided for @showAll.
  ///
  /// In en, this message translates to:
  /// **'Show all'**
  String get showAll;

  /// No description provided for @hide.
  ///
  /// In en, this message translates to:
  /// **'Hide'**
  String get hide;

  /// No description provided for @changeToAPMode.
  ///
  /// In en, this message translates to:
  /// **'Change to AP mode'**
  String get changeToAPMode;

  /// No description provided for @changeToAPModeDescription.
  ///
  /// In en, this message translates to:
  /// **'You are about to connect your device to a WiFi network, in which case the connection to the device is disconnected and you must reconnect to your device via the configured network.'**
  String get changeToAPModeDescription;

  /// No description provided for @consumption.
  ///
  /// In en, this message translates to:
  /// **'Consumption'**
  String get consumption;

  /// No description provided for @currentDay.
  ///
  /// In en, this message translates to:
  /// **'Current day'**
  String get currentDay;

  /// No description provided for @twoWeeks.
  ///
  /// In en, this message translates to:
  /// **'2 Weeks'**
  String get twoWeeks;

  /// No description provided for @oneYear.
  ///
  /// In en, this message translates to:
  /// **'1 Year'**
  String get oneYear;

  /// No description provided for @threeYears.
  ///
  /// In en, this message translates to:
  /// **'3 Years'**
  String get threeYears;

  /// No description provided for @passwordMinLength.
  ///
  /// In en, this message translates to:
  /// **'Password needs at least {length} characters.'**
  String passwordMinLength(Object length);

  /// No description provided for @passwordNeedsLetter.
  ///
  /// In en, this message translates to:
  /// **'Password must contain a letter.'**
  String get passwordNeedsLetter;

  /// No description provided for @passwordNeedsNumber.
  ///
  /// In en, this message translates to:
  /// **'Password must contain a number.'**
  String get passwordNeedsNumber;

  /// No description provided for @portEmpty.
  ///
  /// In en, this message translates to:
  /// **'Port cannot be empty'**
  String get portEmpty;

  /// No description provided for @portInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid port'**
  String get portInvalid;

  /// No description provided for @portOutOfRange.
  ///
  /// In en, this message translates to:
  /// **'Port must be between {rangeStart} and {rangeEnd}'**
  String portOutOfRange(Object rangeEnd, Object rangeStart);

  /// No description provided for @ipAddressEmpty.
  ///
  /// In en, this message translates to:
  /// **'IP address cannot be empty'**
  String get ipAddressEmpty;

  /// No description provided for @ipAddressInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid IP address'**
  String get ipAddressInvalid;

  /// No description provided for @subnetMaskEmpty.
  ///
  /// In en, this message translates to:
  /// **'Subnet mask cannot be empty'**
  String get subnetMaskEmpty;

  /// No description provided for @subnetMaskInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid subnet mask'**
  String get subnetMaskInvalid;

  /// No description provided for @gatewayEmpty.
  ///
  /// In en, this message translates to:
  /// **'Gateway cannot be empty'**
  String get gatewayEmpty;

  /// No description provided for @gatewayInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid gateway'**
  String get gatewayInvalid;

  /// No description provided for @dnsEmpty.
  ///
  /// In en, this message translates to:
  /// **'DNS cannot be empty'**
  String get dnsEmpty;

  /// No description provided for @dnsInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid DNS'**
  String get dnsInvalid;

  /// No description provided for @uriEmpty.
  ///
  /// In en, this message translates to:
  /// **'URI cannot be empty'**
  String get uriEmpty;

  /// No description provided for @uriInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid URI'**
  String get uriInvalid;

  /// No description provided for @electricityMeterChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Electricity meter successfully changed'**
  String get electricityMeterChangedSuccessfully;

  /// No description provided for @networkChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Network configuration successfully changed'**
  String get networkChangedSuccessfully;

  /// No description provided for @mqttChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'MQTT configuration successfully changed'**
  String get mqttChangedSuccessfully;

  /// No description provided for @modbusChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Modbus settings successfully changed'**
  String get modbusChangedSuccessfully;

  /// No description provided for @loginData.
  ///
  /// In en, this message translates to:
  /// **'Delete login data'**
  String get loginData;

  /// No description provided for @valueConfigured.
  ///
  /// In en, this message translates to:
  /// **'Configured'**
  String get valueConfigured;

  /// No description provided for @electricityMeterHistoryNoData.
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get electricityMeterHistoryNoData;

  /// No description provided for @locationChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Location successfully changed'**
  String get locationChangedSuccessfully;

  /// No description provided for @settingsNameFailEmpty.
  ///
  /// In en, this message translates to:
  /// **'Name cannot be empty'**
  String get settingsNameFailEmpty;

  /// No description provided for @settingsNameFailLength.
  ///
  /// In en, this message translates to:
  /// **'Name must not be longer than {length} characters'**
  String settingsNameFailLength(Object length);

  /// No description provided for @solsticeChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Summer/Wintertime settings successfully changed'**
  String get solsticeChangedSuccesfully;

  /// No description provided for @relayFunctionChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Relay-Function successfully changed'**
  String get relayFunctionChangedSuccesfully;

  /// No description provided for @relayFunctionHeader.
  ///
  /// In en, this message translates to:
  /// **'Relay function'**
  String get relayFunctionHeader;

  /// No description provided for @dimmerValueChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Power on behaviour successfully changed'**
  String get dimmerValueChangedSuccesfully;

  /// No description provided for @dimmerBehaviourChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Dimming behaviour successfully changed'**
  String get dimmerBehaviourChangedSuccesfully;

  /// No description provided for @dimmerBrightnessDescription.
  ///
  /// In en, this message translates to:
  /// **'The minimum and maximum brightness affects all adjustable brightnesses of the dimmer.'**
  String get dimmerBrightnessDescription;

  /// No description provided for @dimmerSettingsChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Basic settings successfully changed'**
  String get dimmerSettingsChangedSuccesfully;

  /// No description provided for @liveUpdateEnabled.
  ///
  /// In en, this message translates to:
  /// **'Live test enabled'**
  String get liveUpdateEnabled;

  /// No description provided for @liveUpdateDisabled.
  ///
  /// In en, this message translates to:
  /// **'Live test disabled'**
  String get liveUpdateDisabled;

  /// No description provided for @liveUpdateDescription.
  ///
  /// In en, this message translates to:
  /// **'The last changed slider value will be send to the device'**
  String get liveUpdateDescription;

  /// No description provided for @demoDevices.
  ///
  /// In en, this message translates to:
  /// **'Demo devices'**
  String get demoDevices;

  /// No description provided for @showDemoDevices.
  ///
  /// In en, this message translates to:
  /// **'Show demo devices'**
  String get showDemoDevices;

  /// No description provided for @deviceCategoryTimeSwitch.
  ///
  /// In en, this message translates to:
  /// **'Time switch'**
  String get deviceCategoryTimeSwitch;

  /// No description provided for @deviceCategoryMultifunctionalRelay.
  ///
  /// In en, this message translates to:
  /// **'Multifunctional relay'**
  String get deviceCategoryMultifunctionalRelay;

  /// No description provided for @deviceCategoryDimmer.
  ///
  /// In en, this message translates to:
  /// **'Dimmer'**
  String get deviceCategoryDimmer;

  /// No description provided for @deviceCategoryShutter.
  ///
  /// In en, this message translates to:
  /// **'Roller Shutters and Blinds'**
  String get deviceCategoryShutter;

  /// No description provided for @deviceCategoryRelay.
  ///
  /// In en, this message translates to:
  /// **'Relay'**
  String get deviceCategoryRelay;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @configurationsHeader.
  ///
  /// In en, this message translates to:
  /// **'Configurations'**
  String get configurationsHeader;

  /// No description provided for @configurationsDescription.
  ///
  /// In en, this message translates to:
  /// **'Manage your configurations here.'**
  String get configurationsDescription;

  /// No description provided for @configurationsNameFailEmpty.
  ///
  /// In en, this message translates to:
  /// **'Configuration name cannot be empty'**
  String get configurationsNameFailEmpty;

  /// No description provided for @configurationDeleted.
  ///
  /// In en, this message translates to:
  /// **'Configuration deleted'**
  String get configurationDeleted;

  /// No description provided for @codeFound.
  ///
  /// In en, this message translates to:
  /// **'{codeType} code found'**
  String codeFound(Object codeType);

  /// No description provided for @errorCameraPermission.
  ///
  /// In en, this message translates to:
  /// **'Please allow the camera permission to scan the ELTAKO code.'**
  String get errorCameraPermission;

  /// No description provided for @authorizationSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Successfully authorized on device'**
  String get authorizationSuccessful;

  /// No description provided for @wifiAuthenticationResetConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'The device is now ready for a new authorization.'**
  String get wifiAuthenticationResetConfirmationDescription;

  /// No description provided for @settingsResetConnectionHeader.
  ///
  /// In en, this message translates to:
  /// **'Reset connection'**
  String get settingsResetConnectionHeader;

  /// No description provided for @settingsResetConnectionDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to reset the connection?'**
  String get settingsResetConnectionDescription;

  /// No description provided for @settingsResetConnectionConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'Connection has been successfully reset.'**
  String get settingsResetConnectionConfirmationDescription;

  /// No description provided for @wiredInputChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Switch behaviour successfully changed'**
  String get wiredInputChangedSuccesfully;

  /// No description provided for @runtimeChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Runtime behaviour successfully changed'**
  String get runtimeChangedSuccesfully;

  /// No description provided for @expertModeActivated.
  ///
  /// In en, this message translates to:
  /// **'Expert mode activated'**
  String get expertModeActivated;

  /// No description provided for @expertModeDeactivated.
  ///
  /// In en, this message translates to:
  /// **'Expert mode deactivated'**
  String get expertModeDeactivated;

  /// No description provided for @license.
  ///
  /// In en, this message translates to:
  /// **'License'**
  String get license;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @provisioningConnectingHint.
  ///
  /// In en, this message translates to:
  /// **'Device connection is being established. This can take up to 1 minute.'**
  String get provisioningConnectingHint;

  /// No description provided for @serialnumberEmpty.
  ///
  /// In en, this message translates to:
  /// **'Serial number cannot be empty'**
  String get serialnumberEmpty;

  /// No description provided for @interfaceStateInactiveDescriptionBLE.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth is disabled, please enable it to discover Bluetooth devices.'**
  String get interfaceStateInactiveDescriptionBLE;

  /// No description provided for @interfaceStateDeniedDescriptionBLE.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth permissions weren\'t granted.'**
  String get interfaceStateDeniedDescriptionBLE;

  /// No description provided for @interfaceStatePermanentDeniedDescriptionBLE.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth permissions weren\'t granted. Please enable them in your device settings.'**
  String get interfaceStatePermanentDeniedDescriptionBLE;

  /// No description provided for @requestPermission.
  ///
  /// In en, this message translates to:
  /// **'Request permission'**
  String get requestPermission;

  /// No description provided for @goToSettings.
  ///
  /// In en, this message translates to:
  /// **'Go to settings'**
  String get goToSettings;

  /// No description provided for @enableBluetooth.
  ///
  /// In en, this message translates to:
  /// **'Enable bluetooth'**
  String get enableBluetooth;

  /// No description provided for @installed.
  ///
  /// In en, this message translates to:
  /// **'Installed'**
  String get installed;

  /// No description provided for @teachInDialogDescription.
  ///
  /// In en, this message translates to:
  /// **'Would you like to teach in your device with {type}?'**
  String teachInDialogDescription(Object type);

  /// No description provided for @useMatter.
  ///
  /// In en, this message translates to:
  /// **'Use Matter'**
  String get useMatter;

  /// No description provided for @relayMode.
  ///
  /// In en, this message translates to:
  /// **'Activate relay-mode'**
  String get relayMode;

  /// No description provided for @whatsNew.
  ///
  /// In en, this message translates to:
  /// **'New in this version'**
  String get whatsNew;

  /// No description provided for @migrationHint.
  ///
  /// In en, this message translates to:
  /// **'A migration is necessary to use the new features.'**
  String get migrationHint;

  /// No description provided for @migrationHeader.
  ///
  /// In en, this message translates to:
  /// **'Migration'**
  String get migrationHeader;

  /// No description provided for @migrationProgress.
  ///
  /// In en, this message translates to:
  /// **'Migration in progress...'**
  String get migrationProgress;

  /// No description provided for @letsGo.
  ///
  /// In en, this message translates to:
  /// **'Let\'s go!'**
  String get letsGo;

  /// No description provided for @noDevicesFound.
  ///
  /// In en, this message translates to:
  /// **'No devices found. Check whether your device is in pairing mode.'**
  String get noDevicesFound;

  /// No description provided for @interfaceStateEmpty.
  ///
  /// In en, this message translates to:
  /// **'No devices were found'**
  String get interfaceStateEmpty;

  /// No description provided for @ssidEmpty.
  ///
  /// In en, this message translates to:
  /// **'SSID cannot be empty'**
  String get ssidEmpty;

  /// No description provided for @passwordEmpty.
  ///
  /// In en, this message translates to:
  /// **'Password cannot be empty'**
  String get passwordEmpty;

  /// No description provided for @settingsDeleteSettingsHeader.
  ///
  /// In en, this message translates to:
  /// **'Reset Settings'**
  String get settingsDeleteSettingsHeader;

  /// No description provided for @settingsDeleteSettingsDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to reset all settings?'**
  String get settingsDeleteSettingsDescription;

  /// No description provided for @settingsDeleteSettingsConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'All settings have been successfully reset.'**
  String get settingsDeleteSettingsConfirmationDescription;

  /// No description provided for @locationNotFound.
  ///
  /// In en, this message translates to:
  /// **'Location not found'**
  String get locationNotFound;

  /// No description provided for @timerProgramEmptySaveHint.
  ///
  /// In en, this message translates to:
  /// **'The time program is empty. Do you want to cancel the editing?'**
  String get timerProgramEmptySaveHint;

  /// No description provided for @timerProgramDaysEmptySaveHint.
  ///
  /// In en, this message translates to:
  /// **'No days are selected. Do you want to save the time program anyway?'**
  String get timerProgramDaysEmptySaveHint;

  /// No description provided for @timeProgramNoDays.
  ///
  /// In en, this message translates to:
  /// **'At least one day must be activated'**
  String get timeProgramNoDays;

  /// No description provided for @timeProgramColliding.
  ///
  /// In en, this message translates to:
  /// **'Time program collides with program {program}'**
  String timeProgramColliding(Object program);

  /// No description provided for @timeProgramDuplicated.
  ///
  /// In en, this message translates to:
  /// **'Time program is a duplicate of program {program}'**
  String timeProgramDuplicated(Object program);

  /// No description provided for @screenshotZgw16.
  ///
  /// In en, this message translates to:
  /// **'Detached house'**
  String get screenshotZgw16;

  /// No description provided for @interfaceStateUnknown.
  ///
  /// In en, this message translates to:
  /// **'No devices found'**
  String get interfaceStateUnknown;

  /// No description provided for @settingsPinChange.
  ///
  /// In en, this message translates to:
  /// **'Change PIN'**
  String get settingsPinChange;

  /// No description provided for @timeProgrammOneTime.
  ///
  /// In en, this message translates to:
  /// **'one-time'**
  String get timeProgrammOneTime;

  /// No description provided for @timeProgrammRepeating.
  ///
  /// In en, this message translates to:
  /// **'repeating'**
  String get timeProgrammRepeating;

  /// No description provided for @generalIgnore.
  ///
  /// In en, this message translates to:
  /// **'Ignore'**
  String get generalIgnore;

  /// No description provided for @timeProgramChooseDay.
  ///
  /// In en, this message translates to:
  /// **'Choose day'**
  String get timeProgramChooseDay;

  /// No description provided for @generalToday.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get generalToday;

  /// No description provided for @generalTomorrow.
  ///
  /// In en, this message translates to:
  /// **'Tomorrow'**
  String get generalTomorrow;

  /// No description provided for @bluetoothAndPINChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth and PIN successfully changed'**
  String get bluetoothAndPINChangedSuccessfully;

  /// No description provided for @generalTextDimTime.
  ///
  /// In en, this message translates to:
  /// **'Dimming time'**
  String get generalTextDimTime;

  /// No description provided for @discoverySu62Description.
  ///
  /// In en, this message translates to:
  /// **'1-Channel Time Switch Bluetooth'**
  String get discoverySu62Description;

  /// No description provided for @bluetoothAlwaysOnTitle.
  ///
  /// In en, this message translates to:
  /// **'Always on'**
  String get bluetoothAlwaysOnTitle;

  /// No description provided for @bluetoothAlwaysOnDescription.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth is permanently enabled.'**
  String get bluetoothAlwaysOnDescription;

  /// No description provided for @bluetoothAlwaysOnHint.
  ///
  /// In en, this message translates to:
  /// **'Note: If this setting is activated, the device is permanently visible to everyone via Bluetooth! It is recommended to change the default PIN.'**
  String get bluetoothAlwaysOnHint;

  /// No description provided for @bluetoothManualStartupOnTitle.
  ///
  /// In en, this message translates to:
  /// **'Temporary on'**
  String get bluetoothManualStartupOnTitle;

  /// No description provided for @bluetoothManualStartupOnDescription.
  ///
  /// In en, this message translates to:
  /// **'After power is applied, Bluetooth is activated for 3 minutes.'**
  String get bluetoothManualStartupOnDescription;

  /// No description provided for @bluetoothManualStartupOnHint.
  ///
  /// In en, this message translates to:
  /// **'Note: Pairing standby is activated for 3 minutes and then switches off. If a new connection is to be established, the button must be held down for approx. 5 seconds.'**
  String get bluetoothManualStartupOnHint;

  /// No description provided for @bluetoothManualStartupOffTitle.
  ///
  /// In en, this message translates to:
  /// **'Manual startup'**
  String get bluetoothManualStartupOffTitle;

  /// No description provided for @bluetoothManualStartupOffDescription.
  ///
  /// In en, this message translates to:
  /// **'Bluetooth is manually activated via the button input.'**
  String get bluetoothManualStartupOffDescription;

  /// No description provided for @bluetoothManualStartupOffHint.
  ///
  /// In en, this message translates to:
  /// **'Note: To activate Bluetooth, the button on the button input must be held down for approx. 5 seconds.'**
  String get bluetoothManualStartupOffHint;

  /// No description provided for @timeProgrammOneTimeRepeatingDescription.
  ///
  /// In en, this message translates to:
  /// **'Programmes can either be executed repeatedly by always carrying out a switching operation on the configured days and times, or they can be executed only once at the configured switching time.'**
  String get timeProgrammOneTimeRepeatingDescription;

  /// No description provided for @versionHeader.
  ///
  /// In en, this message translates to:
  /// **'Version {version}'**
  String versionHeader(Object version);

  /// No description provided for @releaseNotesHeader.
  ///
  /// In en, this message translates to:
  /// **'Release notes'**
  String get releaseNotesHeader;

  /// No description provided for @release30Header.
  ///
  /// In en, this message translates to:
  /// **'The new ELTAKO Connect app is here!'**
  String get release30Header;

  /// No description provided for @release30FeatureDesignHeader.
  ///
  /// In en, this message translates to:
  /// **'New design'**
  String get release30FeatureDesignHeader;

  /// No description provided for @release30FeatureDesignDescription.
  ///
  /// In en, this message translates to:
  /// **'The app has been completely revised and has a new design. It is now even easier and more intuitive to use.'**
  String get release30FeatureDesignDescription;

  /// No description provided for @release30FeaturePerformanceHeader.
  ///
  /// In en, this message translates to:
  /// **'Improved performance'**
  String get release30FeaturePerformanceHeader;

  /// No description provided for @release30FeaturePerformanceDescription.
  ///
  /// In en, this message translates to:
  /// **'Enjoy an enhanced set-up and reduced loading times - for a better user experience.'**
  String get release30FeaturePerformanceDescription;

  /// No description provided for @release30FeatureConfigurationHeader.
  ///
  /// In en, this message translates to:
  /// **'Cross-device configurations'**
  String get release30FeatureConfigurationHeader;

  /// No description provided for @release30FeatureConfigurationDescription.
  ///
  /// In en, this message translates to:
  /// **'Save device configurations and transfer them to other devices. Even if they do not have the same hardware, you can, for example, transfer the configuration of your S2U12DBT1+1-UC to an ASSU-BT or vice versa.'**
  String get release30FeatureConfigurationDescription;

  /// No description provided for @release31Header.
  ///
  /// In en, this message translates to:
  /// **'The new flush-mounted 1-channel time switch with Bluetooth is here!'**
  String get release31Header;

  /// No description provided for @release31Description.
  ///
  /// In en, this message translates to:
  /// **'What can the SU62PF-BT/UC do?'**
  String get release31Description;

  /// No description provided for @release31SU62Name.
  ///
  /// In en, this message translates to:
  /// **'SU62PF-BT/UC'**
  String get release31SU62Name;

  /// No description provided for @release31DeviceNote1.
  ///
  /// In en, this message translates to:
  /// **'Up to 60 time programs.'**
  String get release31DeviceNote1;

  /// No description provided for @release31DeviceNote2.
  ///
  /// In en, this message translates to:
  /// **'Astro function: The clock switches devices based on sunrise and sunset.'**
  String get release31DeviceNote2;

  /// No description provided for @release31DeviceNote3.
  ///
  /// In en, this message translates to:
  /// **'Random mode: switching times can be randomly shifted by up to 15 minutes.'**
  String get release31DeviceNote3;

  /// No description provided for @release31DeviceNote4.
  ///
  /// In en, this message translates to:
  /// **'Summer/winter time changeover: The clock automatically switches to summer or winter time.'**
  String get release31DeviceNote4;

  /// No description provided for @release31DeviceNote5.
  ///
  /// In en, this message translates to:
  /// **'Universal supply and control voltage 12-230V UC.'**
  String get release31DeviceNote5;

  /// No description provided for @release31DeviceNote6.
  ///
  /// In en, this message translates to:
  /// **'Push-button input for manual switching.'**
  String get release31DeviceNote6;

  /// No description provided for @release31DeviceNote7.
  ///
  /// In en, this message translates to:
  /// **'1 NO contact potential-free 10 A/250 V AC.'**
  String get release31DeviceNote7;

  /// No description provided for @release31DeviceNote8.
  ///
  /// In en, this message translates to:
  /// **'One-time execution of time programs.'**
  String get release31DeviceNote8;

  /// No description provided for @generalNew.
  ///
  /// In en, this message translates to:
  /// **'New'**
  String get generalNew;

  /// No description provided for @yearsAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Last year} other {{count} years ago}}'**
  String yearsAgo(num count);

  /// No description provided for @monthsAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Last month} other {{count} months ago}}'**
  String monthsAgo(num count);

  /// No description provided for @weeksAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Last week} other {{count} weeks ago}}'**
  String weeksAgo(num count);

  /// No description provided for @daysAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Yesterday} other {{count} days ago}}'**
  String daysAgo(num count);

  /// No description provided for @minutesAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {A minute ago} other {{count} minutes ago}}'**
  String minutesAgo(num count);

  /// No description provided for @hoursAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {An hour ago} other {{count} hours ago}}'**
  String hoursAgo(num count);

  /// No description provided for @secondsAgo.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {A second ago} other {{count} seconds ago}}'**
  String secondsAgo(num count);

  /// No description provided for @justNow.
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get justNow;

  /// No description provided for @discoveryEsripmDescription.
  ///
  /// In en, this message translates to:
  /// **'Impulse switching relay IP Matter'**
  String get discoveryEsripmDescription;

  /// No description provided for @generalTextKidsRoom.
  ///
  /// In en, this message translates to:
  /// **'Childrens room function'**
  String get generalTextKidsRoom;

  /// No description provided for @generalDescriptionKidsRoom.
  ///
  /// In en, this message translates to:
  /// **'When switching on with a longer push-button action ({mode}), the light is switched on at the lowest brightness level after approx. 1 second and slowly dimmed up as long as the push-button is held down, without changing the last saved brightness level.'**
  String generalDescriptionKidsRoom(Object mode);

  /// No description provided for @generalTextSceneButton.
  ///
  /// In en, this message translates to:
  /// **'Scene button'**
  String get generalTextSceneButton;

  /// No description provided for @settingsEnOceanConfigHeader.
  ///
  /// In en, this message translates to:
  /// **'EnOcean configuration'**
  String get settingsEnOceanConfigHeader;

  /// No description provided for @enOceanConfigChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'EnOcean configuration successfully changed'**
  String get enOceanConfigChangedSuccessfully;

  /// No description provided for @activateEnOceanRepeater.
  ///
  /// In en, this message translates to:
  /// **'Activate EnOcean Repeater'**
  String get activateEnOceanRepeater;

  /// No description provided for @enOceanRepeaterLevel.
  ///
  /// In en, this message translates to:
  /// **'Repeater level'**
  String get enOceanRepeaterLevel;

  /// No description provided for @enOceanRepeaterLevel1.
  ///
  /// In en, this message translates to:
  /// **'One level'**
  String get enOceanRepeaterLevel1;

  /// No description provided for @enOceanRepeaterLevel2.
  ///
  /// In en, this message translates to:
  /// **'Two levels'**
  String get enOceanRepeaterLevel2;

  /// No description provided for @enOceanRepeaterOffDescription.
  ///
  /// In en, this message translates to:
  /// **'No wireless signals are received from sensors.'**
  String get enOceanRepeaterOffDescription;

  /// No description provided for @enOceanRepeaterLevel1Description.
  ///
  /// In en, this message translates to:
  /// **'Only the wireless signals from sensors are received, checked and forwarded at full transmission power. Wireless signals from other repeaters are ignored to reduce the amount of data.'**
  String get enOceanRepeaterLevel1Description;

  /// No description provided for @enOceanRepeaterLevel2Description.
  ///
  /// In en, this message translates to:
  /// **'In addition to the wireless signals from sensors, the wireless signals from 1-level repeaters are also processed. A wireless signal can therefore be received and amplified a maximum of two times. Wireless repeaters do not need to be taught in. They receive and amplify the wireless signals from all wireless sensors in their reception area.'**
  String get enOceanRepeaterLevel2Description;

  /// No description provided for @settingsSensorHeader.
  ///
  /// In en, this message translates to:
  /// **'Sensors'**
  String get settingsSensorHeader;

  /// No description provided for @sensorChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Sensors successfully changed'**
  String get sensorChangedSuccessfully;

  /// No description provided for @wiredButton.
  ///
  /// In en, this message translates to:
  /// **'Wired pushbutton'**
  String get wiredButton;

  /// No description provided for @enOceanId.
  ///
  /// In en, this message translates to:
  /// **'EnOcean-ID'**
  String get enOceanId;

  /// No description provided for @enOceanAddManually.
  ///
  /// In en, this message translates to:
  /// **'Enter or scan EnOcean-ID'**
  String get enOceanAddManually;

  /// No description provided for @enOceanIdInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid EnOcean-ID'**
  String get enOceanIdInvalid;

  /// No description provided for @enOceanAddAutomatically.
  ///
  /// In en, this message translates to:
  /// **'Teach-in with EnOcean Telegram'**
  String get enOceanAddAutomatically;

  /// No description provided for @enOceanAddDescription.
  ///
  /// In en, this message translates to:
  /// **'The EnOcean wireless protocol makes it possible to teach-in and operate push-buttons in your actuator.\n\nChoose either automatic teach-in to teach-in push-buttons at the touch of a button or select the manual option to scan or type in the EnOcean ID of your push-button.'**
  String get enOceanAddDescription;

  /// No description provided for @enOceanTelegram.
  ///
  /// In en, this message translates to:
  /// **'Telegram'**
  String get enOceanTelegram;

  /// No description provided for @enOceanCodeScan.
  ///
  /// In en, this message translates to:
  /// **'Enter the EnOcean-ID of your {sensorType} or scan the EnOcean-QR-Code of your {sensorType}, to add it'**
  String enOceanCodeScan(Object sensorType);

  /// No description provided for @enOceanCode.
  ///
  /// In en, this message translates to:
  /// **'EnOcean QR code'**
  String get enOceanCode;

  /// No description provided for @enOceanCodeScanDescription.
  ///
  /// In en, this message translates to:
  /// **'Search for the EnOcean code on your {sensorType} and scan it with your camera.'**
  String enOceanCodeScanDescription(Object sensorType);

  /// No description provided for @enOceanButton.
  ///
  /// In en, this message translates to:
  /// **'EnOcean pushbutton'**
  String get enOceanButton;

  /// No description provided for @enOceanBackpack.
  ///
  /// In en, this message translates to:
  /// **'EnOcean adapter'**
  String get enOceanBackpack;

  /// No description provided for @sensorNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'No sensors have been paired yet'**
  String get sensorNotAvailable;

  /// No description provided for @sensorAdd.
  ///
  /// In en, this message translates to:
  /// **'Add sensors'**
  String get sensorAdd;

  /// No description provided for @sensorCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel teach-in'**
  String get sensorCancel;

  /// No description provided for @sensorCancelDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to cancel the button teach-in?'**
  String get sensorCancelDescription;

  /// No description provided for @getEnOceanBackpack.
  ///
  /// In en, this message translates to:
  /// **'Get your EnOcean adapter'**
  String get getEnOceanBackpack;

  /// No description provided for @enOceanBackpackMissing.
  ///
  /// In en, this message translates to:
  /// **'To enter the fantastic world of perfect connectivity and communication, you need an EnOcean adapter.\nClick here for more information'**
  String get enOceanBackpackMissing;

  /// No description provided for @sensorEditChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'{sensorName} successfully changed'**
  String sensorEditChangedSuccessfully(Object sensorName);

  /// No description provided for @sensorConnectedVia.
  ///
  /// In en, this message translates to:
  /// **'connected via {deviceName}'**
  String sensorConnectedVia(Object deviceName);

  /// No description provided for @lastSeen.
  ///
  /// In en, this message translates to:
  /// **'Last seen'**
  String get lastSeen;

  /// No description provided for @setButtonOrientation.
  ///
  /// In en, this message translates to:
  /// **'Set orientation'**
  String get setButtonOrientation;

  /// No description provided for @setButtonType.
  ///
  /// In en, this message translates to:
  /// **'Set button type'**
  String get setButtonType;

  /// No description provided for @button1Way.
  ///
  /// In en, this message translates to:
  /// **'1-way pushbutton'**
  String get button1Way;

  /// No description provided for @button2Way.
  ///
  /// In en, this message translates to:
  /// **'2-way pushbutton'**
  String get button2Way;

  /// No description provided for @button4Way.
  ///
  /// In en, this message translates to:
  /// **'4-way pushbutton'**
  String get button4Way;

  /// No description provided for @buttonUnset.
  ///
  /// In en, this message translates to:
  /// **'not set'**
  String get buttonUnset;

  /// No description provided for @button.
  ///
  /// In en, this message translates to:
  /// **'Push button'**
  String get button;

  /// No description provided for @sensor.
  ///
  /// In en, this message translates to:
  /// **'Sensor'**
  String get sensor;

  /// No description provided for @sensorsFound.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =0 {No sensors found} one {1 sensor found} other {{count} sensors found}}'**
  String sensorsFound(num count);

  /// No description provided for @sensorSearch.
  ///
  /// In en, this message translates to:
  /// **'Search for sensors'**
  String get sensorSearch;

  /// No description provided for @searchAgain.
  ///
  /// In en, this message translates to:
  /// **'Search again'**
  String get searchAgain;

  /// No description provided for @sensorTeachInHeader.
  ///
  /// In en, this message translates to:
  /// **'Teach in {sensorType}'**
  String sensorTeachInHeader(Object sensorType);

  /// No description provided for @sensorChooseHeader.
  ///
  /// In en, this message translates to:
  /// **'Choose {sensorType}'**
  String sensorChooseHeader(Object sensorType);

  /// No description provided for @sensorChooseDescription.
  ///
  /// In en, this message translates to:
  /// **'Choose a button to teach in'**
  String get sensorChooseDescription;

  /// No description provided for @sensorCategoryDescription.
  ///
  /// In en, this message translates to:
  /// **'Select the category of the sensor you want to add.'**
  String get sensorCategoryDescription;

  /// No description provided for @sensorName.
  ///
  /// In en, this message translates to:
  /// **'Button name'**
  String get sensorName;

  /// No description provided for @sensorNameFooter.
  ///
  /// In en, this message translates to:
  /// **'Name your button'**
  String get sensorNameFooter;

  /// No description provided for @sensorAddedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'{sensorName} was added successfully'**
  String sensorAddedSuccessfully(Object sensorName);

  /// No description provided for @sensorDelete.
  ///
  /// In en, this message translates to:
  /// **'Delete {sensorType}'**
  String sensorDelete(Object sensorType);

  /// No description provided for @sensorDeleteHint.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to delete the {sensorType} {sensorName}?'**
  String sensorDeleteHint(Object sensorName, Object sensorType);

  /// No description provided for @sensorDeletedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'{sensorName} was deleted successfully'**
  String sensorDeletedSuccessfully(Object sensorName);

  /// No description provided for @buttonTapDescription.
  ///
  /// In en, this message translates to:
  /// **'Tap the push button you want to add.'**
  String get buttonTapDescription;

  /// No description provided for @waitingForTelegram.
  ///
  /// In en, this message translates to:
  /// **'The actuator is waiting for the telegram'**
  String get waitingForTelegram;

  /// No description provided for @copied.
  ///
  /// In en, this message translates to:
  /// **'Copied'**
  String get copied;

  /// No description provided for @pairingFailed.
  ///
  /// In en, this message translates to:
  /// **'{sensorType} already paired'**
  String pairingFailed(Object sensorType);

  /// No description provided for @generalDescriptionUniversalbutton.
  ///
  /// In en, this message translates to:
  /// **'With the universal button, the direction is reversed by briefly releasing the button. Short control commands switch on or off.'**
  String get generalDescriptionUniversalbutton;

  /// No description provided for @generalDescriptionDirectionalbutton.
  ///
  /// In en, this message translates to:
  /// **'The direction button is \'switch on and dim up\' at the top and \'switch off and dim down\' at the bottom.'**
  String get generalDescriptionDirectionalbutton;

  /// No description provided for @matterForwardingDescription.
  ///
  /// In en, this message translates to:
  /// **'Forwarding of the Matter telegrams'**
  String get matterForwardingDescription;

  /// No description provided for @none.
  ///
  /// In en, this message translates to:
  /// **'None'**
  String get none;

  /// No description provided for @buttonNoneDescription.
  ///
  /// In en, this message translates to:
  /// **'The button has no functionality'**
  String get buttonNoneDescription;

  /// No description provided for @buttonUnsetDescription.
  ///
  /// In en, this message translates to:
  /// **'The button has no behaviour set'**
  String get buttonUnsetDescription;

  /// No description provided for @sensorButtonTypeChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Button type successfully changed'**
  String get sensorButtonTypeChangedSuccessfully;

  /// No description provided for @forExample.
  ///
  /// In en, this message translates to:
  /// **'e.g. {example}}'**
  String forExample(Object example);

  /// No description provided for @enOceanQRCodeInvalidDescription.
  ///
  /// In en, this message translates to:
  /// **'Only possible from production date 44/20'**
  String get enOceanQRCodeInvalidDescription;

  /// No description provided for @input.
  ///
  /// In en, this message translates to:
  /// **'Input'**
  String get input;

  /// No description provided for @buttonSceneValueOverride.
  ///
  /// In en, this message translates to:
  /// **'Override scene button value'**
  String get buttonSceneValueOverride;

  /// No description provided for @buttonSceneValueOverrideDescription.
  ///
  /// In en, this message translates to:
  /// **'The scene button value will be overwritten with the current dim value through a button long press'**
  String get buttonSceneValueOverrideDescription;

  /// No description provided for @buttonSceneDescription.
  ///
  /// In en, this message translates to:
  /// **'The scene button turns on at a specific dim value'**
  String get buttonSceneDescription;

  /// No description provided for @buttonPress.
  ///
  /// In en, this message translates to:
  /// **'Button pressed'**
  String get buttonPress;

  /// No description provided for @triggerOn.
  ///
  /// In en, this message translates to:
  /// **'Universal push-button or direction button pressed on turn on side'**
  String get triggerOn;

  /// No description provided for @triggerOff.
  ///
  /// In en, this message translates to:
  /// **'Universal push-button or direction button pressed on turn off side'**
  String get triggerOff;

  /// No description provided for @centralOn.
  ///
  /// In en, this message translates to:
  /// **'Central On'**
  String get centralOn;

  /// No description provided for @centralOff.
  ///
  /// In en, this message translates to:
  /// **'Central Off'**
  String get centralOff;

  /// No description provided for @centralButton.
  ///
  /// In en, this message translates to:
  /// **'Central Button'**
  String get centralButton;

  /// No description provided for @enOceanAdapterNotFound.
  ///
  /// In en, this message translates to:
  /// **'No EnOcean adapter found'**
  String get enOceanAdapterNotFound;

  /// No description provided for @updateRequired.
  ///
  /// In en, this message translates to:
  /// **'Update required'**
  String get updateRequired;

  /// No description provided for @updateRequiredDescription.
  ///
  /// In en, this message translates to:
  /// **'Your app requires an update to support this new device.'**
  String get updateRequiredDescription;

  /// No description provided for @release32Header.
  ///
  /// In en, this message translates to:
  /// **'The new BR64 with Matter and EnOcean and the new SU62 Bluetooth flush-mounted time switch are now available!'**
  String get release32Header;

  /// No description provided for @release32EUD64Header.
  ///
  /// In en, this message translates to:
  /// **'The new flush-mounted 1-channel dimmer with Matter over Wi-Fi and up to 300W is here!'**
  String get release32EUD64Header;

  /// No description provided for @release32EUD64Note1.
  ///
  /// In en, this message translates to:
  /// **'Configuration of dimming speed, on/off speed, children’s room/sleep mode, and much more.'**
  String get release32EUD64Note1;

  /// No description provided for @release32EUD64Note2.
  ///
  /// In en, this message translates to:
  /// **'The functionality of the EUD64NPN-IPM can be expanded through adapters, such as the EnOcean adapter EOA64.'**
  String get release32EUD64Note2;

  /// No description provided for @release32EUD64Note3.
  ///
  /// In en, this message translates to:
  /// **'Up to 30 EnOcean wireless switches can be directly linked to the EUD64NPN-IPM in combination with the EnOcean adapter EOA64 and forwarded to Matter.'**
  String get release32EUD64Note3;

  /// No description provided for @release32EUD64Note4.
  ///
  /// In en, this message translates to:
  /// **'Two wired button inputs can be directly linked to the EUD64NPN-IPM or forwarded to Matter.'**
  String get release32EUD64Note4;

  /// No description provided for @release32ESR64Header.
  ///
  /// In en, this message translates to:
  /// **'The new potential-free flush-mounted 1-channel switch actuator with Matter over Wi-Fi and up to 16A is here!'**
  String get release32ESR64Header;

  /// No description provided for @release32ESR64Note1.
  ///
  /// In en, this message translates to:
  /// **'Configuration of various functions such as pulse switch (ES), relay function (ER), normally closed (ER-Inverse), and much more.'**
  String get release32ESR64Note1;

  /// No description provided for @release32ESR64Note2.
  ///
  /// In en, this message translates to:
  /// **'The functionality of the ESR64PF-IPM can be expanded through adapters, such as the EnOcean adapter EOA64.'**
  String get release32ESR64Note2;

  /// No description provided for @release32ESR64Note3.
  ///
  /// In en, this message translates to:
  /// **'Up to 30 EnOcean wireless switches can be directly linked to the ESR64PF-IPM in combination with the EnOcean adapter EOA64 and forwarded to Matter.'**
  String get release32ESR64Note3;

  /// No description provided for @release32ESR64Note4.
  ///
  /// In en, this message translates to:
  /// **'One wired button input can be directly linked to the ESR64PF-IPM or forwarded to Matter.'**
  String get release32ESR64Note4;

  /// No description provided for @buttonsFound.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =0 {No buttons found} one {1 button found} other {{count} buttons found}}'**
  String buttonsFound(num count);

  /// No description provided for @doubleImpuls.
  ///
  /// In en, this message translates to:
  /// **'with a double impulse'**
  String get doubleImpuls;

  /// No description provided for @impulseDescription.
  ///
  /// In en, this message translates to:
  /// **'If the channel is on, it is turned off by an impulse.'**
  String get impulseDescription;

  /// No description provided for @locationServiceEnable.
  ///
  /// In en, this message translates to:
  /// **'Activate location'**
  String get locationServiceEnable;

  /// No description provided for @locationServiceDisabledDescription.
  ///
  /// In en, this message translates to:
  /// **'Location is disabled. Your operating system version needs the location to be able to find Bluetooth devices.'**
  String get locationServiceDisabledDescription;

  /// No description provided for @locationPermissionDeniedNoPosition.
  ///
  /// In en, this message translates to:
  /// **'Location permissions have not been granted. Your operating system version requires location permissions to be able to find Bluetooth devices. Please allow the location permission in your device settings.'**
  String get locationPermissionDeniedNoPosition;

  /// No description provided for @interfaceStatePermanentDeniedDescriptionDevicesAround.
  ///
  /// In en, this message translates to:
  /// **'Nearby devices permission wasn\'t granted. Please enable the permission in your device settings.'**
  String get interfaceStatePermanentDeniedDescriptionDevicesAround;

  /// No description provided for @permissionNearbyDevices.
  ///
  /// In en, this message translates to:
  /// **'Nearby devices'**
  String get permissionNearbyDevices;

  /// No description provided for @release320Header.
  ///
  /// In en, this message translates to:
  /// **'The new powerful universal dimmer EUD12NPN-BT/600W-230V is here!'**
  String get release320Header;

  /// No description provided for @release320EUD600Header.
  ///
  /// In en, this message translates to:
  /// **'What can the new universal dimmer do?'**
  String get release320EUD600Header;

  /// No description provided for @release320EUD600Note1.
  ///
  /// In en, this message translates to:
  /// **'Universal dimmer with up to 600W power'**
  String get release320EUD600Note1;

  /// No description provided for @release320EUD600Note2.
  ///
  /// In en, this message translates to:
  /// **'Expandable with LUD12 power extension up to 3800W'**
  String get release320EUD600Note2;

  /// No description provided for @release320EUD600Note3.
  ///
  /// In en, this message translates to:
  /// **'Local operation with universal or directional push-button'**
  String get release320EUD600Note3;

  /// No description provided for @release320EUD600Note4.
  ///
  /// In en, this message translates to:
  /// **'Central functions On / Off'**
  String get release320EUD600Note4;

  /// No description provided for @release320EUD600Note5.
  ///
  /// In en, this message translates to:
  /// **'Motion detector input for added convenience'**
  String get release320EUD600Note5;

  /// No description provided for @release320EUD600Note6.
  ///
  /// In en, this message translates to:
  /// **'Integrated timer with 10 switching programs'**
  String get release320EUD600Note6;

  /// No description provided for @release320EUD600Note7.
  ///
  /// In en, this message translates to:
  /// **'Astro function'**
  String get release320EUD600Note7;

  /// No description provided for @release320EUD600Note8.
  ///
  /// In en, this message translates to:
  /// **'Individual switch-on brightness'**
  String get release320EUD600Note8;

  /// No description provided for @mqttClientCertificate.
  ///
  /// In en, this message translates to:
  /// **'Client Certificate'**
  String get mqttClientCertificate;

  /// No description provided for @mqttClientCertificateHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT Client Certificate'**
  String get mqttClientCertificateHint;

  /// No description provided for @mqttClientKey.
  ///
  /// In en, this message translates to:
  /// **'Client Key'**
  String get mqttClientKey;

  /// No description provided for @mqttClientKeyHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT Client Key'**
  String get mqttClientKeyHint;

  /// No description provided for @mqttClientPassword.
  ///
  /// In en, this message translates to:
  /// **'Client Password'**
  String get mqttClientPassword;

  /// No description provided for @mqttClientPasswordHint.
  ///
  /// In en, this message translates to:
  /// **'MQTT Client Password'**
  String get mqttClientPasswordHint;

  /// No description provided for @mqttEnableHomeAssistantDiscovery.
  ///
  /// In en, this message translates to:
  /// **'Enable HomeAssistant MQTT Discovery'**
  String get mqttEnableHomeAssistantDiscovery;

  /// No description provided for @modbusTcp.
  ///
  /// In en, this message translates to:
  /// **'Modbus TCP'**
  String get modbusTcp;

  /// No description provided for @enableInterface.
  ///
  /// In en, this message translates to:
  /// **'Enable interface'**
  String get enableInterface;

  /// No description provided for @busAddress.
  ///
  /// In en, this message translates to:
  /// **'Bus address'**
  String get busAddress;

  /// No description provided for @busAddressWithAddress.
  ///
  /// In en, this message translates to:
  /// **'Bus address {index}'**
  String busAddressWithAddress(Object index);

  /// No description provided for @deviceType.
  ///
  /// In en, this message translates to:
  /// **'Device type'**
  String get deviceType;

  /// No description provided for @registerTable.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, one {Register table} other {Register tables}}'**
  String registerTable(num count);

  /// No description provided for @currentValues.
  ///
  /// In en, this message translates to:
  /// **'Current values'**
  String get currentValues;

  /// No description provided for @requestRTU.
  ///
  /// In en, this message translates to:
  /// **'Request RTU'**
  String get requestRTU;

  /// No description provided for @requestPriority.
  ///
  /// In en, this message translates to:
  /// **'Request priority'**
  String get requestPriority;

  /// No description provided for @mqttForwarding.
  ///
  /// In en, this message translates to:
  /// **'Forwarding to MQTT'**
  String get mqttForwarding;

  /// No description provided for @historicData.
  ///
  /// In en, this message translates to:
  /// **'Historic data'**
  String get historicData;

  /// No description provided for @dataFormat.
  ///
  /// In en, this message translates to:
  /// **'Data format'**
  String get dataFormat;

  /// No description provided for @dataType.
  ///
  /// In en, this message translates to:
  /// **'Data type'**
  String get dataType;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @readWrite.
  ///
  /// In en, this message translates to:
  /// **'Read/Write'**
  String get readWrite;

  /// No description provided for @unit.
  ///
  /// In en, this message translates to:
  /// **'Unit'**
  String get unit;

  /// No description provided for @registerTableReset.
  ///
  /// In en, this message translates to:
  /// **'Reset register table'**
  String get registerTableReset;

  /// No description provided for @registerTableResetDescription.
  ///
  /// In en, this message translates to:
  /// **'Should the register table really be reset?'**
  String get registerTableResetDescription;

  /// No description provided for @notConfigured.
  ///
  /// In en, this message translates to:
  /// **'Not configured'**
  String get notConfigured;

  /// No description provided for @release330ZGW16Header.
  ///
  /// In en, this message translates to:
  /// **'Major update for the ZGW16WL-IP'**
  String get release330ZGW16Header;

  /// No description provided for @release330Header.
  ///
  /// In en, this message translates to:
  /// **'The ZGW16WL-IP with up to 16 electricity meters'**
  String get release330Header;

  /// No description provided for @release330ZGW16Note1.
  ///
  /// In en, this message translates to:
  /// **'Supports up to 16 ELTAKO Modbus electricity meters'**
  String get release330ZGW16Note1;

  /// No description provided for @release330ZGW16Note2.
  ///
  /// In en, this message translates to:
  /// **'Modbus TCP support'**
  String get release330ZGW16Note2;

  /// No description provided for @release330ZGW16Note3.
  ///
  /// In en, this message translates to:
  /// **'MQTT Discovery support'**
  String get release330ZGW16Note3;

  /// No description provided for @screenshotButtonLivingRoom.
  ///
  /// In en, this message translates to:
  /// **'Living room push-button'**
  String get screenshotButtonLivingRoom;

  /// No description provided for @registerChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Register successfully changed'**
  String get registerChangedSuccessfully;

  /// No description provided for @serverCertificateEmpty.
  ///
  /// In en, this message translates to:
  /// **'Server certificate cannot be empty'**
  String get serverCertificateEmpty;

  /// No description provided for @registerTemplates.
  ///
  /// In en, this message translates to:
  /// **'Register templates'**
  String get registerTemplates;

  /// No description provided for @registerTemplateChangedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Register template successfully changed'**
  String get registerTemplateChangedSuccessfully;

  /// No description provided for @registerTemplateReset.
  ///
  /// In en, this message translates to:
  /// **'Reset Register template'**
  String get registerTemplateReset;

  /// No description provided for @registerTemplateResetDescription.
  ///
  /// In en, this message translates to:
  /// **'Should the register template really be reset?'**
  String get registerTemplateResetDescription;

  /// No description provided for @registerTemplateNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'No Register templates available'**
  String get registerTemplateNotAvailable;

  /// No description provided for @rename.
  ///
  /// In en, this message translates to:
  /// **'Rename'**
  String get rename;

  /// No description provided for @registerName.
  ///
  /// In en, this message translates to:
  /// **'Register name'**
  String get registerName;

  /// No description provided for @registerRenameDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter a custom name for the register'**
  String get registerRenameDescription;

  /// No description provided for @restart.
  ///
  /// In en, this message translates to:
  /// **'Restart device'**
  String get restart;

  /// No description provided for @restartDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to restart the device?'**
  String get restartDescription;

  /// No description provided for @restartConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'The device is now restarting'**
  String get restartConfirmationDescription;

  /// No description provided for @deleteAllElectricityMeters.
  ///
  /// In en, this message translates to:
  /// **'Delete all electricity meters'**
  String get deleteAllElectricityMeters;

  /// No description provided for @deleteAllElectricityMetersDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to delete all electricity meters?'**
  String get deleteAllElectricityMetersDescription;

  /// No description provided for @deleteAllElectricityMetersConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'All electricity meters have been successfully deleted'**
  String get deleteAllElectricityMetersConfirmationDescription;

  /// No description provided for @resetAllElectricityMeters.
  ///
  /// In en, this message translates to:
  /// **'Reset all electricity meter configurations'**
  String get resetAllElectricityMeters;

  /// No description provided for @resetAllElectricityMetersDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to reset all electricity meter configurations?'**
  String get resetAllElectricityMetersDescription;

  /// No description provided for @resetAllElectricityMetersConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'All electricity meter configurations have been successfully reset'**
  String get resetAllElectricityMetersConfirmationDescription;

  /// No description provided for @deleteElectricityMeterHistories.
  ///
  /// In en, this message translates to:
  /// **'Delete all electricity meter histories'**
  String get deleteElectricityMeterHistories;

  /// No description provided for @deleteElectricityMeterHistoriesDescription.
  ///
  /// In en, this message translates to:
  /// **'Do you really want to delete all electricity meter histories?'**
  String get deleteElectricityMeterHistoriesDescription;

  /// No description provided for @deleteElectricityMeterHistoriesConfirmationDescription.
  ///
  /// In en, this message translates to:
  /// **'All electricity meter histories have been successfully deleted'**
  String get deleteElectricityMeterHistoriesConfirmationDescription;

  /// No description provided for @multipleElectricityMetersSupportMissing.
  ///
  /// In en, this message translates to:
  /// **'Your device currently supports only one electricity meter. Please update your firmware.'**
  String get multipleElectricityMetersSupportMissing;

  /// No description provided for @consumptionWithUnit.
  ///
  /// In en, this message translates to:
  /// **'Usage (kWh)'**
  String get consumptionWithUnit;

  /// No description provided for @exportWithUnit.
  ///
  /// In en, this message translates to:
  /// **'Delivery (kWh)'**
  String get exportWithUnit;

  /// No description provided for @importWithUnit.
  ///
  /// In en, this message translates to:
  /// **'Consumption (kWh)'**
  String get importWithUnit;

  /// No description provided for @resourceWarningHeader.
  ///
  /// In en, this message translates to:
  /// **'Resource limitations'**
  String get resourceWarningHeader;

  /// No description provided for @mqttAndTcpResourceWarning.
  ///
  /// In en, this message translates to:
  /// **'Operating MQTT and Modbus TCP at the same time is not possible due to limited system resources. Deactivate {protocol} first.'**
  String mqttAndTcpResourceWarning(Object protocol);

  /// No description provided for @mqttEnabled.
  ///
  /// In en, this message translates to:
  /// **'MQTT enabled'**
  String get mqttEnabled;

  /// No description provided for @redirectMQTT.
  ///
  /// In en, this message translates to:
  /// **'Go to MQTT Settings'**
  String get redirectMQTT;

  /// No description provided for @redirectModbus.
  ///
  /// In en, this message translates to:
  /// **'Go to Modbus Settings'**
  String get redirectModbus;

  /// No description provided for @unsupportedSettingDescription.
  ///
  /// In en, this message translates to:
  /// **'With your current firmware version, some of the device settings are not supported. Please update your firmware to use the new features'**
  String get unsupportedSettingDescription;

  /// No description provided for @updateNow.
  ///
  /// In en, this message translates to:
  /// **'Update now'**
  String get updateNow;

  /// No description provided for @zgw241Hint.
  ///
  /// In en, this message translates to:
  /// **'With this update, Modbus TCP is enabled by default and MQTT is disabled. This can be changed in the settings. With support for up to 16 counters, many optimisations have been made; this may lead to changes in the device settings. Please restart the device after adjusting the settings.'**
  String get zgw241Hint;

  /// No description provided for @deviceConfigChangedSuccesfully.
  ///
  /// In en, this message translates to:
  /// **'Device Configuration successfully changed'**
  String get deviceConfigChangedSuccesfully;

  /// No description provided for @deviceConfiguration.
  ///
  /// In en, this message translates to:
  /// **'Device configuration'**
  String get deviceConfiguration;

  /// No description provided for @tiltModeToggle.
  ///
  /// In en, this message translates to:
  /// **'Tilt mode'**
  String get tiltModeToggle;

  /// No description provided for @tiltModeToggleFooter.
  ///
  /// In en, this message translates to:
  /// **'If the device is set up in Matter, all functions must be reconfigured there'**
  String get tiltModeToggleFooter;

  /// No description provided for @shaderMovementDirection.
  ///
  /// In en, this message translates to:
  /// **'Reverse Up/Down'**
  String get shaderMovementDirection;

  /// No description provided for @shaderMovementDirectionDescription.
  ///
  /// In en, this message translates to:
  /// **'Reverse the direction for Up/Down movement of the motor'**
  String get shaderMovementDirectionDescription;

  /// No description provided for @tiltTime.
  ///
  /// In en, this message translates to:
  /// **'Tilt runtime'**
  String get tiltTime;

  /// No description provided for @changeTiltModeDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'{target, select, true {Enable} false {Disable} other {Change}} tilt function'**
  String changeTiltModeDialogTitle(String target);

  /// No description provided for @changeTiltModeDialogConfirmation.
  ///
  /// In en, this message translates to:
  /// **'{target, select, true {Enable} false {Disable} other {Change}}'**
  String changeTiltModeDialogConfirmation(String target);

  /// No description provided for @generalTextSlatSetting.
  ///
  /// In en, this message translates to:
  /// **'Slat setting'**
  String get generalTextSlatSetting;

  /// No description provided for @generalTextPosition.
  ///
  /// In en, this message translates to:
  /// **'Position'**
  String get generalTextPosition;

  /// No description provided for @generalTextSlatPosition.
  ///
  /// In en, this message translates to:
  /// **'Slat position'**
  String get generalTextSlatPosition;

  /// No description provided for @slatSettingDescription.
  ///
  /// In en, this message translates to:
  /// **'Activate the slat function to adjust the tilt angle of your blinds.'**
  String get slatSettingDescription;

  /// No description provided for @scenePositionSliderDescription.
  ///
  /// In en, this message translates to:
  /// **'The position defines how far the blinds are opened or closed.'**
  String get scenePositionSliderDescription;

  /// No description provided for @sceneSlatPositionSliderDescription.
  ///
  /// In en, this message translates to:
  /// **'The slat position defines the tilt angle of the roller shutter slats, if the tilt function is activated.'**
  String get sceneSlatPositionSliderDescription;

  /// No description provided for @referenceRun.
  ///
  /// In en, this message translates to:
  /// **'Calibration run'**
  String get referenceRun;

  /// No description provided for @slatAutoSettingHint.
  ///
  /// In en, this message translates to:
  /// **'In this mode, the position of the shades don\'t matter before the slats adjust adjust to the desired tilt position.'**
  String get slatAutoSettingHint;

  /// No description provided for @slatReversalSettingHint.
  ///
  /// In en, this message translates to:
  /// **'In this mode, the shades will fully close before the slats adjust to the desired tilt position.'**
  String get slatReversalSettingHint;

  /// No description provided for @release340Header.
  ///
  /// In en, this message translates to:
  /// **'The new ESB64NP-IPM flush-mounted matter shading actuator is here!'**
  String get release340Header;

  /// No description provided for @release340ESB64Header.
  ///
  /// In en, this message translates to:
  /// **'What is the ESB64NP-IPM capable of?'**
  String get release340ESB64Header;

  /// No description provided for @release340ESB64Note1.
  ///
  /// In en, this message translates to:
  /// **'Our Matter Gateway-certified shading actuator with optional slat function'**
  String get release340ESB64Note1;

  /// No description provided for @release340ESB64Note2.
  ///
  /// In en, this message translates to:
  /// **'Two wired button inputs for manual switching and forwarding to Matter'**
  String get release340ESB64Note2;

  /// No description provided for @release340ESB64Note3.
  ///
  /// In en, this message translates to:
  /// **'Expandable with EnOcean adapter (EOA64). E.g. with EnOcean wireless push button F4T55'**
  String get release340ESB64Note3;

  /// No description provided for @release340ESB64Note4.
  ///
  /// In en, this message translates to:
  /// **'Open for integrations thanks to REST API based on OpenAPI standard'**
  String get release340ESB64Note4;

  /// No description provided for @activateTiltModeDialogText.
  ///
  /// In en, this message translates to:
  /// **'If the tilt function is enabled, all settings will be lost. Are you sure you want to enable the tilt function?'**
  String get activateTiltModeDialogText;

  /// No description provided for @deactivateTiltModeDialogText.
  ///
  /// In en, this message translates to:
  /// **'If the tilt function is disabled, all settings will be lost. Are you sure you want to disable the tilt function?'**
  String get deactivateTiltModeDialogText;

  /// No description provided for @buttonSceneESBDescription.
  ///
  /// In en, this message translates to:
  /// **'The scene button sets the roller shutter to a specific position.'**
  String get buttonSceneESBDescription;

  /// No description provided for @sceneValueOverride.
  ///
  /// In en, this message translates to:
  /// **'Press and hold the button for 4 seconds to overwrite the position with the current value (starting value).'**
  String get sceneValueOverride;

  /// No description provided for @sceneCalibration.
  ///
  /// In en, this message translates to:
  /// **'The calibration run moves the roller shutter fully down and up once to determine the end positions and enable position detection.'**
  String get sceneCalibration;

  /// No description provided for @up.
  ///
  /// In en, this message translates to:
  /// **'Up'**
  String get up;

  /// No description provided for @down.
  ///
  /// In en, this message translates to:
  /// **'Down'**
  String get down;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
    'de',
    'en',
    'es',
    'fi',
    'fr',
    'it',
    'nl',
    'sv',
  ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+country codes are specified.
  switch (locale.languageCode) {
    case 'nl':
      {
        switch (locale.countryCode) {
          case 'BE':
            return AppLocalizationsNlBe();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fi':
      return AppLocalizationsFi();
    case 'fr':
      return AppLocalizationsFr();
    case 'it':
      return AppLocalizationsIt();
    case 'nl':
      return AppLocalizationsNl();
    case 'sv':
      return AppLocalizationsSv();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
