// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint => 'Activate Bluetooth on the device to connect';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count devices found',
      one: '1 device found',
      zero: 'No devices found',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demo devices',
      one: 'Demo device',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => '2-Channel Time Switch Bluetooth';

  @override
  String get discoveryImprint => 'Imprint';

  @override
  String get discoveryLegalnotice => 'Legal notice';

  @override
  String get generalSave => 'Save';

  @override
  String get generalCancel => 'Cancel';

  @override
  String get detailsHeaderHardwareversion => 'Hardware version';

  @override
  String get detailsHeaderSoftwareversion => 'Software version';

  @override
  String get detailsHeaderConnected => 'Connected';

  @override
  String get detailsHeaderDisconnected => 'Disconnected';

  @override
  String get detailsTimersectionHeader => 'Programs';

  @override
  String get detailsTimersectionTimercount => 'of 60 programs used';

  @override
  String get detailsConfigurationsectionHeader => 'Configuration';

  @override
  String get detailsConfigurationPin => 'Device PIN';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Channel 1: $channel1 | Channel 2: $channel2';
  }

  @override
  String get settingsCentralHeader => 'Central On/Off';

  @override
  String get detailsConfigurationCentralDescription =>
      'Only applies if the channel is set to AUTO';

  @override
  String get detailsConfigurationDevicedisplaylock => 'Lock device display';

  @override
  String get timerOverviewHeader => 'Programs';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inactive';

  @override
  String get timerDetailsListsectionDays1 => 'Monday';

  @override
  String get timerDetailsListsectionDays2 => 'Tuesday';

  @override
  String get timerDetailsListsectionDays3 => 'Wednesday';

  @override
  String get timerDetailsListsectionDays4 => 'Thursday';

  @override
  String get timerDetailsListsectionDays5 => 'Friday';

  @override
  String get timerDetailsListsectionDays6 => 'Saturday';

  @override
  String get timerDetailsListsectionDays7 => 'Sunday';

  @override
  String get timerDetailsHeader => 'Program';

  @override
  String get timerDetailsSunrise => 'Sunrise';

  @override
  String get generalToggleOff => 'Off';

  @override
  String get generalToggleOn => 'On';

  @override
  String get timerDetailsImpuls => 'Impulse';

  @override
  String get generalTextTime => 'Time';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Time Offset';

  @override
  String get timerDetailsPlausibility => 'Activate plausibility check';

  @override
  String get timerDetailsPlausibilityDescription =>
      'If the off time is set to an earlier time than the on time, both programs are ignored, e.g. switch on at sunrise and switch off at 6:00 am. There are also situations where the check is not wanted, e.g. switching on at sunset and switching off at 1:00 am.';

  @override
  String get generalDone => 'Done';

  @override
  String get generalDelete => 'Delete';

  @override
  String get timerDetailsImpulsDescription =>
      'Change global impulse configuration';

  @override
  String get settingsNameHeader => 'Device name';

  @override
  String get settingsNameDescription =>
      'This name is also displayed in Discovery.';

  @override
  String get settingsFactoryresetHeader => 'Factory settings';

  @override
  String get settingsFactoryresetDescription =>
      'Which content should be reset?';

  @override
  String get settingsFactoryresetResetbluetooth => 'Reset Bluetooth settings';

  @override
  String get settingsFactoryresetResettime => 'Reset time settings';

  @override
  String get settingsFactoryresetResetall => 'Set to factory settings';

  @override
  String get settingsDeletetimerHeader => 'Delete programs';

  @override
  String get settingsDeletetimerDescription =>
      'Do you really want to delete all timers?';

  @override
  String get settingsDeletetimerAllchannels => 'All channels';

  @override
  String get settingsImpulseHeader => 'Impulse Switch Time';

  @override
  String get settingsImpulseDescription =>
      'The impulse Switch Time sets the duration of the impulse.';

  @override
  String get generalTextRandommode => 'Random mode';

  @override
  String get settingsChannelsTimeoffsetHeader => 'Solar time offset';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Summer: $summerOffset min | Winter: $winterOffset min';
  }

  @override
  String get settingsLocationHeader => 'Location';

  @override
  String get settingsLocationDescription =>
      'Set your location to use astro functions.';

  @override
  String get settingsLanguageHeader => 'Device language';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Set language automatically';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Choose the language for the $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'German';

  @override
  String get settingsLanguageFrench => 'French';

  @override
  String get settingsLanguageEnglish => 'English';

  @override
  String get settingsLanguageItalian => 'Italian';

  @override
  String get settingsLanguageSpanish => 'Spanish';

  @override
  String get settingsDatetimeHeader => 'Date and time';

  @override
  String get settingsDatetimeSettimeautomatically => 'Apply system time';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Set timezone automatically';

  @override
  String get generalTextTimezone => 'Timezone';

  @override
  String get settingsDatetime24Hformat => '24 hour format';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Summer-/Wintertime automatically';

  @override
  String get settingsDatetimeWinter => 'Winter';

  @override
  String get settingsDatetimeSummer => 'Summer';

  @override
  String get settingsPasskeyHeader => 'Current device PIN';

  @override
  String get settingsPasskeyDescription => 'Enter the current device PIN';

  @override
  String get timerDetailsActiveprogram => 'Program active';

  @override
  String get timerDetailsActivedays => 'Active days';

  @override
  String get timerDetailsSuccessdialogHeader => 'Successful';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Program added successfully';

  @override
  String get settingsRandommodeDescription =>
      'The random mode only works with timer based programs but not with impulse based programs or astro based programs (sunrise / sunset).';

  @override
  String get settingsSolsticeHeader => 'Solar time offset';

  @override
  String get settingsSolsticeDescription =>
      'The set time defines the time-offset to solar time and it gets inverted respectively.';

  @override
  String get settingsSolsticeHint =>
      'For Example: \nIn winter the switch occurs 30 minutes before sunset in response the switch at sunrise also occurs 30 minutes in advance.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription =>
      'The PIN is required for the connection.';

  @override
  String get settingsPinHeader => 'New device PIN';

  @override
  String get settingsPinNewpinDescription => 'Enter a new PIN';

  @override
  String get settingsPinNewpinRepeat => 'Repeat the new PIN';

  @override
  String get detailsProductinfo => 'Product information';

  @override
  String get settingsDatetimeSettimeautodescription =>
      'Choose the preferred time';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minutes',
      one: 'Minute',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Hours',
      one: 'Hour',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Seconds',
      one: 'Second',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Channels',
      one: 'Channel',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Channel $number';
  }

  @override
  String get generalTextDate => 'Date';

  @override
  String get settingsDatetime24HformatDescription =>
      'Choose the preferred format';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Summer-/Winter time';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => 'AM/PM';

  @override
  String get detailsEdittimer => 'Edit programs';

  @override
  String get settingsPinOldpinRepeat => 'Please repeat the current PIN';

  @override
  String get settingsPinCheckpin => 'Checking PIN';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Devices',
      one: 'Device',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Disconnect';

  @override
  String get settingsCentralDescription =>
      'The input A1 controls the Central On/Off.\nCentral On/Off only applies if the channel is set to Central On/Off.';

  @override
  String get settingsCentralHint =>
      'Example:\nChannel 1 = Central On/Off\nChannel 2 = Off\nA1 = Central On -> Only C1 switches to On, C2 stays Off';

  @override
  String get settingsCentralToggleheader => 'Central Input switches';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Current channels with the setting Central On/Off:';

  @override
  String get settingsSolsticeSign => 'Sign';

  @override
  String get settingsDatetimeTimezoneDescription => 'Central European Time';

  @override
  String get generalButtonContinue => 'Continue';

  @override
  String get settingsPinConfirmationDescription => 'PIN change successful';

  @override
  String get settingsPinFailDescription => 'PIN change failed';

  @override
  String get settingsPinFailHeader => 'Failure';

  @override
  String get settingsPinFailShort => 'The PIN has to be exactly 6 digits long';

  @override
  String get settingsPinFailWrong => 'The current PIN is incorrect';

  @override
  String get settingsPinFailMatch => 'The PINs do not match';

  @override
  String get discoveryLostconnectionHeader => 'Connection lost';

  @override
  String get discoveryLostconnectionDescription =>
      'The device has been disconnected.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Behaves like AUTO and also listens to the wired central inputs';

  @override
  String get settingsChannelConfigOnDescription =>
      'Switches the channel permanently to ON and ignores the programs';

  @override
  String get settingsChannelConfigOffDescription =>
      'Switches the channel permanently to OFF and ignores the programs';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Switches in relation to the time and astro programs';

  @override
  String get bluetoothPermissionDescription =>
      'Bluetooth is required for the configuration of the devices.';

  @override
  String get timerListitemOn => 'Turn on';

  @override
  String get timerListitemOff => 'Turn off';

  @override
  String get timerListitemUnknown => 'Unknown';

  @override
  String get timerDetailsAstroHint =>
      'The location must be set in the settings for the astro programs to work correctly.';

  @override
  String get timerDetailsTrigger => 'Trigger';

  @override
  String get timerDetailsSunset => 'Sunset';

  @override
  String get settingsLocationCoordinates => 'Coordinates';

  @override
  String get settingsLocationLatitude => 'Latitude';

  @override
  String get settingsLocationLongitude => 'Longitude';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'No programs are currently used for $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Programs are loaded';

  @override
  String get timerOverviewProgramchanged => 'Program was changed';

  @override
  String get settingsDatetimeProcessing => 'Date and time is changed';

  @override
  String get deviceNameEmpty => 'Input must not be empty';

  @override
  String deviceNameHint(Object count) {
    return 'The input must not contain more than $count characters.';
  }

  @override
  String get deviceNameChanged => 'Device name is changed';

  @override
  String get deviceNameChangedSuccessfully =>
      'Device name was successfully changed';

  @override
  String get deviceNameChangedFailed => 'An error has occurred.';

  @override
  String get settingsPinConfirm => 'Confirm';

  @override
  String get deviceShowInstructions =>
      '1. Activate the Bluetooth of the watch with SET\n2. Tap the button at the top to start the search.';

  @override
  String get deviceNameNew => 'Enter new device name';

  @override
  String get settingsLanguageRetrieved => 'Language is retrieved';

  @override
  String get detailsProgramsShow => 'Show programs';

  @override
  String get generalTextProcessing => 'Please wait';

  @override
  String get generalTextRetrieving => 'are retrieved';

  @override
  String get settingsLocationPermission =>
      'Allow ELTAKO Connect to access this device\'s location';

  @override
  String get timerOverviewChannelloaded => 'Channels are loaded';

  @override
  String get generalTextRandommodeChanged => 'Random mode is changed';

  @override
  String get detailsConfigurationsectionChanged => 'Configuration is changed';

  @override
  String get settingsSettimeFunctions => 'Time functions are changed';

  @override
  String get imprintContact => 'Contact';

  @override
  String get imprintPhone => 'Phone';

  @override
  String get imprintMail => 'Mail';

  @override
  String get imprintRegistrycourt => 'Register court';

  @override
  String get imprintRegistrynumber => 'Registration number';

  @override
  String get imprintCeo => 'Managing Director';

  @override
  String get imprintTaxnumber => 'Sales tax identification number';

  @override
  String get settingsLocationCurrent => 'Current location';

  @override
  String get generalTextReset => 'Reset';

  @override
  String get discoverySearchStart => 'Start search';

  @override
  String get discoverySearchStop => 'Stop search';

  @override
  String get settingsImpulsSaved => 'Impulse Switch Time is stored';

  @override
  String get settingsCentralNochannel =>
      'There are no channels with the Central On/Off setting';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'Bluetooth connection was successfully reset.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Bluetooth connections reset failed.';

  @override
  String get imprintPublisher => 'Publisher';

  @override
  String get discoveryDeviceConnecting => 'Connection is established';

  @override
  String get discoveryDeviceRestarting => 'Restarting...';

  @override
  String get generalTextConfigurationsaved => 'Channel configuration saved.';

  @override
  String get timerOverviewChannelssaved => 'Save channels';

  @override
  String get timerOverviewSaved => 'Timer saved';

  @override
  String get timerSectionList => 'List view';

  @override
  String get timerSectionDayview => 'Day view';

  @override
  String get generalTextChannelInstructions => 'Channel settings';

  @override
  String get generalTextPublisher => 'Publisher';

  @override
  String get settingsDeletetimerDialog =>
      'Do you really want to delete all programmes?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Do you really want to reset all Bluetooth settings?';

  @override
  String get settingsCentralTogglecentral => 'Central\nOn/Off';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName change successful.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName change failed.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Channels were successfully changed.';

  @override
  String get timerDetailsSaveHeader => 'Save program';

  @override
  String get timerDetailsDeleteHeader => 'Delete program';

  @override
  String get timerDetailsSaveDescription => 'Saving program successful.';

  @override
  String get timerDetailsDeleteDescription => 'Deleting program successful.';

  @override
  String get timerDetailsAlertweekdays =>
      'The program can\'t be saved, because no weekdays are selected.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'Date and time were successfully changed.';

  @override
  String get discoveryConnectionFailed => 'Connection failed';

  @override
  String get discoveryDeviceResetrequired =>
      'No connection could be established with the device. To solve this problem, delete the device from your Bluetooth settings. If the problem persists, please contact our technical support.';

  @override
  String get generalTextSearch => 'Search devices';

  @override
  String get generalTextOr => 'or';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'All programs were successfully deleted.';

  @override
  String get generalTextManualentry => 'Manual entry';

  @override
  String get settingsLocationSaved => 'Location saved';

  @override
  String get settingsLocationAutosearch => 'Search location automatically';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Do you really want to reset the device to factory settings?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'The device has been successfully reset to factory settings.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Device reset failed.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => '2-stage response delay (A2)';

  @override
  String get mfzFunctionA2TitleShort => '2-stage response delay (A2)';

  @override
  String get mfzFunctionA2Description =>
      'When the control voltage is applied, time lapse T1 between 0 and 60 seconds begins. At the end of this period, contact 1-2 closes and the time lapse t2 between 0 and 60 seconds begins. At the end of this time, contact 3-4 closes. After an interruption, the time sequence starts again with t1.';

  @override
  String get mfzFunctionRvTitle => 'Off Delay (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Off Delay';

  @override
  String get mfzFunctionRvDescription =>
      'When the control voltage is applied the relay contact switches to 15-18. When the control voltage is interrupted the timing period is started; on time-out the relay contact returns to normal position. Resettable during the timing period.';

  @override
  String get mfzFunctionTiTitle => 'Clock generator starting with impulse (TI)';

  @override
  String get mfzFunctionTiTitleShort =>
      'TI | clock generator starting with impulse';

  @override
  String get mfzFunctionTiDescription =>
      'As long as the control voltage is applied, the operating contact closes and opens. The change-over time in both directions can be set separately. When the control voltage is applied, the operating contact changes immediately to 15-18.';

  @override
  String get mfzFunctionAvTitle => 'Operate delay (AV)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | operate delay';

  @override
  String get mfzFunctionAvDescription =>
      'When the control voltage is applied the timing period is started; on time-out the relay contact changes to 15-18. After an interruption, the timing period is restarted.';

  @override
  String get mfzFunctionAvPlusTitle => 'Operate delay additive (AV+)';

  @override
  String get mfzFunctionAvPlusTitleShort => 'AV+ | operate delay additive';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Function same as AV. However, after an interruption the elapsed time is stored.';

  @override
  String get mfzFunctionAwTitle => 'Fleeting NC contact (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | fleeting NC contact';

  @override
  String get mfzFunctionAwDescription =>
      'When the control voltage is interrupted the NO contact changes to 15-18, and reverts on wiping time-out. If the control voltage is applied during the wiping time the NO contact immediately reverts to 15-16 and the residual time is cancelled.';

  @override
  String get mfzFunctionIfTitle => 'Pulse shaper (IF)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | pulse shaper';

  @override
  String get mfzFunctionIfDescription =>
      'When the control voltage is applied the relay contact changes to 15-18 for the set time. Further control impulses are evaluated only after the set time has elapsed.';

  @override
  String get mfzFunctionEwTitle => 'Fleeting NO contact (EW)';

  @override
  String get mfzFunctionEwTitleShort => 'EW | fleeting NO contact';

  @override
  String get mfzFunctionEwDescription =>
      'When the control voltage is applied the NO contact changes to 15-18 and reverts on wiping time-out. If the control voltage is removed during the wiping time the NO contact immediately reverts to 15-16 and the residual time is cancelled.';

  @override
  String get mfzFunctionEawTitle =>
      'Fleeting NO contact and fleeting NC contact (EAW)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | fleeting NO contact and fleeting NC contact';

  @override
  String get mfzFunctionEawDescription =>
      'When the control voltage is applied or interrupted the relay contact changes to 15-18 and reverts after the set wiping time.';

  @override
  String get mfzFunctionTpTitle => 'Clock generator starting with pause (TP)';

  @override
  String get mfzFunctionTpTitleShort =>
      'TP | clock generator starting with pause';

  @override
  String get mfzFunctionTpDescription =>
      'Description of function same as for TI, except that, when the control voltage is applied, the contact initially remains at 15-16 rather than changing to 15-18.';

  @override
  String get mfzFunctionIaTitle =>
      'Impulse controlled pickup delay (e.g. automatic door opener) (IA)';

  @override
  String get mfzFunctionIaTitleShort => 'IA | impulse controlled pickup delay';

  @override
  String get mfzFunctionIaDescription =>
      'The timing period t1 starts with a control impulse from 50ms; on time-out the relay contact changes for the timing period t2 to 15-18 for 1 second (e.g. for automatic door opener). If t1 is set to t1 min = 0.1 seconds, the IA operates as pulse shaper, when timing period t2 elapses, independent of the duration of the control impulse (min. 150 ms).';

  @override
  String get mfzFunctionArvTitle => 'Operate and release delay (ARV)';

  @override
  String get mfzFunctionArvTitleShort => 'ARV | operate and release delay';

  @override
  String get mfzFunctionArvDescription =>
      'When the control voltage is applied, the timeout begins, at the end of which the operating contact changes to 15 -18. If the control voltage is then interrupted, a new timeout begins, at the end of which the operating contact returns to the rest position.\nAfter an interruption of the response delay, the timeout starts again.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Operate and release delay additive (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | operate and release delay additive';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Same function as ARV, but after an interruption of the operate delay the elapsed time is stored.';

  @override
  String get mfzFunctionEsTitle => 'Impulse switch (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | impulse switch';

  @override
  String get mfzFunctionEsDescription =>
      'With control impulses from 50ms the make contact switches to and from.';

  @override
  String get mfzFunctionEsvTitle =>
      'Impulse switch with release delay and switch-off early-warning function (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | impulse switch with release delay and switch-off early-warning function';

  @override
  String get mfzFunctionEsvDescription =>
      'Function same as SRV. Additionally with switch-off early warning: approx. 30 sec. before time-out the lighting starts flickering 3 times at gradually shorter time intervals.';

  @override
  String get mfzFunctionErTitle => 'Relay (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | relay';

  @override
  String get mfzFunctionErDescription =>
      'As long as the control contact is closed the make contact reverts from 15-16 to 15-18.';

  @override
  String get mfzFunctionSrvTitle => 'Release-delay impulse switch (SRV)';

  @override
  String get mfzFunctionSrvTitleShort => 'SRV | release-delay impulse switch';

  @override
  String get mfzFunctionSrvDescription =>
      'With control impulses from 50ms the make contact switches to and fro. In the contact position 15-18, the device switches automatically to the rest position 15-16 on delay time-out.';

  @override
  String get detailsFunctionsHeader => 'Functions';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Time (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'permanent ON';

  @override
  String get mfzFunctionOffDescription => 'permanent OFF';

  @override
  String get mfzFunctionMultiplier => 'Factor';

  @override
  String get discoveryMfz12Description => 'Multifunction time relay Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'permanent ON';

  @override
  String get mfzFunctionOnTitleShort => 'permanent ON';

  @override
  String get mfzFunctionOffTitle => 'permanent OFF';

  @override
  String get mfzFunctionOffTitleShort => 'permanent OFF';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 seconds';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minutes';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 hours';

  @override
  String get mfzOverviewFunctionsloaded => 'Functions are loaded';

  @override
  String get mfzOverviewSaved => 'Function saved';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Bluetooth setting has been successfully changed.';

  @override
  String get settingsBluetoothInformation =>
      'Note: If this setting is activated, the device is permanently visible to everyone via Bluetooth!\nIt is recommended to change the device PIN.';

  @override
  String get settingsBluetoothContinuousconnection => 'Permanent visibility';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'By enabling persistent visibility, Bluetooth remains active on the device ($deviceType) and does not need to be manually activated before establishing a connection.';
  }

  @override
  String get settingsBluetoothTimeout => 'Connection timeout';

  @override
  String get settingsBluetoothPinlimit => 'PIN limit';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'The connection is disconnected after $timeout minutes of inactivity.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'For security reasons, you have a maximum of $attempts attempts for entering the PIN. Bluetooth is then deactivated and must be manually reactivated for a new connection.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'attempts';

  @override
  String get settingsResetfunctionHeader => 'Reset functions';

  @override
  String get settingsResetfunctionDialog =>
      'Do you really want to reset all functions?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'All functions have been successfully reset.';

  @override
  String get mfzFunctionTime => 'Time (t)';

  @override
  String get discoveryConnectionFailedInfo => 'No Bluetooth connection';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Immediately after locking the device display, Bluetooth gets deactivated and has to be reactivated manually in order to establish a new connection.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Are you sure to lock the device display?';

  @override
  String get settingsDemodevices => 'Show demo devices';

  @override
  String get generalTextSettings => 'Settings';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Information';

  @override
  String get detailsConfigurationDimmingbehavior => 'Dimming behaviour';

  @override
  String get detailsConfigurationSwitchbehavior => 'Switch behaviour';

  @override
  String get detailsConfigurationBrightness => 'Brightness';

  @override
  String get detailsConfigurationMinimum => 'Minimum';

  @override
  String get detailsConfigurationMaximum => 'Maximum';

  @override
  String get detailsConfigurationSwitchesGr => 'Group relay (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Group switch (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer =>
      'Normally opened contact (NO/ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Off -> Pushbutton pressed (On) -> Release (Off)';

  @override
  String get detailsConfigurationSwitchesOpenerer =>
      'Normally closed contact (NC/ER-Invers)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'On -> Pushbutton pressed (Off) -> Release (On)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Switch';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'With each switch change, the light is switched on and off';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Impulse switch';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'Pushbutton is briefly pressed and released to turn the light on or off';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'As long as the pushbutton is pressed, the motor is running.';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Pushbutton is briefly pressed to start the motor and briefly pressed to stop it again';

  @override
  String get detailsConfigurationWifiloginScan => 'Scan QR-Code';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Scanned code is not valid';

  @override
  String get detailsConfigurationWifiloginDescription => 'Enter code';

  @override
  String get detailsConfigurationWifiloginPassword => 'Password';

  @override
  String get discoveryEsbipDescription => 'Shutter and blind actuator IP';

  @override
  String get discoveryEsripDescription => 'Impulse switching relay IP';

  @override
  String get discoveryEudipDescription => 'Universal dimmer switch IP';

  @override
  String get generalTextLoad => 'Loading';

  @override
  String get wifiBasicautomationsNotFound => 'No automation found.';

  @override
  String get wifiCodeInvalid => 'Invalid code';

  @override
  String get wifiCodeValid => 'Valid code';

  @override
  String get wifiAuthorizationLogin => 'Connect';

  @override
  String get wifiAuthorizationLoginFailed => 'Log in failed';

  @override
  String get wifiAuthorizationSerialnumber => 'Serial number';

  @override
  String get wifiAuthorizationProductiondate => 'Production date';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'WiFi password';

  @override
  String get generalTextUsername => 'Username';

  @override
  String get generalTextEnter => 'OR ENTER MANUALLY';

  @override
  String get wifiAuthorizationScan => 'Scan the ELTAKO code.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'This device currently does not support any other settings';

  @override
  String get settingsUsedemodelay => 'Use demo delay';

  @override
  String get settingsImpulsLoad => 'Impulse switching time is loaded';

  @override
  String get settingsBluetoothLoad => 'Bluetooth setting is being loaded.';

  @override
  String get detailsConfigurationsectionLoad => 'Configurations are loaded';

  @override
  String get generalTextLogin => 'Log in';

  @override
  String get generalTextAuthentication => 'Authenticate';

  @override
  String get wifiAuthorizationScanDescription =>
      'Look for the ELTAKO code on the WiFi device or on the included info sheet and align it in the camera frame at the top.';

  @override
  String get wifiAuthorizationScanShort => 'Scan ELTAKO code';

  @override
  String get detailsConfigurationEdgemode => 'Dimming curve';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Leading edge';

  @override
  String get generalTextNetwork => 'Network';

  @override
  String get wifiAuthenticationSuccessful => 'Authentication successful';

  @override
  String get detailsConfigurationsectionSavechange => 'Configuration Changed';

  @override
  String get discoveryWifiAdddevice => 'Add WiFi device';

  @override
  String get wifiAuthenticationDelay => 'This can last up to 1 minute';

  @override
  String get generalTextRetry => 'Retry';

  @override
  String get wifiAuthenticationCredentials =>
      'Please enter the password of your WiFi';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'It can last up to 1 minute until the device is ready and appears in the app';

  @override
  String get wifiAuthenticationCredentialsShort => 'Enter WiFi password';

  @override
  String get wifiAuthenticationTeachin => 'Teach in the device into the WiFi';

  @override
  String get wifiAuthenticationEstablish =>
      'Establish connection to the device';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Device connects to WiFi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Connection failed. Disconnect the device from power for a few seconds and retry to connect it';

  @override
  String get wifiAuthenticationReset => 'Reset authentication';

  @override
  String get wifiAuthenticationResetHint =>
      'All authentication data will be deleted.';

  @override
  String get wifiAuthenticationInvaliddata => 'Authentication data invalid';

  @override
  String get wifiAuthenticationReauthenticate => 'Authenticate again';

  @override
  String get wifiAddhkdeviceHeader => 'Add device';

  @override
  String get wifiAddhkdeviceDescription =>
      'Connect your new ELTAKO device to your WiFi via the Apple Home app.';

  @override
  String get wifiAddhkdeviceStep1 => '1. Open the Apple Home app.';

  @override
  String get wifiAddhkdeviceStep2 =>
      '2. Click on the plus in the top right corner of the app and select **Add Device**.';

  @override
  String get wifiAddhkdeviceStep3 => '3. Follow the instructions of the app.';

  @override
  String get wifiAddhkdeviceStep4 =>
      '4. Now your device can be configured in the ELTAKO Connect app.';

  @override
  String get detailsConfigurationRuntime => 'Runtime';

  @override
  String get detailsConfigurationRuntimeMode => 'Mode';

  @override
  String get generalTextManually => 'Manually';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'The shading actuator independently determines the runtime of the shading motor during each movement from the lower to the upper end position (recommended).\nAfter initial startup or changes, such a movement should be performed from the bottom to the top without interruption.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'The runtime of the shading motor is set manually via the duration below.\nPlease make sure that the configured runtime matches the actual runtime of your shading motor. \nAfter initial startup or changes, such a movement should be performed from the bottom to the top without interruption.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'LCD mode is only available via REST API';

  @override
  String get generalTextDemomodeActive => 'Demo mode active';

  @override
  String get detailsConfigurationRuntimeDuration => 'Duration';

  @override
  String get detailsConfigurationSwitchesGs4 => 'Group switch (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Group switch with jog reversing function for controlling blinds';

  @override
  String get screenshotSu12 => 'Outdoor Light';

  @override
  String get screenshotS2U12 => 'Outdoor Light';

  @override
  String get screenshotMfz12 => 'Pump';

  @override
  String get screenshotEsr62 => 'Lamp';

  @override
  String get screenshotEud62 => 'Ceiling light';

  @override
  String get screenshotEsb62 => 'Shutters balcony';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 are comfort positions with different dimming curves for dimmable 230 V LED lamps, which cannot be dimmed far enough on AUTO due to their design and must therefore be forced to phase angle control.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO allows dimming of all types of lamps.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Trailing edge';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 are comfort positions with different dimming curves for dimmable 230 V LED lamps.';

  @override
  String get updateHeader => 'Firmware Update';

  @override
  String get updateTitleStepSearch => 'Searching for an update';

  @override
  String get updateTitleStepFound => 'An update was found';

  @override
  String get updateTitleStepDownload => 'Downloading update';

  @override
  String get updateTitleStepInstall => 'Installing update';

  @override
  String get updateTitleStepSuccess => 'Update successful';

  @override
  String get updateTitleStepUptodate => 'Already up to date';

  @override
  String get updateTitleStepFailed => 'Update failed';

  @override
  String get updateButtonSearch => 'Search for updates';

  @override
  String get updateButtonInstall => 'Install update';

  @override
  String get updateCurrentversion => 'Current version';

  @override
  String get updateNewversion => 'New firmware update available';

  @override
  String get updateHintPower =>
      'The update only starts when the output of the device is not active. The device should not be disconnected from the power supply and the app should not be exited during the update!';

  @override
  String get updateButton => 'Update';

  @override
  String get updateHintCompatibility =>
      'An update is recommended, otherwise some functions in the app will be limited.';

  @override
  String get generalTextDetails => 'Details';

  @override
  String get updateMessageStepMetadata => 'Loading update information';

  @override
  String get updateMessageStepPrepare => 'Update is being prepared';

  @override
  String get updateTitleStepUpdatesuccessful => 'Update is being checked';

  @override
  String get updateTextStepFailed =>
      'Unfortunately something went wrong during the update, try again in a few minutes or wait until your device updates automatically (internet connection required).';

  @override
  String get configurationsNotavailable =>
      'There are no configurations available yet';

  @override
  String get configurationsAddHint =>
      'Create new configurations by connecting to a device and saving a configuration.';

  @override
  String get configurationsEdit => 'Edit configuration';

  @override
  String get generalTextName => 'Name';

  @override
  String get configurationsDelete => 'Delete configuration';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'Should the configuration: $configName really be deleted?';
  }

  @override
  String get configurationsSave => 'Save configuration';

  @override
  String get configurationsSaveHint =>
      'Export the current configuration of your Device, or load one of your existing configurations.';

  @override
  String get configurationsImport => 'Import configuration';

  @override
  String configurationsImportHint(Object configName) {
    return 'Should the configuration $configName really be transferred?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Configurations',
      one: 'Configuration',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Configuration is being prepared';

  @override
  String get configurationsStepName => 'Enter a name for the configuration';

  @override
  String get configurationsStepSaving => 'Configuration is saving';

  @override
  String get configurationsStepSavedsuccessfully =>
      'Configuration was saved successfully';

  @override
  String get configurationsStepSavingfailed =>
      'Saving the configuration failed';

  @override
  String get configurationsStepChoose => 'Select a configuration';

  @override
  String get configurationsStepImporting => 'Configuration is importing';

  @override
  String get configurationsStepImportedsuccessfully =>
      'Configuration was imported successfully';

  @override
  String get configurationsStepImportingfailed =>
      'Importing configuration failed';

  @override
  String get discoveryAssuDescription => 'Outdoor Socket Time Switch Bluetooth';

  @override
  String get settingsDatetimeDevicetime => 'Actual device time';

  @override
  String get settingsDatetimeLoading => 'Time settings are loaded';

  @override
  String get discoveryEud12Description => 'Universal dimmer switch Bluetooth';

  @override
  String get generalTextOffdelay => 'Off Delay';

  @override
  String get generalTextRemainingbrightness => 'Remaining brightness';

  @override
  String get generalTextSwitchonvalue => 'Switch on value';

  @override
  String get motionsensorTitleNoremainingbrightness => 'No residual brightness';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'With residual brightness';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Residual brightness via switching program';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Residual brightness via ZE and ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'No residual brightness (semi-automatic)';

  @override
  String get generalTextMotionsensor => 'Motion detector';

  @override
  String get generalTextLightclock => 'Light alarm clock';

  @override
  String get generalTextSnoozeclock => 'Snooze function';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'When switching on ($mode), the light is switched on after approx. 1 second at the lowest brightness and slowly dimmed up without changing the last saved brightness level.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'When switching off ($mode), the lighting is dimmed down from the current dimming position to the minimum brightness and switched off. The lighting can be switched off at any time during the dimming down process by pressing the button briefly. A long press during the dimming process dims up and ends the snooze function.';
  }

  @override
  String get generalTextImmediately => 'Immediately';

  @override
  String get generalTextPercentage => 'Percentage';

  @override
  String get generalTextSwitchoffprewarning => 'Switch-off prewarning';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Slow dimming to minimum brightness';

  @override
  String get generalDescriptionOffdelay =>
      'The device switches on when the control voltage is applied. If the control voltage is interrupted, the time lapse begins, after which the device switches off. The appliance can be switched on downstream during the time lapse.';

  @override
  String get generalDescriptionBrightness =>
      'The brightness at which the lamp is switched on by the dimmer.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'The dimming value in per cent to which the lamp is dimmed after the motion detector is switched off.';

  @override
  String get generalDescriptionRuntime =>
      'Running time of the light alarm function from minimum brightness to maximum brightness.';

  @override
  String get generalTextUniversalbutton => 'Universal push-button';

  @override
  String get generalTextDirectionalbutton => 'Direction button';

  @override
  String get eud12DescriptionAuto =>
      'Automatic detection UT/RT (with directional sensor diode RTD)';

  @override
  String get eud12DescriptionRt => 'with directional sensing diode RTD';

  @override
  String get generalTextProgram => 'Program';

  @override
  String get eud12MotionsensorOff => 'With motion detector set to Off';

  @override
  String get eud12ClockmodeTitleProgramze => 'Program and Central On';

  @override
  String get eud12ClockmodeTitleProgramza => 'Program and Central Off';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Program and UT/RT On';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Program and UT/RT Off';

  @override
  String get eud12TiImpulseTitle => 'Pulse time On (t1)';

  @override
  String get eud12TiImpulseHeader => 'Dimming value Pulse time On';

  @override
  String get eud12TiImpulseDescription =>
      'The dimming value as a percentage\n to which the lamp is dimmed at pulse time ON.';

  @override
  String get eud12TiOffTitle => 'Pulse time Off (t2)';

  @override
  String get eud12TiOffHeader => 'Dimming value Pulse time Off';

  @override
  String get eud12TiOffDescription =>
      'The dimming value is a percentage to which the lamp is dimmed at pulse time OFF.';

  @override
  String get generalTextButtonpermanentlight => 'Permanent push-button light';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Setting the push-button continuous light from 0 to 10 hours in 0.5 hour increments. Activation by pressing the button for longer than 1 second (1x flickering), deactivation by pressing the button for longer than 2 seconds.';

  @override
  String get generalTextNobuttonpermanentlight => 'No TSP';

  @override
  String get generalTextBasicsettings => 'Basic settings';

  @override
  String get generalTextInputswitch => 'Local button input (A1)';

  @override
  String get generalTextOperationmode => 'Operating mode';

  @override
  String get generalTextDimvalue => 'Power on behaviour';

  @override
  String get eud12TitleUsememory => 'Use memory value';

  @override
  String get eud12DescriptionUsememory =>
      'The memory value corresponds to the last dimming value set. If the memory value is deactivated, dimming is always set to the switch-on value.';

  @override
  String get generalTextStartup => 'Switch-on brightness';

  @override
  String get generalDescriptionSwitchonvalue =>
      'The switch-on value is an adjustable brightness value that guarantees safe switch-on.';

  @override
  String get generalTitleSwitchontime => 'Switch-on time';

  @override
  String get generalDescriptionSwitchontime =>
      'After the switch-on time has elapsed, the lamp is dimmed from the switch-on value to the memory value.';

  @override
  String get generalDescriptionStartup =>
      'Some LED lamps require a higher inrush current to switch on reliably. The lamp is switched on at this switch-on value and then dimmed to the memory value after the switch-on time.';

  @override
  String get eud12ClockmodeSubtitleProgramze => 'Short click on Central On';

  @override
  String get eud12ClockmodeSubtitleProgramza => 'Short click on central off';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Double-click on universal button/direction button On';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Double-click on universal button/direction button Off';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Staircase lighting timer';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Timer with adjustable switch-on and switch-off time from 0.5 seconds to 9.9 minutes. The brightness can be set from minimum brightness to maximum brightness.';

  @override
  String get eud12FunctionAutoDescription =>
      'Universal dimmer switch with setting for motion detector, light alarm and snooze function';

  @override
  String get eud12FunctionErDescription =>
      'Switching relay, the brightness can be set from minimum brightness to maximum brightness.';

  @override
  String get eud12FunctionEsvDescription =>
      'Universal dimmer switch with setting of a switch-off delay from 1 to 120 minutes. Switch-off pre-warning at the end by dimming down selectable and adjustable from 1 to 3 minutes. Both central inputs active.';

  @override
  String get eud12FunctionTlzDescription =>
      'Setting the button light duration from 0 to 10 hours in 0.5 hour increments. Activation by pressing the button for longer than 1 second (1x flickering), deactivation by pressing the button for longer than 2 seconds.';

  @override
  String get eud12FunctionMinDescription =>
      'Universal dimmer switch, switches to the set minimum brightness when the control voltage is applied. The light is dimmed to maximum brightness within the set dimming time of 1 to 120 minutes. When the control voltage is removed, the light is switched off immediately, even during the dimming time. Both central inputs active.';

  @override
  String get eud12FunctionMmxDescription =>
      'Universal dimmer switch, switches to the set minimum brightness when the control voltage is applied. During the set dimming time of 1 to 120 minutes, the light is dimmed to the maximum brightness. However, when the control voltage is removed, the dimmer dims down to the set minimum brightness. It is then switched off. Both central inputs active.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'With motion detector set to Off';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'With motion detector set to Off';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Switching program activated and deactivated with BWM off';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Central On activates motion sensor, Central Off deactivates motion sensor, as well as by switching program';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Motion detector only switches off';

  @override
  String get detailsDimsectionHeader => 'Dimming';

  @override
  String get generalTextFast => 'Fast';

  @override
  String get generalTextSlow => 'Slowly';

  @override
  String get eud12TextDimspeed => 'Dimming speed';

  @override
  String get eud12TextSwitchonspeed => 'Switch-on speed';

  @override
  String get eud12TextSwitchoffspeed => 'Switch-off speed';

  @override
  String get eud12DescriptionDimspeed =>
      'The dimming speed is the speed at which the dimmer dims from the current brightness to the target brightness.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'The switch-on speed is the speed that the dimmer requires to switch on completely.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'The switch-off speed is the speed that the dimmer requires to switch off completely.';

  @override
  String get settingsFactoryresetResetdimHeader => 'Reset dimming settings';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Should all dimming settings really be reset?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Dimming settings have been successfully reset';

  @override
  String get eud12TextSwitchonoffspeed => 'On/off speed';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'The switch-on/switch-off speed is the speed that the dimmer requires to switch on or off completely.';

  @override
  String get timerDetailsDimtoval => 'On with dimming value in %';

  @override
  String get timerDetailsDimtovalDescription =>
      'The dimmer always switches on with the fixed dimming value in %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Switch on with $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'On with memory value';

  @override
  String get timerDetailsDimtomemSubtitle => 'Switching on with memory value';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Residual brightness (BWM) On';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Residual brightness (BWM) Off';

  @override
  String get settingsRandommodeHint =>
      'With random mode activated all programs of this channel are randomly offset by up to 15 minutes. On-timers are offset in advance, Off-timers are delayed.';

  @override
  String get runtimeOffsetDescription =>
      'Additional overrun, after the journey time has elapsed';

  @override
  String get loadingTextDimvalue => 'Dimming value is loaded';

  @override
  String get discoveryEudipmDescription => 'Universal dimmer switch IP Matter';

  @override
  String get generalTextOffset => 'Overrun';

  @override
  String get eud12DimvalueTestText => 'Send brightness';

  @override
  String get eud12DimvalueTestDescription =>
      'The currently set dim speed is taken into account during testing.';

  @override
  String get eud12DimvalueLoadText => 'Load brightness';

  @override
  String get settingsDatetimeNotime =>
      'The date and time settings must be read out via the device display.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Please teach in your Matter device using one of the following apps';

  @override
  String get generalMatterOpengooglehome => 'Open Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Open Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Open SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Program $number';
  }

  @override
  String get generalTextDone => 'Done';

  @override
  String get settingsRandommodeDescriptionShort =>
      'With random mode activated all programs of this channel are randomly offset by up to 15 minutes. On-timers are offset in advance, Off-timers are delayed.';

  @override
  String get all => 'All';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get timeProgramAdd => 'Add time program';

  @override
  String get noConnection => 'No connection';

  @override
  String get timeProgramOnlyActive => 'Configured programs';

  @override
  String get timeProgramAll => 'All programs';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String timeProgramSaved(Object number) {
    return 'Time program $number saved';
  }

  @override
  String get deviceLanguageSaved => 'Device language saved';

  @override
  String generalTextTimeShort(Object time) {
    return '$time';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Should program $index really be deleted?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Milliseconds',
      one: 'Millisecond',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Milliseconds',
      one: '$count Millisecond',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Seconds',
      one: '$count Second',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Minutes',
      one: '$count Minute',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Hours',
      one: '$count Hour',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'The PIN must not be empty';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'Scanned code does not match the device';

  @override
  String get wifiAuthorizationPopIsEmpty => 'PoP cannot be empty';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'As the app cannot access your private Wi-Fi password, it is not possible to check the correctness of the entry. If no connection is established, check the password and enter it again.';

  @override
  String get generalMatterOpenApplehome => 'Open Apple Home';

  @override
  String get timeProgramNoActive => 'No configured programs';

  @override
  String get timeProgramNoEmpty => 'No free time program available';

  @override
  String get nameOfConfiguration => 'Configuration name';

  @override
  String get currentDevice => 'Current device';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get savedConfigurations => 'Saved configurations';

  @override
  String get importableServicesLabel =>
      'The following settings can be imported:';

  @override
  String get notImportableServicesLabel => 'Incompatible settings';

  @override
  String get deviceCategoryMeterGateway => 'Meter Gateway';

  @override
  String get deviceCategory2ChannelTimeSwitch => '2-channel time switch';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Outdoor time switch Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Adjust the baud rate, parity, and timeout to configure the transmission speed, error detection and waiting time.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Baudrate';

  @override
  String get settingsModbusParity => 'Parity';

  @override
  String get settingsModbusTimeout => 'Modbus Timeout';

  @override
  String get locationServiceDisabled => 'Location is disabled';

  @override
  String get locationPermissionDenied =>
      'Please allow the location permission to request your current position.';

  @override
  String get locationPermissionDeniedPermanently =>
      'Location permissions are permanently denied, please allow the location permission in your device settings to request your current position.';

  @override
  String get lastSync => 'Last sync';

  @override
  String get dhcpActive => 'DHCP active';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Subnet mask';

  @override
  String get standardGateway => 'Default gateway';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'Alternate DNS';

  @override
  String get errorNoNetworksFound => 'No wifi network found';

  @override
  String get availableNetworks => 'Available networks';

  @override
  String get enableWifiInterface => 'Enable WiFi interface';

  @override
  String get enableLANInterface => 'Enable LAN interface';

  @override
  String get hintDontDisableAllInterfaces =>
      'Make sure that not all interfaces are disabled. The last activated interface has priority.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'Search networks';

  @override
  String get errorNoNetworkEnabled => 'At least one station must be active';

  @override
  String get errorActiveNetworkInvalid => 'Not all active stations are valid';

  @override
  String get invalidNetworkConfiguration => 'Invalid network configuration';

  @override
  String get generalDefault => 'Default';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Connected to MQTT broker';

  @override
  String get mqttDisconnected => 'No connection to MQTT broker';

  @override
  String get mqttBrokerURI => 'Broker URI';

  @override
  String get mqttBrokerURIHint => 'MQTT broker URI';

  @override
  String get mqttPort => 'Port';

  @override
  String get mqttPortHint => 'MQTT port';

  @override
  String get mqttClientId => 'Client-ID';

  @override
  String get mqttClientIdHint => 'MQTT Client-ID';

  @override
  String get mqttUsername => 'Username';

  @override
  String get mqttUsernameHint => 'MQTT username';

  @override
  String get mqttPassword => 'Password';

  @override
  String get mqttPasswordHint => 'MQTT password';

  @override
  String get mqttCertificate => 'Certificate';

  @override
  String get mqttCertificateHint => 'MQTT certificate';

  @override
  String get mqttTopic => 'Topic';

  @override
  String get mqttTopicHint => 'MQTT topic';

  @override
  String get electricityMeter => 'Electricity meter';

  @override
  String get electricityMeterCurrent => 'Current';

  @override
  String get electricityMeterHistory => 'History';

  @override
  String get electricityMeterReading => 'Meter reading';

  @override
  String get connectivity => 'Connectivity';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Electric meters',
      one: 'Electric meter',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description => 'Modbus-Energy-Meters-MQTT-Gateway';

  @override
  String get bluetoothConnectionLost => 'Bluetooth connection lost';

  @override
  String get bluetoothConnectionLostDescription =>
      'The Bluetooth connection to the device has been lost. Please check the connection to the device.';

  @override
  String get openBluetoothSettings => 'Open Bluetooth settings';

  @override
  String get password => 'Password';

  @override
  String get setInitialPassword => 'Set initial password';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'The password must be at least $length characters long';
  }

  @override
  String get repeatPassword => 'Repeat password';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get savePassword => 'Save password';

  @override
  String get savePasswordHint =>
      'The password is saved for future connections on your device.';

  @override
  String get retrieveNtpServer => 'Retrieve time from NTP server';

  @override
  String get retrieveNtpServerFailed =>
      'The connection to the NTP server could not be established.';

  @override
  String get retrieveNtpServerSuccess =>
      'The connection to the NTP server was successful.';

  @override
  String get settingsPasswordNewPasswordDescription => 'Enter new password';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Password change successful';

  @override
  String get dhcpRangeStart => 'DHCP range start';

  @override
  String get dhcpRangeEnd => 'DHCP range end';

  @override
  String get forwardOnMQTT => 'Forward to MQTT';

  @override
  String get showAll => 'Show all';

  @override
  String get hide => 'Hide';

  @override
  String get changeToAPMode => 'Change to AP mode';

  @override
  String get changeToAPModeDescription =>
      'You are about to connect your device to a WiFi network, in which case the connection to the device is disconnected and you must reconnect to your device via the configured network.';

  @override
  String get consumption => 'Consumption';

  @override
  String get currentDay => 'Current day';

  @override
  String get twoWeeks => '2 Weeks';

  @override
  String get oneYear => '1 Year';

  @override
  String get threeYears => '3 Years';

  @override
  String passwordMinLength(Object length) {
    return 'Password needs at least $length characters.';
  }

  @override
  String get passwordNeedsLetter => 'Password must contain a letter.';

  @override
  String get passwordNeedsNumber => 'Password must contain a number.';

  @override
  String get portEmpty => 'Port cannot be empty';

  @override
  String get portInvalid => 'Invalid port';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'Port must be between $rangeStart and $rangeEnd';
  }

  @override
  String get ipAddressEmpty => 'IP address cannot be empty';

  @override
  String get ipAddressInvalid => 'Invalid IP address';

  @override
  String get subnetMaskEmpty => 'Subnet mask cannot be empty';

  @override
  String get subnetMaskInvalid => 'Invalid subnet mask';

  @override
  String get gatewayEmpty => 'Gateway cannot be empty';

  @override
  String get gatewayInvalid => 'Invalid gateway';

  @override
  String get dnsEmpty => 'DNS cannot be empty';

  @override
  String get dnsInvalid => 'Invalid DNS';

  @override
  String get uriEmpty => 'URI cannot be empty';

  @override
  String get uriInvalid => 'Invalid URI';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Electricity meter successfully changed';

  @override
  String get networkChangedSuccessfully =>
      'Network configuration successfully changed';

  @override
  String get mqttChangedSuccessfully =>
      'MQTT configuration successfully changed';

  @override
  String get modbusChangedSuccessfully =>
      'Modbus settings successfully changed';

  @override
  String get loginData => 'Delete login data';

  @override
  String get valueConfigured => 'Configured';

  @override
  String get electricityMeterHistoryNoData => 'No data available';

  @override
  String get locationChangedSuccessfully => 'Location successfully changed';

  @override
  String get settingsNameFailEmpty => 'Name cannot be empty';

  @override
  String settingsNameFailLength(Object length) {
    return 'Name must not be longer than $length characters';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Summer/Wintertime settings successfully changed';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Relay-Function successfully changed';

  @override
  String get relayFunctionHeader => 'Relay function';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Power on behaviour successfully changed';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Dimming behaviour successfully changed';

  @override
  String get dimmerBrightnessDescription =>
      'The minimum and maximum brightness affects all adjustable brightnesses of the dimmer.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Basic settings successfully changed';

  @override
  String get liveUpdateEnabled => 'Live test enabled';

  @override
  String get liveUpdateDisabled => 'Live test disabled';

  @override
  String get liveUpdateDescription =>
      'The last changed slider value will be send to the device';

  @override
  String get demoDevices => 'Demo devices';

  @override
  String get showDemoDevices => 'Show demo devices';

  @override
  String get deviceCategoryTimeSwitch => 'Time switch';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Multifunctional relay';

  @override
  String get deviceCategoryDimmer => 'Dimmer';

  @override
  String get deviceCategoryShutter => 'Roller Shutters and Blinds';

  @override
  String get deviceCategoryRelay => 'Relay';

  @override
  String get search => 'Search';

  @override
  String get configurationsHeader => 'Configurations';

  @override
  String get configurationsDescription => 'Manage your configurations here.';

  @override
  String get configurationsNameFailEmpty =>
      'Configuration name cannot be empty';

  @override
  String get configurationDeleted => 'Configuration deleted';

  @override
  String codeFound(Object codeType) {
    return '$codeType code found';
  }

  @override
  String get errorCameraPermission =>
      'Please allow the camera permission to scan the ELTAKO code.';

  @override
  String get authorizationSuccessful => 'Successfully authorized on device';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'The device is now ready for a new authorization.';

  @override
  String get settingsResetConnectionHeader => 'Reset connection';

  @override
  String get settingsResetConnectionDescription =>
      'Do you really want to reset the connection?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'Connection has been successfully reset.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Switch behaviour successfully changed';

  @override
  String get runtimeChangedSuccesfully =>
      'Runtime behaviour successfully changed';

  @override
  String get expertModeActivated => 'Expert mode activated';

  @override
  String get expertModeDeactivated => 'Expert mode deactivated';

  @override
  String get license => 'License';

  @override
  String get retry => 'Retry';

  @override
  String get provisioningConnectingHint =>
      'Device connection is being established. This can take up to 1 minute.';

  @override
  String get serialnumberEmpty => 'Serial number cannot be empty';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth is disabled, please enable it to discover Bluetooth devices.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Bluetooth permissions weren\'t granted.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Bluetooth permissions weren\'t granted. Please enable them in your device settings.';

  @override
  String get requestPermission => 'Request permission';

  @override
  String get goToSettings => 'Go to settings';

  @override
  String get enableBluetooth => 'Enable bluetooth';

  @override
  String get installed => 'Installed';

  @override
  String teachInDialogDescription(Object type) {
    return 'Would you like to teach in your device with $type?';
  }

  @override
  String get useMatter => 'Use Matter';

  @override
  String get relayMode => 'Activate relay-mode';

  @override
  String get whatsNew => 'New in this version';

  @override
  String get migrationHint =>
      'A migration is necessary to use the new features.';

  @override
  String get migrationHeader => 'Migration';

  @override
  String get migrationProgress => 'Migration in progress...';

  @override
  String get letsGo => 'Let\'s go!';

  @override
  String get noDevicesFound =>
      'No devices found. Check whether your device is in pairing mode.';

  @override
  String get interfaceStateEmpty => 'No devices were found';

  @override
  String get ssidEmpty => 'SSID cannot be empty';

  @override
  String get passwordEmpty => 'Password cannot be empty';

  @override
  String get settingsDeleteSettingsHeader => 'Reset Settings';

  @override
  String get settingsDeleteSettingsDescription =>
      'Do you really want to reset all settings?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'All settings have been successfully reset.';

  @override
  String get locationNotFound => 'Location not found';

  @override
  String get timerProgramEmptySaveHint =>
      'The time program is empty. Do you want to cancel the editing?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'No days are selected. Do you want to save the time program anyway?';

  @override
  String get timeProgramNoDays => 'At least one day must be activated';

  @override
  String timeProgramColliding(Object program) {
    return 'Time program collides with program $program';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Time program is a duplicate of program $program';
  }

  @override
  String get screenshotZgw16 => 'Detached house';

  @override
  String get interfaceStateUnknown => 'No devices found';

  @override
  String get settingsPinChange => 'Change PIN';

  @override
  String get timeProgrammOneTime => 'one-time';

  @override
  String get timeProgrammRepeating => 'repeating';

  @override
  String get generalIgnore => 'Ignore';

  @override
  String get timeProgramChooseDay => 'Choose day';

  @override
  String get generalToday => 'Today';

  @override
  String get generalTomorrow => 'Tomorrow';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth and PIN successfully changed';

  @override
  String get generalTextDimTime => 'Dimming time';

  @override
  String get discoverySu62Description => '1-Channel Time Switch Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Always on';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth is permanently enabled.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Note: If this setting is activated, the device is permanently visible to everyone via Bluetooth! It is recommended to change the default PIN.';

  @override
  String get bluetoothManualStartupOnTitle => 'Temporary on';

  @override
  String get bluetoothManualStartupOnDescription =>
      'After power is applied, Bluetooth is activated for 3 minutes.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Note: Pairing standby is activated for 3 minutes and then switches off. If a new connection is to be established, the button must be held down for approx. 5 seconds.';

  @override
  String get bluetoothManualStartupOffTitle => 'Manual startup';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Bluetooth is manually activated via the button input.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Note: To activate Bluetooth, the button on the button input must be held down for approx. 5 seconds.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Programmes can either be executed repeatedly by always carrying out a switching operation on the configured days and times, or they can be executed only once at the configured switching time.';

  @override
  String versionHeader(Object version) {
    return 'Version $version';
  }

  @override
  String get releaseNotesHeader => 'Release notes';

  @override
  String get release30Header => 'The new ELTAKO Connect app is here!';

  @override
  String get release30FeatureDesignHeader => 'New design';

  @override
  String get release30FeatureDesignDescription =>
      'The app has been completely revised and has a new design. It is now even easier and more intuitive to use.';

  @override
  String get release30FeaturePerformanceHeader => 'Improved performance';

  @override
  String get release30FeaturePerformanceDescription =>
      'Enjoy an enhanced set-up and reduced loading times - for a better user experience.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Cross-device configurations';

  @override
  String get release30FeatureConfigurationDescription =>
      'Save device configurations and transfer them to other devices. Even if they do not have the same hardware, you can, for example, transfer the configuration of your S2U12DBT1+1-UC to an ASSU-BT or vice versa.';

  @override
  String get release31Header =>
      'The new flush-mounted 1-channel time switch with Bluetooth is here!';

  @override
  String get release31Description => 'What can the SU62PF-BT/UC do?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Up to 60 time programs.';

  @override
  String get release31DeviceNote2 =>
      'Astro function: The clock switches devices based on sunrise and sunset.';

  @override
  String get release31DeviceNote3 =>
      'Random mode: switching times can be randomly shifted by up to 15 minutes.';

  @override
  String get release31DeviceNote4 =>
      'Summer/winter time changeover: The clock automatically switches to summer or winter time.';

  @override
  String get release31DeviceNote5 =>
      'Universal supply and control voltage 12-230V UC.';

  @override
  String get release31DeviceNote6 => 'Push-button input for manual switching.';

  @override
  String get release31DeviceNote7 =>
      '1 NO contact potential-free 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 => 'One-time execution of time programs.';

  @override
  String get generalNew => 'New';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count years ago',
      one: 'Last year',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count months ago',
      one: 'Last month',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count weeks ago',
      one: 'Last week',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count days ago',
      one: 'Yesterday',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minutes ago',
      one: 'A minute ago',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count hours ago',
      one: 'An hour ago',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count seconds ago',
      one: 'A second ago',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Just now';

  @override
  String get discoveryEsripmDescription => 'Impulse switching relay IP Matter';

  @override
  String get generalTextKidsRoom => 'Childrens room function';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'When switching on with a longer push-button action ($mode), the light is switched on at the lowest brightness level after approx. 1 second and slowly dimmed up as long as the push-button is held down, without changing the last saved brightness level.';
  }

  @override
  String get generalTextSceneButton => 'Scene button';

  @override
  String get settingsEnOceanConfigHeader => 'EnOcean configuration';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'EnOcean configuration successfully changed';

  @override
  String get activateEnOceanRepeater => 'Activate EnOcean Repeater';

  @override
  String get enOceanRepeaterLevel => 'Repeater level';

  @override
  String get enOceanRepeaterLevel1 => 'One level';

  @override
  String get enOceanRepeaterLevel2 => 'Two levels';

  @override
  String get enOceanRepeaterOffDescription =>
      'No wireless signals are received from sensors.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Only the wireless signals from sensors are received, checked and forwarded at full transmission power. Wireless signals from other repeaters are ignored to reduce the amount of data.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'In addition to the wireless signals from sensors, the wireless signals from 1-level repeaters are also processed. A wireless signal can therefore be received and amplified a maximum of two times. Wireless repeaters do not need to be taught in. They receive and amplify the wireless signals from all wireless sensors in their reception area.';

  @override
  String get settingsSensorHeader => 'Sensors';

  @override
  String get sensorChangedSuccessfully => 'Sensors successfully changed';

  @override
  String get wiredButton => 'Wired pushbutton';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'Enter or scan EnOcean-ID';

  @override
  String get enOceanIdInvalid => 'Invalid EnOcean-ID';

  @override
  String get enOceanAddAutomatically => 'Teach-in with EnOcean Telegram';

  @override
  String get enOceanAddDescription =>
      'The EnOcean wireless protocol makes it possible to teach-in and operate push-buttons in your actuator.\n\nChoose either automatic teach-in to teach-in push-buttons at the touch of a button or select the manual option to scan or type in the EnOcean ID of your push-button.';

  @override
  String get enOceanTelegram => 'Telegram';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Enter the EnOcean-ID of your $sensorType or scan the EnOcean-QR-Code of your $sensorType, to add it';
  }

  @override
  String get enOceanCode => 'EnOcean QR code';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Search for the EnOcean code on your $sensorType and scan it with your camera.';
  }

  @override
  String get enOceanButton => 'EnOcean pushbutton';

  @override
  String get enOceanBackpack => 'EnOcean adapter';

  @override
  String get sensorNotAvailable => 'No sensors have been paired yet';

  @override
  String get sensorAdd => 'Add sensors';

  @override
  String get sensorCancel => 'Cancel teach-in';

  @override
  String get sensorCancelDescription =>
      'Do you really want to cancel the button teach-in?';

  @override
  String get getEnOceanBackpack => 'Get your EnOcean adapter';

  @override
  String get enOceanBackpackMissing =>
      'To enter the fantastic world of perfect connectivity and communication, you need an EnOcean adapter.\nClick here for more information';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName successfully changed';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'connected via $deviceName';
  }

  @override
  String get lastSeen => 'Last seen';

  @override
  String get setButtonOrientation => 'Set orientation';

  @override
  String get setButtonType => 'Set button type';

  @override
  String get button1Way => '1-way pushbutton';

  @override
  String get button2Way => '2-way pushbutton';

  @override
  String get button4Way => '4-way pushbutton';

  @override
  String get buttonUnset => 'not set';

  @override
  String get button => 'Push button';

  @override
  String get sensor => 'Sensor';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sensors found',
      one: '1 sensor found',
      zero: 'No sensors found',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Search for sensors';

  @override
  String get searchAgain => 'Search again';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return 'Teach in $sensorType';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return 'Choose $sensorType';
  }

  @override
  String get sensorChooseDescription => 'Choose a button to teach in';

  @override
  String get sensorCategoryDescription =>
      'Select the category of the sensor you want to add.';

  @override
  String get sensorName => 'Button name';

  @override
  String get sensorNameFooter => 'Name your button';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName was added successfully';
  }

  @override
  String sensorDelete(Object sensorType) {
    return 'Delete $sensorType';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Do you really want to delete the $sensorType $sensorName?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName was deleted successfully';
  }

  @override
  String get buttonTapDescription => 'Tap the push button you want to add.';

  @override
  String get waitingForTelegram => 'The actuator is waiting for the telegram';

  @override
  String get copied => 'Copied';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType already paired';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'With the universal button, the direction is reversed by briefly releasing the button. Short control commands switch on or off.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'The direction button is \'switch on and dim up\' at the top and \'switch off and dim down\' at the bottom.';

  @override
  String get matterForwardingDescription =>
      'Forwarding of the Matter telegrams';

  @override
  String get none => 'None';

  @override
  String get buttonNoneDescription => 'The button has no functionality';

  @override
  String get buttonUnsetDescription => 'The button has no behaviour set';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Button type successfully changed';

  @override
  String forExample(Object example) {
    return 'e.g. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Only possible from production date 44/20';

  @override
  String get input => 'Input';

  @override
  String get buttonSceneValueOverride => 'Override scene button value';

  @override
  String get buttonSceneValueOverrideDescription =>
      'The scene button value will be overwritten with the current dim value through a button long press';

  @override
  String get buttonSceneDescription =>
      'The scene button turns on at a specific dim value';

  @override
  String get buttonPress => 'Button pressed';

  @override
  String get triggerOn =>
      'Universal push-button or direction button pressed on turn on side';

  @override
  String get triggerOff =>
      'Universal push-button or direction button pressed on turn off side';

  @override
  String get centralOn => 'Central On';

  @override
  String get centralOff => 'Central Off';

  @override
  String get centralButton => 'Central Button';

  @override
  String get enOceanAdapterNotFound => 'No EnOcean adapter found';

  @override
  String get updateRequired => 'Update required';

  @override
  String get updateRequiredDescription =>
      'Your app requires an update to support this new device.';

  @override
  String get release32Header =>
      'The new BR64 with Matter and EnOcean and the new SU62 Bluetooth flush-mounted time switch are now available!';

  @override
  String get release32EUD64Header =>
      'The new flush-mounted 1-channel dimmer with Matter over Wi-Fi and up to 300W is here!';

  @override
  String get release32EUD64Note1 =>
      'Configuration of dimming speed, on/off speed, children’s room/sleep mode, and much more.';

  @override
  String get release32EUD64Note2 =>
      'The functionality of the EUD64NPN-IPM can be expanded through adapters, such as the EnOcean adapter EOA64.';

  @override
  String get release32EUD64Note3 =>
      'Up to 30 EnOcean wireless switches can be directly linked to the EUD64NPN-IPM in combination with the EnOcean adapter EOA64 and forwarded to Matter.';

  @override
  String get release32EUD64Note4 =>
      'Two wired button inputs can be directly linked to the EUD64NPN-IPM or forwarded to Matter.';

  @override
  String get release32ESR64Header =>
      'The new potential-free flush-mounted 1-channel switch actuator with Matter over Wi-Fi and up to 16A is here!';

  @override
  String get release32ESR64Note1 =>
      'Configuration of various functions such as pulse switch (ES), relay function (ER), normally closed (ER-Inverse), and much more.';

  @override
  String get release32ESR64Note2 =>
      'The functionality of the ESR64PF-IPM can be expanded through adapters, such as the EnOcean adapter EOA64.';

  @override
  String get release32ESR64Note3 =>
      'Up to 30 EnOcean wireless switches can be directly linked to the ESR64PF-IPM in combination with the EnOcean adapter EOA64 and forwarded to Matter.';

  @override
  String get release32ESR64Note4 =>
      'One wired button input can be directly linked to the ESR64PF-IPM or forwarded to Matter.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count buttons found',
      one: '1 button found',
      zero: 'No buttons found',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'with a double impulse';

  @override
  String get impulseDescription =>
      'If the channel is on, it is turned off by an impulse.';

  @override
  String get locationServiceEnable => 'Activate location';

  @override
  String get locationServiceDisabledDescription =>
      'Location is disabled. Your operating system version needs the location to be able to find Bluetooth devices.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Location permissions have not been granted. Your operating system version requires location permissions to be able to find Bluetooth devices. Please allow the location permission in your device settings.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'Nearby devices permission wasn\'t granted. Please enable the permission in your device settings.';

  @override
  String get permissionNearbyDevices => 'Nearby devices';

  @override
  String get release320Header =>
      'The new powerful universal dimmer EUD12NPN-BT/600W-230V is here!';

  @override
  String get release320EUD600Header => 'What can the new universal dimmer do?';

  @override
  String get release320EUD600Note1 => 'Universal dimmer with up to 600W power';

  @override
  String get release320EUD600Note2 =>
      'Expandable with LUD12 power extension up to 3800W';

  @override
  String get release320EUD600Note3 =>
      'Local operation with universal or directional push-button';

  @override
  String get release320EUD600Note4 => 'Central functions On / Off';

  @override
  String get release320EUD600Note5 =>
      'Motion detector input for added convenience';

  @override
  String get release320EUD600Note6 =>
      'Integrated timer with 10 switching programs';

  @override
  String get release320EUD600Note7 => 'Astro function';

  @override
  String get release320EUD600Note8 => 'Individual switch-on brightness';

  @override
  String get mqttClientCertificate => 'Client Certificate';

  @override
  String get mqttClientCertificateHint => 'MQTT Client Certificate';

  @override
  String get mqttClientKey => 'Client Key';

  @override
  String get mqttClientKeyHint => 'MQTT Client Key';

  @override
  String get mqttClientPassword => 'Client Password';

  @override
  String get mqttClientPasswordHint => 'MQTT Client Password';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'Enable HomeAssistant MQTT Discovery';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Enable interface';

  @override
  String get busAddress => 'Bus address';

  @override
  String busAddressWithAddress(Object index) {
    return 'Bus address $index';
  }

  @override
  String get deviceType => 'Device type';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Register tables',
      one: 'Register table',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Current values';

  @override
  String get requestRTU => 'Request RTU';

  @override
  String get requestPriority => 'Request priority';

  @override
  String get mqttForwarding => 'Forwarding to MQTT';

  @override
  String get historicData => 'Historic data';

  @override
  String get dataFormat => 'Data format';

  @override
  String get dataType => 'Data type';

  @override
  String get description => 'Description';

  @override
  String get readWrite => 'Read/Write';

  @override
  String get unit => 'Unit';

  @override
  String get registerTableReset => 'Reset register table';

  @override
  String get registerTableResetDescription =>
      'Should the register table really be reset?';

  @override
  String get notConfigured => 'Not configured';

  @override
  String get release330ZGW16Header => 'Major update for the ZGW16WL-IP';

  @override
  String get release330Header =>
      'The ZGW16WL-IP with up to 16 electricity meters';

  @override
  String get release330ZGW16Note1 =>
      'Supports up to 16 ELTAKO Modbus electricity meters';

  @override
  String get release330ZGW16Note2 => 'Modbus TCP support';

  @override
  String get release330ZGW16Note3 => 'MQTT Discovery support';

  @override
  String get screenshotButtonLivingRoom => 'Living room push-button';

  @override
  String get registerChangedSuccessfully => 'Register successfully changed';

  @override
  String get serverCertificateEmpty => 'Server certificate cannot be empty';

  @override
  String get registerTemplates => 'Register templates';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Register template successfully changed';

  @override
  String get registerTemplateReset => 'Reset Register template';

  @override
  String get registerTemplateResetDescription =>
      'Should the register template really be reset?';

  @override
  String get registerTemplateNotAvailable => 'No Register templates available';

  @override
  String get rename => 'Rename';

  @override
  String get registerName => 'Register name';

  @override
  String get registerRenameDescription =>
      'Enter a custom name for the register';

  @override
  String get restart => 'Restart device';

  @override
  String get restartDescription => 'Do you really want to restart the device?';

  @override
  String get restartConfirmationDescription => 'The device is now restarting';

  @override
  String get deleteAllElectricityMeters => 'Delete all electricity meters';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Do you really want to delete all electricity meters?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'All electricity meters have been successfully deleted';

  @override
  String get resetAllElectricityMeters =>
      'Reset all electricity meter configurations';

  @override
  String get resetAllElectricityMetersDescription =>
      'Do you really want to reset all electricity meter configurations?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'All electricity meter configurations have been successfully reset';

  @override
  String get deleteElectricityMeterHistories =>
      'Delete all electricity meter histories';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Do you really want to delete all electricity meter histories?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'All electricity meter histories have been successfully deleted';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Your device currently supports only one electricity meter. Please update your firmware.';

  @override
  String get consumptionWithUnit => 'Usage (kWh)';

  @override
  String get exportWithUnit => 'Delivery (kWh)';

  @override
  String get importWithUnit => 'Consumption (kWh)';

  @override
  String get resourceWarningHeader => 'Resource limitations';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Operating MQTT and Modbus TCP at the same time is not possible due to limited system resources. Deactivate $protocol first.';
  }

  @override
  String get mqttEnabled => 'MQTT enabled';

  @override
  String get redirectMQTT => 'Go to MQTT Settings';

  @override
  String get redirectModbus => 'Go to Modbus Settings';

  @override
  String get unsupportedSettingDescription =>
      'With your current firmware version, some of the device settings are not supported. Please update your firmware to use the new features';

  @override
  String get updateNow => 'Update now';

  @override
  String get zgw241Hint =>
      'With this update, Modbus TCP is enabled by default and MQTT is disabled. This can be changed in the settings. With support for up to 16 counters, many optimisations have been made; this may lead to changes in the device settings. Please restart the device after adjusting the settings.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Device Configuration successfully changed';

  @override
  String get deviceConfiguration => 'Device configuration';

  @override
  String get tiltModeToggle => 'Tilt mode';

  @override
  String get tiltModeToggleFooter =>
      'If the device is set up in Matter, all functions must be reconfigured there';

  @override
  String get shaderMovementDirection => 'Reverse Up/Down';

  @override
  String get shaderMovementDirectionDescription =>
      'Reverse the direction for Up/Down movement of the motor';

  @override
  String get tiltTime => 'Tilt runtime';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0 tilt function';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Slat setting';

  @override
  String get generalTextPosition => 'Position';

  @override
  String get generalTextSlatPosition => 'Slat position';

  @override
  String get slatSettingDescription =>
      'Activate the slat function to adjust the tilt angle of your blinds.';

  @override
  String get scenePositionSliderDescription =>
      'The position defines how far the blinds are opened or closed.';

  @override
  String get sceneSlatPositionSliderDescription =>
      'The slat position defines the tilt angle of the roller shutter slats, if the tilt function is activated.';

  @override
  String get referenceRun => 'Calibration run';

  @override
  String get slatAutoSettingHint =>
      'In this mode, the position of the shades don\'t matter before the slats adjust adjust to the desired tilt position.';

  @override
  String get slatReversalSettingHint =>
      'In this mode, the shades will fully close before the slats adjust to the desired tilt position.';

  @override
  String get release340Header =>
      'The new ESB64NP-IPM flush-mounted matter shading actuator is here!';

  @override
  String get release340ESB64Header => 'What is the ESB64NP-IPM capable of?';

  @override
  String get release340ESB64Note1 =>
      'Our Matter Gateway-certified shading actuator with optional slat function';

  @override
  String get release340ESB64Note2 =>
      'Two wired button inputs for manual switching and forwarding to Matter';

  @override
  String get release340ESB64Note3 =>
      'Expandable with EnOcean adapter (EOA64). E.g. with EnOcean wireless push button F4T55';

  @override
  String get release340ESB64Note4 =>
      'Open for integrations thanks to REST API based on OpenAPI standard';

  @override
  String get activateTiltModeDialogText =>
      'If the tilt function is enabled, all settings will be lost. Are you sure you want to enable the tilt function?';

  @override
  String get deactivateTiltModeDialogText =>
      'If the tilt function is disabled, all settings will be lost. Are you sure you want to disable the tilt function?';

  @override
  String get buttonSceneESBDescription =>
      'The scene button sets the roller shutter to a specific position.';

  @override
  String get sceneValueOverride =>
      'Press and hold the button for 4 seconds to overwrite the position with the current value (starting value).';

  @override
  String get sceneCalibration =>
      'The calibration run moves the roller shutter fully down and up once to determine the end positions and enable position detection.';

  @override
  String get up => 'Up';

  @override
  String get down => 'Down';
}
