// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Finnish (`fi`).
class AppLocalizationsFi extends AppLocalizations {
  AppLocalizationsFi([String locale = 'fi']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint =>
      'Aktivoi Bluetooth laitteessa yhteyden muodostamiseksi';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count laitetta löytyi',
      one: '1 laitetta löytyi',
      zero: 'Laitteita ei löytynyt',
    );
    return '$_temp0';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Demolaitteet',
      one: 'Demolaite',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description => '2-kanavainen kellokytkin Bluetooth';

  @override
  String get discoveryImprint => 'Painotiedot';

  @override
  String get discoveryLegalnotice => 'Oikeudellinen huomautus';

  @override
  String get generalSave => 'Tallenna';

  @override
  String get generalCancel => 'Peruuta';

  @override
  String get detailsHeaderHardwareversion => 'Laitteistoversio';

  @override
  String get detailsHeaderSoftwareversion => 'Ohjelmiston versio';

  @override
  String get detailsHeaderConnected => 'Yhdistetty';

  @override
  String get detailsHeaderDisconnected => 'Irrotettu';

  @override
  String get detailsTimersectionHeader => 'Ohjelmat';

  @override
  String get detailsTimersectionTimercount => '60 käytetystä ohjelmasta';

  @override
  String get detailsConfigurationsectionHeader => 'Asetukset';

  @override
  String get detailsConfigurationPin => 'Laitteen PIN-koodi';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Kanava 1: $channel1 | Kanava 2:  $channel2';
  }

  @override
  String get settingsCentralHeader => 'Keskusohjaus On/Off';

  @override
  String get detailsConfigurationCentralDescription =>
      'Koskee vain, jos kanava on asetettu AUTO-asentoon.';

  @override
  String get detailsConfigurationDevicedisplaylock => 'Lukitse laitteen näyttö';

  @override
  String get timerOverviewHeader => 'Ohjelmat';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'Ei toiminnassa';

  @override
  String get timerDetailsListsectionDays1 => 'Maanantai';

  @override
  String get timerDetailsListsectionDays2 => 'Tiistai';

  @override
  String get timerDetailsListsectionDays3 => 'Keskiviikko';

  @override
  String get timerDetailsListsectionDays4 => 'Torstai';

  @override
  String get timerDetailsListsectionDays5 => 'Perjantai';

  @override
  String get timerDetailsListsectionDays6 => 'Lauantai';

  @override
  String get timerDetailsListsectionDays7 => 'Sunnuntai';

  @override
  String get timerDetailsHeader => 'Ohjelma %@';

  @override
  String get timerDetailsSunrise => 'Auringonnousu';

  @override
  String get generalToggleOff => 'OFF';

  @override
  String get generalToggleOn => 'ON';

  @override
  String get timerDetailsImpuls => 'Impulssi';

  @override
  String get generalTextTime => 'Aika';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Aikaviive';

  @override
  String get timerDetailsPlausibility => 'Aktivoi uskottavuuden tarkistus';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Jos poiskytkentä on asetettu aikaisemmaksi kuin päällekytkentä, kumpaakaan ohjelmaa ei oteta huomioon, esim. kytkeytyminen päälle auringonnousun aikaan ja poiskytkeytyminen klo 6:00. On myös tilanteita, jolloin tarkistusta ei haluta tehdä ollenkaa, esim. kytkeytyminen päälle auringonlaskun aikaan ja sammuminen klo 1:00 aamulla.';

  @override
  String get generalDone => 'Valmis';

  @override
  String get generalDelete => 'Poista';

  @override
  String get timerDetailsImpulsDescription => 'Muuta pulssikonfiguraatiota';

  @override
  String get settingsNameHeader => 'Laitteen nimi';

  @override
  String get settingsNameDescription => 'Tämä nimi näkyy myös hakemistossa.';

  @override
  String get settingsFactoryresetHeader => 'Tehdasasetukset';

  @override
  String get settingsFactoryresetDescription => 'Mikä sisältö palautetaan?';

  @override
  String get settingsFactoryresetResetbluetooth => 'Nollaa Bluetooth-asetukset';

  @override
  String get settingsFactoryresetResettime => 'Nollaa aika-asetukset';

  @override
  String get settingsFactoryresetResetall => 'Palauta tehdasasetukset';

  @override
  String get settingsDeletetimerHeader => 'Ohjelmien poistaminen';

  @override
  String get settingsDeletetimerDescription =>
      'Haluatko todella poistaa kaikki aikaohjelmat?';

  @override
  String get settingsDeletetimerAllchannels => 'Kaikki kanavat';

  @override
  String get settingsImpulseHeader => 'Impulssin kytkentäaika';

  @override
  String get settingsImpulseDescription =>
      'Impulssin kytkentäaika määrittää impulssin keston.';

  @override
  String get generalTextRandommode => 'Satunnainen ON/OFF kytkentä';

  @override
  String get settingsChannelsTimeoffsetHeader => 'Päivänseisausajan siirtymä';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Kesä: $summerOffset min | Talvi: ${winterOffset}min';
  }

  @override
  String get settingsLocationHeader => 'Sijainti';

  @override
  String get settingsLocationDescription =>
      'Aseta sijaintisi astrotoimintojen käyttöä varten.';

  @override
  String get settingsLanguageHeader => 'Laitteen kieli';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Aseta kieli automaattisesti';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Valitse kieli $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Saksa';

  @override
  String get settingsLanguageFrench => 'Ranska';

  @override
  String get settingsLanguageEnglish => 'Englanti';

  @override
  String get settingsLanguageItalian => 'Italia';

  @override
  String get settingsLanguageSpanish => 'Espanja';

  @override
  String get settingsDatetimeHeader => 'Päivämäärä ja kellonaika';

  @override
  String get settingsDatetimeSettimeautomatically => 'Käytä puhelimen aikaa';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Aseta aikavyöhyke automaattisesti';

  @override
  String get generalTextTimezone => 'Aikavyöhyke';

  @override
  String get settingsDatetime24Hformat => '24 tunnin formaatti';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Kesä-/talviaikaan automaattisesti';

  @override
  String get settingsDatetimeWinter => 'Talvi';

  @override
  String get settingsDatetimeSummer => 'Kesä';

  @override
  String get settingsPasskeyHeader => 'Laitteen nykyinen PIN-koodi';

  @override
  String get settingsPasskeyDescription => 'Syötä laitteen nykyinen PIN-koodi';

  @override
  String get timerDetailsActiveprogram => 'Ohjelma aktiivinen';

  @override
  String get timerDetailsActivedays => 'Aktiiviset päivät';

  @override
  String get timerDetailsSuccessdialogHeader => 'Onnistui';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Ohjelma lisätty onnistuneesti';

  @override
  String get settingsRandommodeDescription =>
      'Satunnaistila toimii vain kellonaikaan perustuvien ohjelmien kanssa, ei impulssi- tai astropohjaisten ohjelmien (auringonnousu/auringonlasku) kanssa.';

  @override
  String get settingsSolsticeHeader => 'Päivänseisausajan siirtymä';

  @override
  String get settingsSolsticeDescription =>
      'Asetettu aika määrittelee aikaeron auringon liikkeiden aikaan nähden, ja se aina käännetään vastaavaksi.';

  @override
  String get settingsSolsticeHint =>
      'Esimerkki:\nTalvella kytkentä tapahtuu aina 30 minuuttia ennen auringonlaskua ja auringonnousun mukainen  kytkentä tapahtuu aina 30 minuuttia etukäteen.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription =>
      'PIN-koodia tarvitaan yhteyden muodostamiseen.';

  @override
  String get settingsPinHeader => 'Uusi laitteen PIN-koodi';

  @override
  String get settingsPinNewpinDescription => 'Anna uusi PIN-koodi';

  @override
  String get settingsPinNewpinRepeat => 'Toista uusi PIN-koodi';

  @override
  String get detailsProductinfo => 'Tuotetiedot';

  @override
  String get settingsDatetimeSettimeautodescription => 'Valitse haluamasi aika';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minuuttia',
      one: 'Minuutti',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tunnit',
      one: 'Tunti',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Sekuntia',
      one: 'Sekunti',
    );
    return '$_temp0';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Kanavat',
      one: 'Kanava',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Kanava $number';
  }

  @override
  String get generalTextDate => 'Päivämäärä';

  @override
  String get settingsDatetime24HformatDescription => 'Valitse haluamasi muoto';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Kesä-/talviaika';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => 'AM/PM';

  @override
  String get detailsEdittimer => 'Muokkaa ohjelmia';

  @override
  String get settingsPinOldpinRepeat => 'Toista nykyinen PIN-koodi';

  @override
  String get settingsPinCheckpin => 'PIN-koodin tarkistaminen';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Laitteet',
      one: 'Laite',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Katkaise yhteys';

  @override
  String get settingsCentralDescription =>
      'Tuloliitin A1 ohjaa keskusohjauksen On/Off.\nPakotettu manuaalinen On/Off on käytössä vain, jos kanava on asetettu keskusohjaus On/Off -toimintoon.';

  @override
  String get settingsCentralHint =>
      'Esimerkki:\nKanava 1 = Keskusohjaus On/Off\nKanava 2 = Off\nA1 = Keskusohjaus On -> Vain C1 kytkeytyy päälle, C2 pysyy pois päältä';

  @override
  String get settingsCentralToggleheader => 'Keskustulokytkimet';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Nykyiset kanavat, joiden asetus on Keskusohjaus On/Off:';

  @override
  String get settingsSolsticeSign => 'Kirjaudu';

  @override
  String get settingsDatetimeTimezoneDescription => 'Keski-Euroopan aikaa';

  @override
  String get generalButtonContinue => 'Jatka';

  @override
  String get settingsPinConfirmationDescription => 'PIN-koodin vaihto onnistui';

  @override
  String get settingsPinFailDescription => 'PIN-koodin vaihto epäonnistui';

  @override
  String get settingsPinFailHeader => 'Epäonnistui';

  @override
  String get settingsPinFailShort =>
      'PIN-koodin on oltava täsmälleen 6-numeroinen.';

  @override
  String get settingsPinFailWrong => 'PIN-koodi on väärä';

  @override
  String get settingsPinFailMatch => 'PIN-koodit eivät täsmää';

  @override
  String get discoveryLostconnectionHeader => 'Yhteys katkennut';

  @override
  String get discoveryLostconnectionDescription =>
      'Laitteen yhteys on katkaistu.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Toiminta kuten AUTO-tilassa ja seuraa myös johdollisia keskusohjaustuloja';

  @override
  String get settingsChannelConfigOnDescription =>
      'Kytkee kanavan pysyvästi päälle ja ei huomioi ohjelmointeja';

  @override
  String get settingsChannelConfigOffDescription =>
      'Kytkee kanavan pysyvästi pois päältä ja ei huomioi ohjelmointeja';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Kytkee aika- ja astro-ohjelmien mukaan';

  @override
  String get bluetoothPermissionDescription =>
      'Laitteiden konfigurointiin tarvitaan Bluetooth.';

  @override
  String get timerListitemOn => 'Kytke päälle';

  @override
  String get timerListitemOff => 'Kytke pois päältä';

  @override
  String get timerListitemUnknown => 'Tuntematon';

  @override
  String get timerDetailsAstroHint =>
      'Sijainti on asetettava asetuksissa, jotta astro-ohjelmat toimivat oikein.';

  @override
  String get timerDetailsTrigger => 'Laukaisin';

  @override
  String get timerDetailsSunset => 'Auringonlasku';

  @override
  String get settingsLocationCoordinates => 'Koordinaatit';

  @override
  String get settingsLocationLatitude => 'Leveysaste';

  @override
  String get settingsLocationLongitude => 'Pituusaste';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Ohjelmia ei tällä hetkellä käytetä $day';
  }

  @override
  String get timerOverviewProgramloaded => 'Ohjelmia ladataan';

  @override
  String get timerOverviewProgramchanged => 'Ohjelma muutettu';

  @override
  String get settingsDatetimeProcessing =>
      'Päivämäärä ja kellonaika on muutettu';

  @override
  String get deviceNameEmpty => 'Syöte ei saa olla tyhjä';

  @override
  String deviceNameHint(Object count) {
    return 'Syötteessä ei saa olla enemmän kuin $count-merkkiä.';
  }

  @override
  String get deviceNameChanged => 'Laitteen nimi on muutettu';

  @override
  String get deviceNameChangedSuccessfully =>
      'Laitteen nimi vaihdettiin onnistuneesti';

  @override
  String get deviceNameChangedFailed => 'Tapahtui virhe.';

  @override
  String get settingsPinConfirm => 'Vahvista';

  @override
  String get deviceShowInstructions =>
      '1. Aktivoi kellokytkimen Bluetooth SET-näppäimellä\n2. Aloita haku napauttamalla ylhäällä olevaa painiketta.';

  @override
  String get deviceNameNew => 'Syötä uusi laitteen nimi';

  @override
  String get settingsLanguageRetrieved => 'Kieli haettiin';

  @override
  String get detailsProgramsShow => 'Näytä ohjelmat';

  @override
  String get generalTextProcessing => 'Odota';

  @override
  String get generalTextRetrieving => 'haetaan';

  @override
  String get settingsLocationPermission =>
      'Salli ELTAKO Connectin käyttää tämän laitteen sijaintia.';

  @override
  String get timerOverviewChannelloaded => 'Kanavat on ladattu';

  @override
  String get generalTextRandommodeChanged => 'Satunnaistilaa muutetaan';

  @override
  String get detailsConfigurationsectionChanged => 'Konfiguraatiota muutetaan';

  @override
  String get settingsSettimeFunctions => 'Aikatoimintoja muutetaan';

  @override
  String get imprintContact => 'Ota yhteyttä';

  @override
  String get imprintPhone => 'Puhelin';

  @override
  String get imprintMail => 'Sähköposti';

  @override
  String get imprintRegistrycourt => 'Rekisterituomioistuin';

  @override
  String get imprintRegistrynumber => 'Rekisteröintinumero';

  @override
  String get imprintCeo => 'Toimitusjohtaja';

  @override
  String get imprintTaxnumber => 'Myyntiveron tunnistenumero';

  @override
  String get settingsLocationCurrent => 'Nykyinen sijainti';

  @override
  String get generalTextReset => 'Nollaa';

  @override
  String get discoverySearchStart => 'Aloita haku';

  @override
  String get discoverySearchStop => 'Lopeta haku';

  @override
  String get settingsImpulsSaved => 'Impulssikytkentäaika on tallennettu';

  @override
  String get settingsCentralNochannel =>
      'Ei kanavia, joiden asetus on keskusohjaus On/Off.';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'Bluetooth-yhteys nollattiin onnistuneesti.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Bluetooth-yhteyksien nollaus epäonnistui.';

  @override
  String get imprintPublisher => 'Julkaisija';

  @override
  String get discoveryDeviceConnecting => 'Yhdistetään...';

  @override
  String get discoveryDeviceRestarting => 'Käynnistetään uudelleen...';

  @override
  String get generalTextConfigurationsaved =>
      'Kanavan konfiguraatio tallennettu.\n';

  @override
  String get timerOverviewChannelssaved => 'Tallenna kanavat';

  @override
  String get timerOverviewSaved => 'Ajastin tallennettu\n';

  @override
  String get timerSectionList => 'Listanäkymä\n';

  @override
  String get timerSectionDayview => 'Päivänäkymä';

  @override
  String get generalTextChannelInstructions => 'Kanavan asetukset';

  @override
  String get generalTextPublisher => 'Julkaisija';

  @override
  String get settingsDeletetimerDialog =>
      'Haluatko todella poistaa kaikki ohjelmat?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Haluatko todella nollata kaikki Bluetooth-asetukset?';

  @override
  String get settingsCentralTogglecentral => 'Keskusohjaus\nOn/Off';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName muutos onnistui.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName muutos epäonnistui.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'Kanavien vaihto onnistui.';

  @override
  String get timerDetailsSaveHeader => 'Tallenna ohjelma';

  @override
  String get timerDetailsDeleteHeader => 'Poista ohjelma';

  @override
  String get timerDetailsSaveDescription => 'Ohjelman tallennus onnistui.';

  @override
  String get timerDetailsDeleteDescription => 'Ohjelman poistaminen onnistui.';

  @override
  String get timerDetailsAlertweekdays =>
      'Ohjelmaa ei voida tallentaa, koska mitään viikonpäiviä ei ole valittu aktiiviseksi.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'Päivämäärä ja kellonaika vaihdettiin onnistuneesti.';

  @override
  String get discoveryConnectionFailed => 'Yhteys epäonnistui';

  @override
  String get discoveryDeviceResetrequired =>
      'Yhteyttä laitteeseen ei saatu muodostettua. Voit ratkaista ongelman poistamalla laitteen Bluetooth-asetuksista. Jos ongelma jatkuu, ota yhteyttä tekniseen tukeemme.';

  @override
  String get generalTextSearch => 'Etsi laitteita';

  @override
  String get generalTextOr => 'tai';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Kaikki ohjelmat poistettiin onnistuneesti.';

  @override
  String get generalTextManualentry => 'Manuaalinen syöttö';

  @override
  String get settingsLocationSaved => 'Sijainti tallennettu';

  @override
  String get settingsLocationAutosearch => 'Etsi sijainti automaattisesti';

  @override
  String get imprintPhoneNumber => '045 7870 6795';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Haluatko todella palauttaa laitteen tehdasasetuksiin?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'Laite on onnistuneesti palautettu tehdasasetuksiin.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Laitteen nollaus epäonnistui.';

  @override
  String get imprintPhoneNumberIos => '04578706795';

  @override
  String get mfzFunctionA2Title => '2-vaiheinen kytkentäviive (A2)';

  @override
  String get mfzFunctionA2TitleShort => '2-vaiheinen kytkentäviive (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Kun ohjausjännite kytketään, alkaa aikajakso t1 0-60 sekunnin välillä. Tämän ajanjakson päätyttyä kosketin 1-2 sulkeutuu ja alkaa aikajakso t2 0-60 sekunnin välillä. Tämän aikajakson päätyttyä kosketin 3-4 sulkeutuu. Keskeytyksen jälkeen, aikajakso alkaa uudelleen t1:stä.';

  @override
  String get mfzFunctionRvTitle => 'Poiskytkentäviive (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Poiskytkentäviive';

  @override
  String get mfzFunctionRvDescription =>
      'Kun ohjausjännite A1-A2 kytketään, releen kosketin vaihtaa asennosta 15-16 asentoon 15-18. \nKun ohjausjännite katkaistaan, aikajakso t alkaa; aikajakson päätyttyä, releen kosketin palaa asentoon 15-16. \nJos A1-A2 kytketään uudelleen ennen kuin aikajakso on päättynyt, kulunut aika nollataan.';

  @override
  String get mfzFunctionTiTitle => 'Tauko-/käyntiaika, käyntiaika alussa (TI)';

  @override
  String get mfzFunctionTiTitleShort =>
      'TI | Tauko-/käyntiaika, käyntiaika alussa';

  @override
  String get mfzFunctionTiDescription =>
      'Kun ohjausjännite A1-A2 kytketään, ensimmäinen aikajakso t1 (käyntiaika) käynnistyy ja asetetun ajan jälkeen, releen koskettimen asento vaihtuu ja aikajakso t2 (taukoaika) käynnistyy. Aikajaksot jatkuvat niin pitkään, kunnes ohjausjännite A1-A2 katkaistaan. Releen kosketin vaihtaa asentoon 15-18 heti kun ohjausjännite A1-A2 kytketään (käyntiaika käynnistyksen yhteydessä).';

  @override
  String get mfzFunctionAvTitle => 'Päällekytkentäviive (AV)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | Päällekytkentäviive';

  @override
  String get mfzFunctionAvDescription =>
      'Kun ohjausjännite A1-A2 kytketään, aikajakso t alkaa; aikajakson päätyttyä, releen kosketin vaihtaa asentoon 15-18. Jos ohjausjännite A1-A2 katkaistaan ennen kuin aikajakso on päättynyt, kulunut aika nollataan.';

  @override
  String get mfzFunctionAvPlusTitle => 'Päällekytkentäviive summaus (AV+)';

  @override
  String get mfzFunctionAvPlusTitleShort => 'AV+ | Päällekytkentäviive summaus';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Toiminto muuten samanlainen kuin toiminnossa AV. Mutta kun ohjausjännite A1-A2 keskeytyy, nykyinen kulunut aika tallennetaan ja sitä käytetään jatkossa.';

  @override
  String get mfzFunctionAwTitle =>
      'Pulssi poiskytkennän yhteydessä (pyyhkäisy) (AW)';

  @override
  String get mfzFunctionAwTitleShort => 'AW | Pulssi poiskytkennän yhteydessä';

  @override
  String get mfzFunctionAwDescription =>
      'Kun ohjausjännite A1-A2 katkaistaan, releen kosketin vaihtaa asentoon 15-18 ja samalla aikajakso t alkaa. Kun aikajakso t on kulunut loppuun, releen kosketin palaa asentoon 15-16. \nJos ohjausjännite kytketään käynnissä olevan aikajakson aikana, kosketin palaa takaisin asentoon 15-16 ja aikajakso t nollataan.';

  @override
  String get mfzFunctionIfTitle => 'Pulssisuodatin (IF)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | Pulssisuodatin';

  @override
  String get mfzFunctionIfDescription =>
      'Kun ohjausjännite A1-A2 kytketään, releen kosketin vaihtaa asentoon 15-18 asetetun aikajakson t ajaksi. Muut asetetun aikajakson aikana saapuvat ohjausimpulssit ohitetaan, kunnes asetettu aika on kulunut loppuun.';

  @override
  String get mfzFunctionEwTitle =>
      'Pulssi päällekytkennän yhteydessä (pyyhkäisy) (EW)';

  @override
  String get mfzFunctionEwTitleShort =>
      'EW | Pulssi päällekytkennän yhteydessä';

  @override
  String get mfzFunctionEwDescription =>
      'Kun ohjausjännite A1-A2 kytketään, releen kosketin vaihtaa asentoon 15-18 ja samalla aikajakso t alkaa. Kun aikajakso t on kulunut loppuun, releen kosketin palaa asentoon 15-16. \nJos ohjausjännite katkeaa käynnissä olevan aikajakson aikana, kosketin palaa takaisin asentoon 15-16 ja aikajakso t nollataan.';

  @override
  String get mfzFunctionEawTitle =>
      'Pulssi päälle- ja poiskytkennän yhteydessä (pyyhkäisy) (EAW)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | Pulssi päälle- ja poiskytkennän yhteydessä';

  @override
  String get mfzFunctionEawDescription =>
      'Kun ohjausjännite A1-A2 kytketään, releen kosketin vaihtaa asentoon 15-18 ja samalla aikajakso t1 alkaa. Kun aikajakso t1 on kulunut loppuun, releen kosketin palaa asentoon 15-16.\nKun ohjausjännite A1-A2 katkaistaan, releen kosketin vaihtaa asentoon 15-18 ja samalla aikajakso t2 alkaa. Kun aikajakso t2 on kulunut loppuun, releen kosketin palaa asentoon 15-16.';

  @override
  String get mfzFunctionTpTitle =>
      'Tauko-/käyntiaika, alkaa taukoajalla (vilkkurele) (TP)';

  @override
  String get mfzFunctionTpTitleShort =>
      'TP | Tauko-/käyntiaika, taukoaika alussa';

  @override
  String get mfzFunctionTpDescription =>
      'Kun ohjausjännite A1-A2 kytketään, ensimmäinen aikajakso t1 (taukoaika) käynnistyy ja asetetun ajan jälkeen, releen koskettimen asento vaihtuu ja aikajakso t2 (käyntiaika) käynnistyy. Aikajaksot jatkuvat niin pitkään, kunnes ohjausjännite A1-A2 katkaistaan. Releen kosketin pysyy asennossa 15-16 kun ohjausjännite A1-A2 kytketään (taukoaika käynnistyksen yhteydessä).';

  @override
  String get mfzFunctionIaTitle =>
      'Impulssiohjattu päällekytkentäviive (esim. oven avaaja) (IA)';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA | Impulssiohjattu päällekytkentäviive';

  @override
  String get mfzFunctionIaDescription =>
      'Aikajakso t1 alkaa, kun ohjausjännite A1-A2 kytketään. Kun aikajakso t1 on kulunut loppuun, releen kosketin vaihtaa asentoon 15-18 ja tästä käynnistyy aikajakso t2. Kun aikajakso t2 on kulunut loppuun, releen kosketin palaa asentoon 15-16. \nJos t1 on asetettu minimiarvoon = 0,1 sekuntia, IA-toiminto toimii pulssisuodattimena t2 ajoituksen aikana.';

  @override
  String get mfzFunctionArvTitle => 'Päälle- ja poiskytkentäviive (ARV)';

  @override
  String get mfzFunctionArvTitleShort => 'ARV | Päälle- ja poiskytkentäviive';

  @override
  String get mfzFunctionArvDescription =>
      'Kun ohjausjännite A1-A2 kytketään, aikajakso t1 alkaa; aikajakson jälkeen releen kosketin vaihtaa asentoon 15-18. Jos ohjausjännite A1-A2 katkaistaan, aikajakso t2 alkaa ja aikajakson jälkeen releen kosketin palaa asentoon 15-16.\nJos päällekytkentäviiveen aikajakso t1 keskeytetään, aika käynnistetään uudelleen alusta.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Päälle- ja poiskytkentäviive, summaus (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | Päälle- ja poiskytkentäviive, summaus';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Toiminto muuten samanlainen kuin ARV, mutta sillä erolla, että jos ohjausjännite A1-A2 keskeytyy, nykyinen kulunut aika tallennetaan ja sitä käytetään jatkossa.';

  @override
  String get mfzFunctionEsTitle => 'Impulssirele (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | Impulssirele';

  @override
  String get mfzFunctionEsDescription =>
      'Yli 50 ms:n pituisilla ohjausimpulsseilla, releen kosketin kytkee päälle ja pois päältä.';

  @override
  String get mfzFunctionEsvTitle =>
      'Impulsrele poiskytkentäviiveellä ja poiskytkentävaroituksella (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | Impulsrele poiskytkentäviiveellä ja poiskytkentävaroituksella';

  @override
  String get mfzFunctionEsvDescription =>
      'Toiminto muuten samanlainen kuin toiminnossa SRV, mutta lisänä poiskytkentävaroitus: Noin 30 sekuntia ennen aikajakson t umpeutumista, releen kosketin (ja esim. kytketty valaisin) ”vilkkuu” 3 kertaa asteittain lyhenevillä aikaväleillä.';

  @override
  String get mfzFunctionErTitle => 'Reletoiminto (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | Reletoiminto';

  @override
  String get mfzFunctionErDescription =>
      'Niin pitkään, kun ohjausjännite A1-A2 on kytkettynä, releen kosketin pysyy asennossa 15-18. \nKun ohjausjännite A1-A2 katkaistaan, releen kosketin palaa asentoon 15-16.';

  @override
  String get mfzFunctionSrvTitle => 'Impulssirele poiskytkentäviiveellä (SRV)';

  @override
  String get mfzFunctionSrvTitleShort =>
      'SRV | Impulssirele poiskytkentäviiveellä';

  @override
  String get mfzFunctionSrvDescription =>
      'Yli 50 ms:n pituisilla ohjausimpulsseilla, releen kosketin kytkee päälle ja pois päältä.\nKun releen koskettimen asento vaihtuu asennosta 15-18, aikajakso t alkaa automaattisesti. Kun aikaviive on kulunut loppuun, releen kosketin palaa automaattisesti lähtöasentoon 15-16.';

  @override
  String get detailsFunctionsHeader => 'Toiminnot';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Aika (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'Pysyvästi päällä.';

  @override
  String get mfzFunctionOffDescription => 'Pysyvästi pois päältä.';

  @override
  String get mfzFunctionMultiplier => 'Kerroin';

  @override
  String get discoveryMfz12Description => 'Monitoimiaikarele Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'Pysyvästi ON';

  @override
  String get mfzFunctionOnTitleShort => 'Pysyvästi ON';

  @override
  String get mfzFunctionOffTitle => 'Pysyvästi OFF';

  @override
  String get mfzFunctionOffTitleShort => 'Pysyvästi OFF';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 sekuntia';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minuuttia';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 tuntia';

  @override
  String get mfzOverviewFunctionsloaded => 'Toimintoja ladataan';

  @override
  String get mfzOverviewSaved => 'Toiminto tallennettu';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'Bluetooth-asetuksen muuttaminen onnistui.';

  @override
  String get settingsBluetoothInformation =>
      'Huomio: Jos tämä asetus on aktivoituna, laite on pysyvästi kaikkien nähtävissä Bluetoothin kautta!\nTällöin on suositeltavaa vaihtaa laitteen PIN-koodi.';

  @override
  String get settingsBluetoothContinuousconnection => 'Jatkuva näkyvyys';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Aktivoimalla Bluetoothin jatkuvan näkyvyyden, Bluetooth pysyy jatkuvasti päällä laitteessa ($deviceType), eikä sitä tarvitse aktivoida manuaalisesti ennen yhteyden muodostamista.';
  }

  @override
  String get settingsBluetoothTimeout => 'Yhteyden aikakatkaisu';

  @override
  String get settingsBluetoothPinlimit => 'PIN-koodin raja';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'Yhteys katkaistaan $timeout minuutin käyttämättömyyden jälkeen.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Turvallisuussyistä sinulla on enintään $attempts yritystä syöttää PIN-koodi. Bluetooth poistetaan tämän jälkeen käytöstä ja se on aktivoitava uudelleen manuaalisesti uutta yhteyttä varten.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'yritykset';

  @override
  String get settingsResetfunctionHeader => 'Nollaa toiminnot';

  @override
  String get settingsResetfunctionDialog =>
      'Haluatko todella nollata kaikki toiminnot?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Kaikki toiminnot on onnistuneesti nollattu.';

  @override
  String get mfzFunctionTime => 'Aika (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Ei Bluetooth-yhteyttä';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Heti laitteen näytön lukitsemisen jälkeen Bluetooth deaktivoidaan ja se on aktivoitava uudelleen manuaalisesti uuden yhteyden muodostamiseksi.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Lukitsetko varmasti laitteen näytön?';

  @override
  String get settingsDemodevices => 'Näytä demolaitteet';

  @override
  String get generalTextSettings => 'Asetukset';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Tiedot';

  @override
  String get detailsConfigurationDimmingbehavior => 'Himmennystoiminnot';

  @override
  String get detailsConfigurationSwitchbehavior => 'Vaihda toimintoa';

  @override
  String get detailsConfigurationBrightness => 'Kirkkaus';

  @override
  String get detailsConfigurationMinimum => 'Minimitaso';

  @override
  String get detailsConfigurationMaximum => 'Maksimitaso';

  @override
  String get detailsConfigurationSwitchesGr => 'Ryhmärele (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Ryhmäkytkin (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer => 'Sulkeutuva kosketin (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Painike vapautettuna -> Pois päältä. Pidä painettuna -> Päällä.';

  @override
  String get detailsConfigurationSwitchesOpenerer =>
      'Avautuva kosketin (käänteinen ER)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'Painike vapautettuna -> Päällä. Pidä painettuna -> Pois päältä.';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Kytkin';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Jokaisella kytkimen asennon muutoksella, valo kytketään päälle ja pois päältä.';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Painike';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'Lyhyt painikkeen painallus kytkee valon päälle ja pois päältä.';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Pidä painike painettuna. Kun vapautat painikkeen, moottori pysähtyy.';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'Paina painiketta lyhyesti käynnistääksesi moottorin ja paina uudelleen pysäyttääksesi sen.';

  @override
  String get detailsConfigurationWifiloginScan => 'Skannaa QR-koodi';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Skannattu koodi ei ole voimassa';

  @override
  String get detailsConfigurationWifiloginDescription => 'Syötä koodi';

  @override
  String get detailsConfigurationWifiloginPassword => 'Salasana';

  @override
  String get discoveryEsbipDescription => 'Verho-ohjain IP';

  @override
  String get discoveryEsripDescription => 'Impulssirele IP';

  @override
  String get discoveryEudipDescription => 'Yleishimmennin IP';

  @override
  String get generalTextLoad => 'Ladataan';

  @override
  String get wifiBasicautomationsNotFound => 'Automaatiota ei löytynyt.';

  @override
  String get wifiCodeInvalid => 'Virheellinen koodi';

  @override
  String get wifiCodeValid => 'Voimassa oleva koodi';

  @override
  String get wifiAuthorizationLogin => 'Yhdistä';

  @override
  String get wifiAuthorizationLoginFailed => 'Kirjautuminen epäonnistui';

  @override
  String get wifiAuthorizationSerialnumber => 'Sarjanumero';

  @override
  String get wifiAuthorizationProductiondate => 'Tuotantopäivä';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'WiFi-salasana';

  @override
  String get generalTextUsername => 'Käyttäjätunnus';

  @override
  String get generalTextEnter => 'TAI SYÖTÄ MANUAALISESTI';

  @override
  String get wifiAuthorizationScan => 'Skannaa ELTAKO-koodi.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Tämä laite ei tällä hetkellä tue muita asetuksia';

  @override
  String get settingsUsedemodelay => 'Käytä demo viivettä';

  @override
  String get settingsImpulsLoad => 'Pulssin kytkentäaika on ladattu';

  @override
  String get settingsBluetoothLoad => 'Bluetooth-asetuksia ladataan.';

  @override
  String get detailsConfigurationsectionLoad => 'Määrityksiä ladataan';

  @override
  String get generalTextLogin => 'Kirjaudu sisään';

  @override
  String get generalTextAuthentication => 'Todennus';

  @override
  String get wifiAuthorizationScanDescription =>
      'Etsi ELTAKO-koodi WiFi-laitteesta tai sen mukana toimitetusta tuotekortista ja kohdista se kameraan.';

  @override
  String get wifiAuthorizationScanShort => 'Skannaa ELTAKO-koodi';

  @override
  String get detailsConfigurationEdgemode => 'Himmennystapa';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Nouseva reuna';

  @override
  String get generalTextNetwork => 'Verkko';

  @override
  String get wifiAuthenticationSuccessful => 'Todennus onnistui';

  @override
  String get detailsConfigurationsectionSavechange => 'Konfiguraatio muuttunut';

  @override
  String get discoveryWifiAdddevice => 'Lisää WiFi-laite';

  @override
  String get wifiAuthenticationDelay => 'Tämä voi kestää jopa 1 minuutin';

  @override
  String get generalTextRetry => 'Yritä uudelleen';

  @override
  String get wifiAuthenticationCredentials => 'Anna WiFi-tunnuksesi';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Voi kestää jopa 1 minuutin, ennen kuin laite on valmis ja näkyy sovelluksessa';

  @override
  String get wifiAuthenticationCredentialsShort => 'Anna WiFi-tunnistetiedot';

  @override
  String get wifiAuthenticationTeachin => 'Opeta laite WiFi-verkkoon';

  @override
  String get wifiAuthenticationEstablish => 'Muodosta yhteys laitteeseen';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Laite muodostaa yhteyden WiFi-verkkoon $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Yhteys epäonnistui. Irrota laite virtalähteestä muutamaksi sekunniksi ja yritä liittää se uudelleen';

  @override
  String get wifiAuthenticationReset => 'Nollaa todennukset';

  @override
  String get wifiAuthenticationResetHint => 'Kaikki todennustiedot poistetaan.';

  @override
  String get wifiAuthenticationInvaliddata => 'Todennustiedot virheellisiä';

  @override
  String get wifiAuthenticationReauthenticate => 'Todenna uudelleen';

  @override
  String get wifiAddhkdeviceHeader => 'Lisää laite';

  @override
  String get wifiAddhkdeviceDescription =>
      'Liitä uusi ELTAKO-laitteesi WiFi-verkkoon Apple Home -sovelluksen kautta.';

  @override
  String get wifiAddhkdeviceStep1 => '1. Avaa Apple Home -sovellus';

  @override
  String get wifiAddhkdeviceStep2 =>
      '2. Napauta sovelluksen oikeassa yläkulmassa olevaa plus-merkkiä ja valitse **Lisää lisälaite**.';

  @override
  String get wifiAddhkdeviceStep3 => '3. Seuraa sovelluksen ohjeita.';

  @override
  String get wifiAddhkdeviceStep4 =>
      '4. Laitteesi asetukset voidaan nyt määrittää ELTAKO Connect -sovelluksessa.';

  @override
  String get detailsConfigurationRuntime => 'Suoritusaika';

  @override
  String get detailsConfigurationRuntimeMode => 'Tila';

  @override
  String get generalTextManually => 'Manuaalisesti';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'Kaihdinlaite määrittää itsenäisesti verhomoottorin käyntiajan jokaisen alimmasta pääteasennosta ylimpään pääteasentoon tapahtuvan matkan aikana (suositus).\nKäyttöönoton jälkeen, tällainen liike alhaalta ylös on suoritettava ilman keskeytystä.';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'Kaihdinmoottorin käyntiaika asetetaan manuaalisesti alla olevan keston avulla.\nVarmista, että määritetty käyntiaika vastaa kaihdinmoottorisi todellista käyntiaikaa.\nKäyttöönoton jälkeen, tällainen liike alhaalta ylöspäin on suoritettava keskeytyksettä.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'Demo-tila on käytettävissä vain REST API:n kautta';

  @override
  String get generalTextDemomodeActive => 'Demo-tila aktiivinen';

  @override
  String get detailsConfigurationRuntimeDuration => 'Kesto';

  @override
  String get detailsConfigurationSwitchesGs4 => 'Ryhmäkytkin (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Ryhmäkytkin, jossa on peruutus toiminto kaihtimien ohjausta varten';

  @override
  String get screenshotSu12 => 'Ulkovalo';

  @override
  String get screenshotS2U12 => 'Ulkovalo';

  @override
  String get screenshotMfz12 => 'Pumppu';

  @override
  String get screenshotEsr62 => 'Lamppu';

  @override
  String get screenshotEud62 => 'Kattovalaisin';

  @override
  String get screenshotEsb62 => 'Parvekkeen kaihtimet';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 ovat helppokäyttötoimintoja erilaisilla himmennyskäyrillä sellaisille himmennettäville 230V LED-lampuille ja valaisimille, joita ei voida himmentää tarpeeksi alas AUTO-tilassa niiden rakenteen vuoksi ja siksi ne on pakotettava vaihekulmaohjaukseen.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO-tilassa, kaikkien lampputyyppien himmennys on mahdollista.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Laskeva reuna';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 ovat helppokäyttötoimintoja, joissa on erilaiset himmennyskäyrät himmennettäville 230V LED-lampuille ja valaisimille.';

  @override
  String get updateHeader => 'Laiteohjelmiston päivitys';

  @override
  String get updateTitleStepSearch => 'Päivitystä etsitään';

  @override
  String get updateTitleStepFound => 'Päivitys löytyi';

  @override
  String get updateTitleStepDownload => 'Päivitystä ladataan';

  @override
  String get updateTitleStepInstall => 'Päivitystä asennetaan';

  @override
  String get updateTitleStepSuccess => 'Päivitys onnistui';

  @override
  String get updateTitleStepUptodate => 'Laite on ajan tasalla';

  @override
  String get updateTitleStepFailed => 'Päivitys epäonnistui';

  @override
  String get updateButtonSearch => 'Etsi päivityksiä';

  @override
  String get updateButtonInstall => 'Asenna päivitys';

  @override
  String get updateCurrentversion => 'Nykyinen versio';

  @override
  String get updateNewversion => 'Uusi laiteohjelmistopäivitys saatavilla';

  @override
  String get updateHintPower =>
      'Päivitys käynnistyy vasta sitten, kun laitteen lähtö ei ole aktiivinen. Laitetta ei saa irrottaa virtalähteestä eikä sovelluksesta saa poistua päivityksen aikana!';

  @override
  String get updateButton => 'Päivitys';

  @override
  String get updateHintCompatibility =>
      'Päivitystä suositellaan, sillä muuten jotkin sovelluksen toiminnoista ovat rajoitettuja.';

  @override
  String get generalTextDetails => 'Yksityiskohdat';

  @override
  String get updateMessageStepMetadata => 'Ladataan päivitystietoja';

  @override
  String get updateMessageStepPrepare => 'Päivitystä valmistellaan';

  @override
  String get updateTitleStepUpdatesuccessful => 'Päivitystä tarkistetaan';

  @override
  String get updateTextStepFailed =>
      'Valitettavasti jokin meni pieleen päivityksen aikana, yritä uudelleen muutaman minuutin kuluttua tai odota, kunnes laitteesi päivittyy automaattisesti (edellyttää internet yhteyttä).';

  @override
  String get configurationsNotavailable =>
      'Konfiguraatioita ei ole vielä saatavilla';

  @override
  String get configurationsAddHint =>
      'Luo uusia konfiguraatioita muodostamalla yhteys laitteeseen ja tallentamalla konfiguraatio.';

  @override
  String get configurationsEdit => 'Muokkaa konfiguraatiota';

  @override
  String get generalTextName => 'Nimi';

  @override
  String get configurationsDelete => 'Poista konfiguraatio';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'Haluatko todella poistaa konfiguraation: $configName?';
  }

  @override
  String get configurationsSave => 'Tallenna konfiguraatio';

  @override
  String get configurationsSaveHint =>
      'Vie laitteesi nykyinen konfiguraatio tai lataa jokin olemassa olevista konfiguraatioista.';

  @override
  String get configurationsImport => 'Tuo konfiguraatio';

  @override
  String configurationsImportHint(Object configName) {
    return 'Haluatko todella siirtää konfiguraation $configName?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Konfiguraatiot',
      one: 'Konfiguraatio',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare => 'Konfiguraatiota valmistellaan';

  @override
  String get configurationsStepName => 'Kirjoita konfiguraation nimi';

  @override
  String get configurationsStepSaving => 'Konfiguraatiota tallennetaan';

  @override
  String get configurationsStepSavedsuccessfully =>
      'Konfiguraatio tallennettiin onnistuneesti';

  @override
  String get configurationsStepSavingfailed =>
      'Konfiguraation tallentaminen epäonnistui';

  @override
  String get configurationsStepChoose => 'Valitse konfiguraatio';

  @override
  String get configurationsStepImporting => 'Konfiguraatiota tuodaan';

  @override
  String get configurationsStepImportedsuccessfully =>
      'Konfiguraatio on tuotu onnistuneesti';

  @override
  String get configurationsStepImportingfailed =>
      'Konfiguraation tuonti epäonnistui';

  @override
  String get discoveryAssuDescription =>
      'Bluetooth kellokytkin ulkopistorasiaan';

  @override
  String get settingsDatetimeDevicetime => 'Laitteen todellinen aika';

  @override
  String get settingsDatetimeLoading => 'Aika-asetuksia ladataan';

  @override
  String get discoveryEud12Description => 'Yleishimmennin Bluetooth';

  @override
  String get generalTextOffdelay => 'Poiskytkentäviive';

  @override
  String get generalTextRemainingbrightness => 'Jäljellä oleva kirkkaus';

  @override
  String get generalTextSwitchonvalue => 'Päällekytkennän arvo';

  @override
  String get motionsensorTitleNoremainingbrightness => 'Ei jäännöskirkkautta';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Jäännöskirkkaudella';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Jäännöskirkkaus kytkentäohjelman mukaan';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Jäännöskirkkaus ZE:n ja ZA:n kautta';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Ei jäännöskirkkautta (puoliautomaattinen)';

  @override
  String get generalTextMotionsensor => 'Liiketunnistin';

  @override
  String get generalTextLightclock => 'Valoherätys';

  @override
  String get generalTextSnoozeclock => 'Torkkutoiminto';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'Kun valo kytketään päälle ($mode), valo syttyy noin 1 sekunnin kuluttua pienimmällä kirkkaudella ja se himmennetään hitaasti siitä ylöspäin muuttamatta viimeksi tallennettua kirkkaustasoa.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Sammutettaessa ($mode), valaistus himmennetään nykyisestä tasosta minimikirkkauteen ja sammutetaan sen jälkeen. Valaistus voidaan sammuttaa milloin tahansa himmennysprosessin aikana painamalla painiketta lyhyesti. Pitkä painallus himmennyksen aikana himmentää ja lopettaa torkkutoiminnon.';
  }

  @override
  String get generalTextImmediately => 'Heti';

  @override
  String get generalTextPercentage => 'Prosentti';

  @override
  String get generalTextSwitchoffprewarning => 'Sammutuksen ennakkovaroitus';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Hidas himmennys minimikirkkauteen';

  @override
  String get generalDescriptionOffdelay =>
      'Laite kytkeytyy päälle, kun ohjausjännite kytketään. Jos ohjausjännite katkeaa, alkaa aikaviive, jonka jälkeen laite sammuu. Laite voidaan kytkeä päälle aikaviiveen kuluessa.';

  @override
  String get generalDescriptionBrightness =>
      'Kirkkaus, jolla himmennin kytkee valaisimen päälle.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'Himmennysarvo prosentteina, johon lamppu himmenee liiketunnistimen sammuttaessa.';

  @override
  String get generalDescriptionRuntime =>
      'Valoherätystoiminnon käyntiaika minimikirkkaudesta maksimikirkkauteen.';

  @override
  String get generalTextUniversalbutton => 'Yleispainike';

  @override
  String get generalTextDirectionalbutton => 'Suuntapainike';

  @override
  String get eud12DescriptionAuto =>
      'Automaattinen UT/RT tunnistus (RTD suunta-anturi diodilla)';

  @override
  String get eud12DescriptionRt => 'jossa on RTD suunta-anturi diodi';

  @override
  String get generalTextProgram => 'Ohjelma';

  @override
  String get eud12MotionsensorOff => 'Liiketunnistimen ollessa pois päältä';

  @override
  String get eud12ClockmodeTitleProgramze => 'Ohjelma ja keskusohjaus ON';

  @override
  String get eud12ClockmodeTitleProgramza => 'Ohjelma ja keskusohjaus OFF';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Ohjelma ja UT/RT ON';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Ohjelma ja UT/RT OFF';

  @override
  String get eud12TiImpulseTitle => 'Pulssiaika ON (t1)';

  @override
  String get eud12TiImpulseHeader => 'Himmennysarvo Pulssiaika ON';

  @override
  String get eud12TiImpulseDescription =>
      'Himmennysarvo prosentteina, johon valaistus säädetään ON-pulssin aikana.';

  @override
  String get eud12TiOffTitle => 'Pulssiaika OFF (t2)';

  @override
  String get eud12TiOffHeader => 'Himmennysarvo Pulssiaika OFF';

  @override
  String get eud12TiOffDescription =>
      'Himmennysarvo prosentteina, johon valaistus säädetään OFF-pulssin aikana.';

  @override
  String get generalTextButtonpermanentlight =>
      'Jatkuva valaistus painikkeella';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Jatkuvan valon painikkeella asettaminen välillä 0 - 10 tuntia 0,5 tunnin välein. Aktivointi tapahtuu painamalla painiketta yli 1 sekunnin ajan (1x välähdys), deaktivointi painamalla painiketta yli 2 sekuntia.';

  @override
  String get generalTextNobuttonpermanentlight => 'Ei käytössä';

  @override
  String get generalTextBasicsettings => 'Perusasetukset';

  @override
  String get generalTextInputswitch => 'Painiketulo (A1)';

  @override
  String get generalTextOperationmode => 'Käyttötila';

  @override
  String get generalTextDimvalue => 'Käyttäytyminen päälle kytkettäessä';

  @override
  String get eud12TitleUsememory => 'Käytä muistiarvoa';

  @override
  String get eud12DescriptionUsememory =>
      'Muistiarvo vastaa aina viimeisimmäksi asetettua himmennysarvoa. Jos muistiarvo on pois käytöstä, himmennys asetetaan aina päällekytkentäarvoon.';

  @override
  String get generalTextStartup => 'Päällekytkentäkirkkaus';

  @override
  String get generalDescriptionSwitchonvalue =>
      'Päällekytkentäarvo on säädettävä kirkkausarvo, joka takaa turvallisen päällekytkennän.';

  @override
  String get generalTitleSwitchontime => 'Päällekytkentäaika';

  @override
  String get generalDescriptionSwitchontime =>
      'Kun päällekytkentäaika on kulunut loppuun, lamppu himmenee päällekytkentäarvosta muistiarvoon.';

  @override
  String get generalDescriptionStartup =>
      'Jotkin LED-lamput vaativat suuremman käynnistysvirran kytkeytyäkseen aina luotettavasti päälle. Lamppu kytkeytyy päälle tällä kytkentäarvolla ja himmenee muistiin jäävään arvoon heti kytkentäajan jälkeen.';

  @override
  String get eud12ClockmodeSubtitleProgramze =>
      'Lyhyt klikkaus Keskusohjaus ON';

  @override
  String get eud12ClockmodeSubtitleProgramza =>
      'Lyhyt klikkaus Keskusohjaus OFF';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Tuplapainallus yleispainikkeen/suuntapainikkeen ON puolelta';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Tuplapainallus yleispainikkeen/suuntapainikkeen OFF puolelta';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Porrasvaloautomaatti';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Ajastin, jossa on säädettävä päällekytkentä ja poiskytkentäsaika 0,5 sekunnista 9,9 minuuttiin. Kirkkautta voidaan säätää minimikirkkaudesta maksimikirkkauteen.';

  @override
  String get eud12FunctionAutoDescription =>
      'Yleishimmennin, jossa on asetukset liiketunnistimelle, valoherätys- ja torkkutoiminnolle.';

  @override
  String get eud12FunctionErDescription =>
      'Välirele, kirkkaus voidaan asettaa minimikirkkaudesta maksimikirkkauteen.';

  @override
  String get eud12FunctionEsvDescription =>
      'Yleishimmennin, jossa voidaan asettaa poiskytkentäviive 1-120 minuuttiin. Poiskytkennän varoitus ajan loppuessa himmentämällä alaspäin, jonka aika säädettävissä 1-3 minuuttiin. Molemmat keskusohjaustulot ovat aktiivisia.';

  @override
  String get eud12FunctionTlzDescription =>
      'Viiveajastin painikkeella, valaistuksen keston asettaminen 0-10 tunnin välillä, 0,5 tunnin askelin. Aktivointi painamalla painiketta yli 1 sekunnin ajan (1x välähdys), deaktivointi painamalla painiketta yli 2 sekunnin ajan.';

  @override
  String get eud12FunctionMinDescription =>
      'Yleishimmennin joka kytkeytyy päälle asetettuun minimikirkkauteen, kun ohjausjännite kytketään. Valo kirkastuu maksimikirkkauteen asetetun himmennysajan (1-120 minuuttia) kuluessa. Kun ohjausjännite katkaistaan, valo sammuu välittömästi, myös asetetun himmennysajan aikana. Molemmat keskusohjaustulot ovat aktiivisia.';

  @override
  String get eud12FunctionMmxDescription =>
      'Yleishimmennin, joka kytkeytyy asetettuun minimikirkkauteen, kun ohjausjännite kytketään. Valo kirkastuu maksimikirkkauteen asetetun himmennysajan (1-120 minuuttia) kuluessa. Kun ohjausjännite katkaistaan, himmennin himmenee asetettuun minimikirkkauteen. Sen jälkeen se kytkeytyy pois päältä. Molemmat keskusohjaustulot ovat aktiivisia.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Liiketunnistimen ollessa pois päältä';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Liiketunnistimen ollessa pois päältä';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Kytkentäohjelma aktivoitu ja deaktivoitu BWM:n ollessa pois päältä';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Keskusohjaus ON aktivoi liiketunnistimen, Keskusohjaus OFF deaktivoi liiketunnistimen, samoin kuin kytkentäohjelmissa';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Liiketunnistin kytkee vain pois päältä';

  @override
  String get detailsDimsectionHeader => 'Himmennys';

  @override
  String get generalTextFast => 'Nopea';

  @override
  String get generalTextSlow => 'Hidas';

  @override
  String get eud12TextDimspeed => 'Himmennyksen nopeus';

  @override
  String get eud12TextSwitchonspeed => 'Päällekytkennän nopeus';

  @override
  String get eud12TextSwitchoffspeed => 'Poiskytkennän nopeus';

  @override
  String get eud12DescriptionDimspeed =>
      'Himmennysnopeus on nopeus, jolla himmennin himmentää nykyisestä kirkkaustasosta tavoitekirkkauteen.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'Päällekytkentänopeus on nopeus, jonka himmennin tarvitsee kytkeytyäkseen kokonaan päälle.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'Poiskytkentänopeus on nopeus, jonka himmennin tarvitsee sammukseen kokonaan.';

  @override
  String get settingsFactoryresetResetdimHeader =>
      'Himmennysasetusten nollaaminen';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Haluatko todella nollata kaikki himmennysasetukset?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Himmennysasetukset on nyt onnistuneesti nollattu';

  @override
  String get eud12TextSwitchonoffspeed => 'ON/OFF-nopeus';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'Päälle/poiskytkentänopeus on nopeus, jolla himmennin kytkee valaistuksen päälle tai pois päältä kokonaan.';

  @override
  String get timerDetailsDimtoval => 'Päälle himmennysarvolla %';

  @override
  String get timerDetailsDimtovalDescription =>
      'Himmennin kytkeytyy aina päälle kiinteällä himmennysarvolla (%).';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Kytke päälle $brightness%:lla';
  }

  @override
  String get timerDetailsDimtomem => 'Päälle muistiarvolla';

  @override
  String get timerDetailsDimtomemSubtitle => 'Päällekytkentä muistiarvolla';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Jäännöskirkkaus (BWM) ON';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Jäännöskirkkaus (BWM) OFF';

  @override
  String get settingsRandommodeHint =>
      'Kun satunnaistila on aktivoituna, kaikkien tämän kanavan ohjelmien kytkentää siirretään satunnaisesti enintään 15 minuuttilla (läsnäolon simulointi). ON-ohjelmat siirretään asetettua aikaisemmaksi, OFF-ohjelmat myöhemmäksi.';

  @override
  String get runtimeOffsetDescription =>
      'Ylimääräinen ylitys, kun aika on kulunut loppuun';

  @override
  String get loadingTextDimvalue => 'Himmennysarvo on ladattu';

  @override
  String get discoveryEudipmDescription => 'Yleishimmennin, IP, Matter';

  @override
  String get generalTextOffset => 'Ylitys';

  @override
  String get eud12DimvalueTestText => 'Kokeile kirkkausarvoa';

  @override
  String get eud12DimvalueTestDescription =>
      'Testauksessa otetaan huomioon myös tällä hetkellä asetettu himmennysnopeus.';

  @override
  String get eud12DimvalueLoadText => 'Lataa kirkkausarvo';

  @override
  String get settingsDatetimeNotime =>
      'Päivämäärän ja kellonajan asetukset on luettava laitteen näytöstä.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Opeta Matter-laitteesi käyttäen jotakin seuraavista sovelluksista.';

  @override
  String get generalMatterOpengooglehome => 'Avaa Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Avaa Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Avaa SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Ohjelma $number';
  }

  @override
  String get generalTextDone => 'Valmis';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Kun satunnaistila on aktivoituna, kaikkien tämän kanavan ohjelmien kytkentää siirretään satunnaisesti enintään 15 minuuttilla (läsnäolon simulointi). ON-ohjelmat siirretään asetettua aikaisemmaksi, OFF-ohjelmat myöhemmäksi.';

  @override
  String get all => 'Kaikki';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Onnistui';

  @override
  String get error => 'Virhe';

  @override
  String get timeProgramAdd => 'Lisää aikaohjelma';

  @override
  String get noConnection => 'Ei yhteyttä';

  @override
  String get timeProgramOnlyActive => 'Määritetyt ohjelmat';

  @override
  String get timeProgramAll => 'Kaikki ohjelmat';

  @override
  String get active => 'Aktiivinen';

  @override
  String get inactive => 'Inaktiivinen';

  @override
  String timeProgramSaved(Object number) {
    return 'Aikaohjelma $number tallennettu';
  }

  @override
  String get deviceLanguageSaved => 'Laitteen kieli tallennettu';

  @override
  String generalTextTimeShort(Object time) {
    return '$time';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Pitääkö ohjelma $index todella poistaa?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Millisekuntia',
      one: 'Millisekunti',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Millisekuntia',
      one: '$count Millisekunti',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Sekuntia',
      one: '$count Sekunti',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Minuutit',
      one: '$count Minuutti',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Tunnit',
      one: '$count Tunti',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'PIN-koodi ei saa olla tyhjä';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'Skannattu koodi ei vastaa laitetta';

  @override
  String get wifiAuthorizationPopIsEmpty => 'PoP ei voi olla tyhjä';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Koska sovellus ei pääse käsiksi yksityiseen Wi-Fi-salasanaasi, ei ole mahdollista tarkistaa syötteen oikeellisuutta. Jos yhteyttä ei muodostu, tarkista salasana ja syötä se uudelleen.';

  @override
  String get generalMatterOpenApplehome => 'Avaa Apple Home';

  @override
  String get timeProgramNoActive => 'Ei määritettyjä ohjelmia';

  @override
  String get timeProgramNoEmpty => 'Vapaata aikaohjelmaa ei ole saatavilla';

  @override
  String get nameOfConfiguration => 'Konfiguraation nimi';

  @override
  String get currentDevice => 'Nykyinen laite';

  @override
  String get export => 'Vie';

  @override
  String get import => 'Tuo';

  @override
  String get savedConfigurations => 'Tallennetut konfiguraatiot';

  @override
  String get importableServicesLabel => 'Seuraavat asetukset voidaan tuoda:';

  @override
  String get notImportableServicesLabel => 'Yhteensopimattomat asetukset';

  @override
  String get deviceCategoryMeterGateway => 'Mittari yhdyskäytävä';

  @override
  String get deviceCategory2ChannelTimeSwitch => '2-kanavainen kellokytkin';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Bluetooth kellokytkin ulkokäyttöön';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Säädä tiedonsiirtonopeutta, pariteettia ja aikakatkaisua lähetysnopeuden, virheiden havaitsemisen ja odotusajan määrittämiseksi.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Tiedonsiirtonopeus';

  @override
  String get settingsModbusParity => 'Pariteetti';

  @override
  String get settingsModbusTimeout => 'Modbus-aikakatkaisu';

  @override
  String get locationServiceDisabled => 'Sijainti poistettu käytöstä';

  @override
  String get locationPermissionDenied =>
      'Anna sijainnille lupa pyytää nykyistä sijaintiasi.';

  @override
  String get locationPermissionDeniedPermanently =>
      'Sijaintioikeudet on estetty pysyvästi, salli sijaintilupa laitteesi asetuksissa nykyisen sijaintisi määrittämistä varten.';

  @override
  String get lastSync => 'Viimeisin synkronointi';

  @override
  String get dhcpActive => 'DHCP aktiivinen';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Aliverkon peite';

  @override
  String get standardGateway => 'Oletusyhdyskäytävä';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'Vaihtoehtoinen DNS';

  @override
  String get errorNoNetworksFound => 'WiFi-verkkoa ei löydy';

  @override
  String get availableNetworks => 'Käytettävissä olevat verkot';

  @override
  String get enableWifiInterface => 'Ota WiFi-liitäntä käyttöön';

  @override
  String get enableLANInterface => 'Ota LAN-liitäntä käyttöön';

  @override
  String get hintDontDisableAllInterfaces =>
      'Varmista, että kaikkia liitäntöjä ei ole poistettu käytöstä. Viimeksi aktivoitu käyttöliittymä on etusijalla.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'Etsi verkkoja';

  @override
  String get errorNoNetworkEnabled =>
      'Vähintään yhden aseman on oltava aktiivinen';

  @override
  String get errorActiveNetworkInvalid =>
      'Kaikki aktiiviset asemat eivät ole kelvollisia';

  @override
  String get invalidNetworkConfiguration => 'Virheellinen verkkomääritys';

  @override
  String get generalDefault => 'Oletus';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Yhdistetty MQTT-välittäjään';

  @override
  String get mqttDisconnected => 'Ei yhteyttä MQTT-välittäjään';

  @override
  String get mqttBrokerURI => 'Välittäjän URI';

  @override
  String get mqttBrokerURIHint => 'MQTT-välittäjän URI';

  @override
  String get mqttPort => 'Portti';

  @override
  String get mqttPortHint => 'MQTT-portti';

  @override
  String get mqttClientId => 'Asiakas-ID';

  @override
  String get mqttClientIdHint => 'MQTT Asiakas-ID';

  @override
  String get mqttUsername => 'Käyttäjätunnus';

  @override
  String get mqttUsernameHint => 'MQTT käyttäjätunnus';

  @override
  String get mqttPassword => 'Salasana';

  @override
  String get mqttPasswordHint => 'MQTT salasana';

  @override
  String get mqttCertificate => 'Sertifikaatti';

  @override
  String get mqttCertificateHint => 'MQTT sertifikaatti';

  @override
  String get mqttTopic => 'Aihe';

  @override
  String get mqttTopicHint => 'MQTT aihe';

  @override
  String get electricityMeter => 'Sähkömittari';

  @override
  String get electricityMeterCurrent => 'Nykyinen';

  @override
  String get electricityMeterHistory => 'Historia';

  @override
  String get electricityMeterReading => 'Mittarin lukema';

  @override
  String get connectivity => 'Yhdistettävyys';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Energiamittarit',
      one: 'Energiamittari',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description =>
      'Modbus-Energia-Mittarit-MQTT-Gateway';

  @override
  String get bluetoothConnectionLost => 'Bluetooth-yhteys katkesi';

  @override
  String get bluetoothConnectionLostDescription =>
      'Bluetooth-yhteys laitteeseen on katkennut. Tarkista yhteys laitteeseen.';

  @override
  String get openBluetoothSettings => 'Avaa Bluetooth-asetukset';

  @override
  String get password => 'Salasana';

  @override
  String get setInitialPassword => 'Aseta alkuperäinen salasana';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'Salasanan on oltava vähintään $length merkkiä pitkä';
  }

  @override
  String get repeatPassword => 'Toista salasana';

  @override
  String get passwordsDoNotMatch => 'Salasanat eivät täsmää';

  @override
  String get savePassword => 'Tallenna salasana';

  @override
  String get savePasswordHint =>
      'Salasana tallennetaan laitteen tulevia yhteyksiä varten.';

  @override
  String get retrieveNtpServer => 'Hae aika NTP-palvelimelta';

  @override
  String get retrieveNtpServerFailed =>
      'Yhteyttä NTP-palvelimeen ei saatu muodostettua.';

  @override
  String get retrieveNtpServerSuccess => 'Yhteys NTP-palvelimeen onnistui.';

  @override
  String get settingsPasswordNewPasswordDescription => 'Anna uusi salasana';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Salasanan vaihto onnistui';

  @override
  String get dhcpRangeStart => 'DHCP-alueen alku';

  @override
  String get dhcpRangeEnd => 'DHCP-alueen loppu';

  @override
  String get forwardOnMQTT => 'Välitä eteenpäin MQTT:hen';

  @override
  String get showAll => 'Näytä kaikki';

  @override
  String get hide => 'Piilota';

  @override
  String get changeToAPMode => 'Vaihda AP-tilaan';

  @override
  String get changeToAPModeDescription =>
      'Olet liittämässä laitetta WiFi-verkkoon, jolloin yhteys laitteeseen katkeaa ja sinun on muodostettava yhteys laitteeseen uudelleen määritetyn verkon kautta.';

  @override
  String get consumption => 'Kulutus';

  @override
  String get currentDay => 'Nykyinen päivä';

  @override
  String get twoWeeks => '2 viikkoa';

  @override
  String get oneYear => '1 vuosi';

  @override
  String get threeYears => '3 vuotta';

  @override
  String passwordMinLength(Object length) {
    return 'Salasanan on oltava vähintään $length merkkiä.';
  }

  @override
  String get passwordNeedsLetter => 'Salasanan on sisällettävä kirjain.';

  @override
  String get passwordNeedsNumber => 'Salasanan on sisällettävä numero.';

  @override
  String get portEmpty => 'Portti ei voi olla tyhjä';

  @override
  String get portInvalid => 'Väärä portti';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'Portin on oltava välillä $rangeStart ja $rangeEnd';
  }

  @override
  String get ipAddressEmpty => 'IP-osoite ei voi olla tyhjä';

  @override
  String get ipAddressInvalid => 'Virheellinen IP-osoite';

  @override
  String get subnetMaskEmpty => 'Aliverkon peite ei voi olla tyhjä';

  @override
  String get subnetMaskInvalid => 'Virheellinen aliverkon peite';

  @override
  String get gatewayEmpty => 'Gateway ei voi olla tyhjä';

  @override
  String get gatewayInvalid => 'Virheellinen yhdyskäytävä';

  @override
  String get dnsEmpty => 'DNS ei voi olla tyhjä';

  @override
  String get dnsInvalid => 'Virheellinen DNS';

  @override
  String get uriEmpty => 'URI ei voi olla tyhjä';

  @override
  String get uriInvalid => 'Virheellinen URI';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Sähkömittari vaihdettu onnistuneesti';

  @override
  String get networkChangedSuccessfully =>
      'Verkkoasetusten muuttaminen onnistui';

  @override
  String get mqttChangedSuccessfully => 'MQTT-asetusten muuttaminen onnistui';

  @override
  String get modbusChangedSuccessfully =>
      'Modbus-asetusten muuttaminen onnistui';

  @override
  String get loginData => 'Poista kirjautumistiedot';

  @override
  String get valueConfigured => 'Määritetty';

  @override
  String get electricityMeterHistoryNoData => 'Tietoja ei ole saatavilla';

  @override
  String get locationChangedSuccessfully => 'Sijainti muutettu onnistuneesti';

  @override
  String get settingsNameFailEmpty => 'Nimi ei voi olla tyhjä';

  @override
  String settingsNameFailLength(Object length) {
    return 'Nimi ei saa olla pitdempi kuin $length merkkiä';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Päivänseisaus asetusten muuttaminen onnistui';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Rele-toiminto muutettu onnistuneesti';

  @override
  String get relayFunctionHeader => 'Reletoiminto';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Käyttäytyminen päälle kytkettäessä muutettu onnistuneesti';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Himmennystoiminnot muutettu onnistuneesti';

  @override
  String get dimmerBrightnessDescription =>
      'Minimi- ja maksimikirkkaus asetus vaikuttaa kaikkiin himmentimen säädettäviin kirkkauksiin.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Perusasetukset muutettu onnistuneesti';

  @override
  String get liveUpdateEnabled => 'Live-testi käytössä';

  @override
  String get liveUpdateDisabled => 'Live-testi pois käytöstä';

  @override
  String get liveUpdateDescription =>
      'Viimeksi muutettu liukusäätimen arvo lähetetään laitteeseen.';

  @override
  String get demoDevices => 'Demolaitteet';

  @override
  String get showDemoDevices => 'Näytä demolaitteet';

  @override
  String get deviceCategoryTimeSwitch => 'Kellokytkin';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Monitoimirele';

  @override
  String get deviceCategoryDimmer => 'Himmennin';

  @override
  String get deviceCategoryShutter => 'Kaihtimet';

  @override
  String get deviceCategoryRelay => 'Rele';

  @override
  String get search => 'Etsi';

  @override
  String get configurationsHeader => 'Konfiguraatiot';

  @override
  String get configurationsDescription => 'Hallitse konfiguraatioitasi täällä.';

  @override
  String get configurationsNameFailEmpty =>
      'Konfiguraation nimi ei voi olla tyhjä';

  @override
  String get configurationDeleted => 'Konfiguraatio poistettu';

  @override
  String codeFound(Object codeType) {
    return '$codeType koodi löydetty';
  }

  @override
  String get errorCameraPermission =>
      'Anna kameralle lupa ELTAKO-koodin skannaamiseen.';

  @override
  String get authorizationSuccessful => 'Laitteen valtuutus onnistui';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'Laite on nyt valmis uutta valtuutusta varten.';

  @override
  String get settingsResetConnectionHeader => 'Resetoi yhteys';

  @override
  String get settingsResetConnectionDescription =>
      'Haluatko todella resetoida yhteyden?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'Yhteys on onnistuneesti resetoitu.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Kytkentä käyttäytiminen muutettu onnistuneesti';

  @override
  String get runtimeChangedSuccesfully =>
      'Käyttöaika toiminta muutettu onnistuneesti';

  @override
  String get expertModeActivated => 'Expert-tila käytössä';

  @override
  String get expertModeDeactivated => 'Expert-tila poistettu käytöstä';

  @override
  String get license => 'Lisenssi';

  @override
  String get retry => 'Uudelleenyritys';

  @override
  String get provisioningConnectingHint =>
      'Laiteyhteyttä muodostetaan. Tämä voi kestää 1 minuutin.';

  @override
  String get serialnumberEmpty => 'Sarjanumero ei voi olla tyhjä';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Bluetooth ei ole käytössä, ota se käyttöön Bluetooth-laitteiden löytämiseksi.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Bluetooth-käyttöoikeuksia ei ole annettu.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Bluetooth-käyttöoikeuksia ei ole annettu. Ota ne käyttöön laitteen asetuksissa.';

  @override
  String get requestPermission => 'Pyydä lupaa';

  @override
  String get goToSettings => 'Siirry asetuksiin';

  @override
  String get enableBluetooth => 'Ota bluetooth käyttöön';

  @override
  String get installed => 'Asennettu';

  @override
  String teachInDialogDescription(Object type) {
    return 'Haluatko opettaa laitteesi $type?';
  }

  @override
  String get useMatter => 'Käytä Matteria';

  @override
  String get relayMode => 'Aktivoi rele-tila';

  @override
  String get whatsNew => 'Uutta tässä versiossa';

  @override
  String get migrationHint =>
      'Uusien ominaisuuksien käyttäminen edellyttää siirtymistä.';

  @override
  String get migrationHeader => 'Siirtyminen';

  @override
  String get migrationProgress => 'Siirtyminen käynnissä...';

  @override
  String get letsGo => 'Mennään!';

  @override
  String get noDevicesFound =>
      'Laitteita ei löytynyt. Tarkista, onko laite paritustilassa.';

  @override
  String get interfaceStateEmpty => 'Laitteita ei löytynyt';

  @override
  String get ssidEmpty => 'SSID ei voi olla tyhjä';

  @override
  String get passwordEmpty => 'Salasana ei voi olla tyhjä';

  @override
  String get settingsDeleteSettingsHeader => 'Asetusten nollaus';

  @override
  String get settingsDeleteSettingsDescription =>
      'Haluatko varmasti nollata kaikki asetukset?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Kaikki asetukset on onnistuneesti nollattu.';

  @override
  String get locationNotFound => 'Sijaintia ei löydy';

  @override
  String get timerProgramEmptySaveHint =>
      'Aikaohjelma on tyhjä. Haluatko peruuttaa muokkauksen?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Viikonpäiviä ei ole valittu. Haluatko kuitenkin tallentaa aikaohjelman?';

  @override
  String get timeProgramNoDays => 'Vähintään yksi viikonpäivä on aktivoitava';

  @override
  String timeProgramColliding(Object program) {
    return 'Aikaohjelma on ristiriidassa ohjelmaan $program';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Aikaohjelma on kopio ohjelmasta $program';
  }

  @override
  String get screenshotZgw16 => 'Omakotitalo';

  @override
  String get interfaceStateUnknown => 'Laitteita ei löytynyt';

  @override
  String get settingsPinChange => 'Vaihda PIN-koodi';

  @override
  String get timeProgrammOneTime => 'kertaluontoinen';

  @override
  String get timeProgrammRepeating => 'Toistuva';

  @override
  String get generalIgnore => 'Älä huomioi';

  @override
  String get timeProgramChooseDay => 'Valitse päivä';

  @override
  String get generalToday => 'Tänään';

  @override
  String get generalTomorrow => 'Huomenna';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth ja PIN-koodi vaihdettu onnistuneesti';

  @override
  String get generalTextDimTime => 'Himmennysaika';

  @override
  String get discoverySu62Description => '1-kanavainen Bluetooth kellokytkin';

  @override
  String get bluetoothAlwaysOnTitle => 'Aina päällä';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Bluetooth on jatkuvasti käytössä.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Huomio: Jos tämä asetus on aktivoituna, laite on jatkuvasti kaikkien nähtävissä Bluetoothin kautta! Tällöin on suositeltavaa vaihtaa oletus PIN-koodi.';

  @override
  String get bluetoothManualStartupOnTitle => 'Väliaikaisesti päälle';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Kun virta kytketään, Bluetooth aktivoituu 3 minuutiksi.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Huomio: Pariliitoksen valmiustila aktivoituu 3 minuutiksi ja sammuu sen jälkeen. Jos halutaan muodostaa uusi yhteys, painiketta on pidettävä painettuna noin 5 sekuntia.';

  @override
  String get bluetoothManualStartupOffTitle => 'Manuaalinen käynnistys';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Bluetooth aktivoidaan manuaalisesti painiketuloon kytketyllä painikkeella.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Huomio: Bluetoothin aktivoimista varten, painiketuloon kytkettyä painiketta tulee pitää painettuna noin 5 sekunnin ajan.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'Ohjelmat voidaan joko suorittaa toistuvasti suorittamalla kytkentä aina määritettyinä päivinä ja kellonaikoina, tai ne voidaan suorittaa vain kertaalleen määritettynä kytkentäaikana.';

  @override
  String versionHeader(Object version) {
    return 'Versio $version';
  }

  @override
  String get releaseNotesHeader => 'Julkaisutiedot';

  @override
  String get release30Header => 'Uusi ELTAKO Connect -appi on nyt täällä!';

  @override
  String get release30FeatureDesignHeader => 'Uusi ulkoasu';

  @override
  String get release30FeatureDesignDescription =>
      'Sovellus on täysin uudistettu ja se on saanut uuden ulkoasun. Sen käyttö on nyt entistä helpompaa ja intuitiivisempaa.';

  @override
  String get release30FeaturePerformanceHeader => 'Parannettu suorituskyky';

  @override
  String get release30FeaturePerformanceDescription =>
      'Nauti sujuvammasta käyttökokemuksesta ja lyhentyneistä latausajoista - parempaa käyttökokemusta ajatellen.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Laitteiden väliset kokoonpanot';

  @override
  String get release30FeatureConfigurationDescription =>
      'Tallenna laitekokoonpanot ja siirrä ne muihin laitteisiin. Laitteiden ei tarvitse olla samanlaisia, vaan voit esimerkiksi siirtää S2U12DBT1+1-UC-laitteen kokoonpanon ja asetukset ASSU-BT-laitteeseen tai päinvastoin.';

  @override
  String get release31Header =>
      'Uusi uppoasennettava 1-kanavainen Bluetooth astro-kellokytkin on nyt täällä!';

  @override
  String get release31Description => 'Mitä SU62PF-BT/UC:llä voi tehdä?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Jopa 60 eri aikaohjelmaa.';

  @override
  String get release31DeviceNote2 =>
      'Astro-toiminto: Kellokytkin kytkee haluamasi laitteet aina auringonnousun ja -laskun mukaan.';

  @override
  String get release31DeviceNote3 =>
      'Satunnaistila: Kytkentäikoja muutetaan satunnaisesti 15 minuutilla, simuloiden läsnäoloa.';

  @override
  String get release31DeviceNote4 =>
      'Kesä-/talviajan vaihto: Kellokytkin siirtyy aina automaattisesti kesä- tai talviaikaan.';

  @override
  String get release31DeviceNote5 =>
      'Yleinen syöttö- ja ohjausjännite 12-230V UC.';

  @override
  String get release31DeviceNote6 =>
      'Painiketulo myös manuaalista kytkentää varten.';

  @override
  String get release31DeviceNote7 =>
      '1 sulkeutuva (NO) potentiaalivapaa kosketin 10 A/250 V AC.';

  @override
  String get release31DeviceNote8 =>
      'Aikaohjelmien kertaluonteinen suorittaminen.';

  @override
  String get generalNew => 'Uusi';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count vuotta sitten',
      one: 'Viime vuonna',
    );
    return '$_temp0';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count kuukautta sitten',
      one: 'Viime kuussa',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count viikkoa sitten',
      one: 'Viime viikolla',
    );
    return '$_temp0';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count päivää sitten',
      one: 'Eilen',
    );
    return '$_temp0';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minuuttia sitten',
      one: 'Minuutti sitten',
    );
    return '$_temp0';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count tuntia sitten',
      one: 'Tunti sitten',
    );
    return '$_temp0';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sekuntia sitten',
      one: 'sekunti sitten',
    );
    return '$_temp0';
  }

  @override
  String get justNow => 'Tällä hetkellä';

  @override
  String get discoveryEsripmDescription => 'Impulssirele IP Matter';

  @override
  String get generalTextKidsRoom => 'Lastenhuonetoiminto';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Jos valo sytytetään pitäen painiketta painettuna pitkään ($mode), valaistus syttyy minimitasolla noin 1 sekunnin kuluttua ja kirkastuu hitaasti niin kauan kuin painiketta pidetään painettuna ilman, että viimeksi tallentunut valaistustaso muuttuu.';
  }

  @override
  String get generalTextSceneButton => 'Tilannepainike';

  @override
  String get settingsEnOceanConfigHeader => 'EnOcean-signaalivahvistin';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'EnOcean-konfiguraatio muutettu onnistuneesti';

  @override
  String get activateEnOceanRepeater => 'Aktivoi EnOcean-vahvistin';

  @override
  String get enOceanRepeaterLevel => 'Vahvistimen toimintataso';

  @override
  String get enOceanRepeaterLevel1 => '1-tasoinen';

  @override
  String get enOceanRepeaterLevel2 => '2-tasoinen';

  @override
  String get enOceanRepeaterOffDescription =>
      'Langattomia signaaleja antureilta ei vastaanoteta.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Vain signaalit kaikilta langattomilta painikkeilta/antureilta vastaanotetaan, tarkistetaan ja lähetetään eteenpäin täydellä lähetysteholla. Muiden vahvistimen toistamat langattomat signaalit jätetään huomioimatta, tiedon määrän vähentämiseksi.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Langattomien painikkeiden/anturien signaalien lisäksi, myös 1-tasoisen vahvistimen langattomat signaalit huomioidaan ja vahvistetaan. Vahvistin pystyy vastaanottamaan ja vahvistamaan signaalin siis enintään kaksi kertaa, 1-tason vahvistimelta 2-tason vahvistimelle. \nLangattomia vahvistimia ei tarvitse erikseen opettaa. Ne aina vastaanottavat ja vahvistavat kaikkien langattomien painikkeiden/antureiden lähettämiä langattomia signaaleja kantama alueensa sisällä.';

  @override
  String get settingsSensorHeader => 'Anturit ja painikkeet';

  @override
  String get sensorChangedSuccessfully => 'Anturit vaihdettu onnistuneesti';

  @override
  String get wiredButton => 'Johdotettu painike';

  @override
  String get enOceanId => 'EnOcean-ID';

  @override
  String get enOceanAddManually => 'Syötä tai skannaa EnOcean-ID';

  @override
  String get enOceanIdInvalid => 'Virheellinen EnOcean-ID';

  @override
  String get enOceanAddAutomatically => 'Opeta EnOcean viestillä';

  @override
  String get enOceanAddDescription =>
      'Langaton EnOcean-protokolla mahdollistaa langattomien painikkeiden opettamisen ja niiden käytön toimilaitteessasi.\n\nValitse joko automaattinen opettaminen, jolloin voit opettaa painikkeet vain painikkeen painalluksella, tai valitse manuaalinen vaihtoehto, jolloin voit skannata tai kirjoittaa painikkeen EnOcean-ID tunnuksen.';

  @override
  String get enOceanTelegram => 'Viesti';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Syötä $sensorType EnOcean-ID -koodi tai skannaa $sensorType EnOcean-QR -koodi lisätäksesi sen.';
  }

  @override
  String get enOceanCode => 'EnOcean QR-koodi';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Etsi EnOcean-koodi $sensorType-anturistasi ja skannaa se kamerallasi.';
  }

  @override
  String get enOceanButton => 'EnOcean-painike';

  @override
  String get enOceanBackpack => 'EnOcean-adapteri';

  @override
  String get sensorNotAvailable => 'Antureita ei ole vielä paritettu';

  @override
  String get sensorAdd => 'Lisää antureita';

  @override
  String get sensorCancel => 'Peruuta opettaminen';

  @override
  String get sensorCancelDescription =>
      'Haluatteko todella peruuttaa painikkeen opetuksen?';

  @override
  String get getEnOceanBackpack => 'Tilaa langaton EnOcean plug-in adapteri';

  @override
  String get enOceanBackpackMissing =>
      'Jotta pääset täydellisen liitettävyyden ja kommunikaation fantastiseen maailmaan, tarvitset EnOcean-sovittimen.\nKlikkaa tästä saadaksesi lisätietoa';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName muutettu onnistuneesti';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'yhdistetty $deviceName kautta';
  }

  @override
  String get lastSeen => 'Viimeksi havaittu';

  @override
  String get setButtonOrientation => 'Aseta suunta';

  @override
  String get setButtonType => 'Aseta painiketyyppi';

  @override
  String get button1Way => '1-kanavainen painike';

  @override
  String get button2Way => '2-kanavainen painike';

  @override
  String get button4Way => '4-kanavainen painike';

  @override
  String get buttonUnset => 'ei asetettu';

  @override
  String get button => 'Painike';

  @override
  String get sensor => 'Anturi';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count anturia löydetty',
      one: '1 anturi löydetty',
      zero: 'Antureita ei löydetty',
    );
    return '$_temp0';
  }

  @override
  String get sensorSearch => 'Etsi antureita';

  @override
  String get searchAgain => 'Etsi uudelleen';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return 'Opeta $sensorType';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return 'Valitse $sensorType';
  }

  @override
  String get sensorChooseDescription => 'Valitse painike, jonka haluat opettaa';

  @override
  String get sensorCategoryDescription =>
      'Valitse lisättävän anturin kategoria.';

  @override
  String get sensorName => 'Painikkeen nimi';

  @override
  String get sensorNameFooter => 'Nimeä painikkeesi';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName lisättiin onnistuneesti';
  }

  @override
  String sensorDelete(Object sensorType) {
    return 'Poista $sensorType';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Haluatko todella poistaa $sensorType $sensorName?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName poistettiin onnistuneesti';
  }

  @override
  String get buttonTapDescription => 'Napauta painiketta, jonka haluat lisätä.';

  @override
  String get waitingForTelegram => 'Toimilaite odottaa signaalia.';

  @override
  String get copied => 'Kopioitu';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType on jo paritettu';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Yleispainikkeella himmennyksen suunnan vaihto tapahtuu vapauttamalla painike hetkeksi. Päälle/poiskytkentä tapahtuu lyhyellä painalluksella.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Suuntapainikkeessa on vivun yläreunassa \"päällekytkentä ja ylös himmennys\" ja alareunassa \"poiskytkentä ja alas himmennys\".';

  @override
  String get matterForwardingDescription =>
      'Matter viestien välittäminen eteenpäin';

  @override
  String get none => 'Ei yhtään';

  @override
  String get buttonNoneDescription => 'Painikkeella ei ole toimintoja';

  @override
  String get buttonUnsetDescription =>
      'Painikkeelle ei ole määritelty käyttäytymistä';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Painikkeen tyyppi on vaihdettu onnistuneesti';

  @override
  String forExample(Object example) {
    return 'esim. $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Mahdollista vain tuotantoviikosta 44/20 alkaen';

  @override
  String get input => 'Tulo';

  @override
  String get buttonSceneValueOverride => 'Korvaa tilannepainikkeen arvo';

  @override
  String get buttonSceneValueOverrideDescription =>
      'Tilannepainikkeen arvo korvataan nykyisellä himmennysarvolla painikkeen pitkällä painalluksella.';

  @override
  String get buttonSceneDescription =>
      'Tilannepainike kytkee päälle tietyllä himmennysarvolla.';

  @override
  String get buttonPress => 'Painike painettuna';

  @override
  String get triggerOn =>
      'Yleispainike tai suuntapainike painettuna päällekytkentä puolelta.';

  @override
  String get triggerOff =>
      'Yleispainike tai suuntapainike painettuna poiskytkentä puolelta.';

  @override
  String get centralOn => 'Keskusohjaus ON';

  @override
  String get centralOff => 'Keskusohjaus OFF';

  @override
  String get centralButton => 'Keskusohjauspainike';

  @override
  String get enOceanAdapterNotFound => 'EnOcean-adapteria ei löydetty';

  @override
  String get updateRequired => 'Päivitys tarvitaan';

  @override
  String get updateRequiredDescription =>
      'Sovelluksesi tarvitsee päivityksen, jotta se tukee tätä uutta laitetta.';

  @override
  String get release32Header =>
      'Uusi 64-sarja jossa on Matter ja EnOcean, sekä uusi 62-sarjan uppoasennettava Bluetooth-astrokellokytkin, ovat nyt saatavilla!';

  @override
  String get release32EUD64Header =>
      'Uusi uppoasennettava 1-kanavainen jopa 300W himmennin, jossa on Matter Wi-Fi:n kautta, on nyt täällä!';

  @override
  String get release32EUD64Note1 =>
      'Muuta himmennysnopeuden, päälle-/poiskytkentänopeuden, lastenhuone-/torkkutoiminnon ja monien muiden toimintojen asetuksia.';

  @override
  String get release32EUD64Note2 =>
      'EUD64NPN-IPM:n toimintoja voidaan laajentaa plug-in adapterien, kuten langattoman EnOcean plug-in adapterin, EOA64 avulla.';

  @override
  String get release32EUD64Note3 =>
      'Jopa 30 eri langatonta EnOcean-painiketta voidaan yhdistää suoraan EUD64NPN-IPM:ään EnOcean-adapterin EOA64 avulla ja sen avulla ne voidaan myös välittää eteenpäin Matteriin.';

  @override
  String get release32EUD64Note4 =>
      'Laitteen kaksi langallista painiketuloa voidaan yhdistää suoraan EUD64NPN-IPM:ään tai ne voidaan myös välittää eteenpäin Matteriin.';

  @override
  String get release32ESR64Header =>
      'Uusi uppoasennettava 1-kanavainen jopa 16A potentiaalivapaa yleisrele, jossa on Matter Wi-Fi:n kautta, on nyt täällä!';

  @override
  String get release32ESR64Note1 =>
      'Erilaisten toimintojen konfigurointi, kuten sysäysrele (ES), välirele (ER), välirele avautuvalla koskettimella (ER, NO) ja paljon muuta.';

  @override
  String get release32ESR64Note2 =>
      'ESR64PF-IPM:n toimintoja voidaan laajentaa adapterien, kuten langattoman EnOcean plug-in adapterin EOA64, avulla.';

  @override
  String get release32ESR64Note3 =>
      'Jopa 30 eri langatonta EnOcean-painiketta voidaan yhdistää suoraan ESR64PF-IPM:ään langattoman EnOcean-adapterin EOA64 avulla ja sen avulla ne voidaan myös välittää eteenpäin Matteriin.';

  @override
  String get release32ESR64Note4 =>
      'Laitteen yksi langallinen painiketulo voidaan yhdistää suoraan ESR64PF-IPM:ään tai välittää eteenpäin Matteriin.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count painiketta löydetty',
      one: '1 painike löydetty',
      zero: 'Painikkeita ei löydetty',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'kaksoisimpulssilla';

  @override
  String get impulseDescription =>
      'Jos kanava on päällä, se sammutetaan impulssilla.';

  @override
  String get locationServiceEnable => 'Aktivoi sijainti';

  @override
  String get locationServiceDisabledDescription =>
      'Sijainti on poistettu käytöstä. Käyttöjärjestelmäsi tarvitsee sijainnin löytääkseen Bluetooth-laitteet.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Sijaintilupia ei ole myönnetty. Käyttöjärjestelmäsi edellyttää sijaintioikeuksia, jotta Bluetooth-laitteet voidaan löytää. Salli sijaintioikeus laitteesi asetuksissa.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'Lähellä oleville laitteille ei myönnetty lupaa. Ota lupa käyttöön laitteen asetuksissa.';

  @override
  String get permissionNearbyDevices => 'Lähellä olevat laitteet';

  @override
  String get release320Header =>
      'Uusi entistä tehokkaampi yleishimmennin EUD12NPN-BT/600W-230V on nyt saatavissa!';

  @override
  String get release320EUD600Header =>
      'Mitä uudella EUD12NPN-BT/600W voi tehdä?';

  @override
  String get release320EUD600Note1 => 'Yleishimmennin jopa 600W teholla';

  @override
  String get release320EUD600Note2 =>
      'Laajennettavissa LUD12-tehoyksiköllä jopa 3800W:iin asti.';

  @override
  String get release320EUD600Note3 =>
      'Paikallinen käyttö yleispainikkeella tai suuntapainikkeella';

  @override
  String get release320EUD600Note4 =>
      'Keskusohjaustoiminnot Päällä / Pois päältä';

  @override
  String get release320EUD600Note5 => 'Liiketunnistintulo lisää mukavuutta';

  @override
  String get release320EUD600Note6 =>
      'Integroitu kellokytkin, jossa on 10 kytkentäohjelmaa';

  @override
  String get release320EUD600Note7 => 'Astrokello-toiminto';

  @override
  String get release320EUD600Note8 => 'Yksilöllinen päällekytkentäkirkkaus';

  @override
  String get mqttClientCertificate => 'Asiakassertifikaatti';

  @override
  String get mqttClientCertificateHint => 'MQTT-asiakasvarmenne';

  @override
  String get mqttClientKey => 'Asiakasavain';

  @override
  String get mqttClientKeyHint => 'MQTT-asiakasavain';

  @override
  String get mqttClientPassword => 'Asiakas salasana';

  @override
  String get mqttClientPasswordHint => 'MQTT-asiakas salasana';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'Ota HomeAssistant MQTT Discovery käyttöön';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Ota käyttöliittymä käyttöön';

  @override
  String get busAddress => 'Väyläosoite';

  @override
  String busAddressWithAddress(Object index) {
    return 'Väyläosoite $index';
  }

  @override
  String get deviceType => 'Laitteen tyyppi';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Rekisteritaulukko',
      one: 'Rekisteritaulukko',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Nykyiset arvot';

  @override
  String get requestRTU => 'Pyydä RTU';

  @override
  String get requestPriority => 'Pyynnön prioriteetti';

  @override
  String get mqttForwarding => 'Edelleenlähetys MQTT:hen';

  @override
  String get historicData => 'Historiatiedot';

  @override
  String get dataFormat => 'Tietomuoto';

  @override
  String get dataType => 'Tietotyyppi';

  @override
  String get description => 'Kuvaus';

  @override
  String get readWrite => 'Lue/Kirjoita';

  @override
  String get unit => 'Yksikkö';

  @override
  String get registerTableReset => 'Rekisteritaulukon nollaus';

  @override
  String get registerTableResetDescription =>
      'Haluatko todella nollata rekisteritaulukon?';

  @override
  String get notConfigured => 'Ei määritetty';

  @override
  String get release330ZGW16Header => 'Merkittävä päivitys ZGW16WL-IP:lle';

  @override
  String get release330Header =>
      'ZGW16WL-IP yhdessä 16 eri energiamittarin kanssa';

  @override
  String get release330ZGW16Note1 =>
      'Tukee jopa 16 ELTAKO Modbus -energiamittaria.';

  @override
  String get release330ZGW16Note2 => 'Modbus TCP -tuki';

  @override
  String get release330ZGW16Note3 => 'MQTT Discovery -tuki';

  @override
  String get screenshotButtonLivingRoom => 'Olohuoneen painike';

  @override
  String get registerChangedSuccessfully =>
      'Rekisteri on vaihdettu onnistuneesti';

  @override
  String get serverCertificateEmpty => 'Palvelimen varmenne ei voi olla tyhjä';

  @override
  String get registerTemplates => 'Rekisterimallit';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Rekisterimalli muutettu onnistuneesti';

  @override
  String get registerTemplateReset => 'Rekisterimallin nollaus';

  @override
  String get registerTemplateResetDescription =>
      'Haluatko todella nollata rekisterimallin?';

  @override
  String get registerTemplateNotAvailable => 'Rekisterimalleja ei saatavilla';

  @override
  String get rename => 'Nimeä uudelleen';

  @override
  String get registerName => 'Rekisterin nimi';

  @override
  String get registerRenameDescription => 'Anna rekisterille oma nimi';

  @override
  String get restart => 'Käynnistä laite uudelleen';

  @override
  String get restartDescription =>
      'Haluatko todella käynnistää laitteen uudelleen?';

  @override
  String get restartConfirmationDescription => 'Laite käynnistyy nyt uudelleen';

  @override
  String get deleteAllElectricityMeters => 'Poistetaan kaikki energiamittarit';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Haluatko todella poistaa kaikki energiamittarit?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Kaikki energiamittarit on onnistuneesti poistettu';

  @override
  String get resetAllElectricityMeters =>
      'Nollaa kaikki energiamittari kokoonpanot';

  @override
  String get resetAllElectricityMetersDescription =>
      'Haluatko todella nollata kaikki energiamittarin asetukset?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Kaikki energiamittarin asetukset on onnistuneesti nollattu.';

  @override
  String get deleteElectricityMeterHistories =>
      'Poista kaikki energiamittari historiatiedot';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Haluatko todella poistaa kaikki energiamittari historiatiedot?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Kaikki energiamittari historiatiedot on onnistuneesti poistettu';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Laitteesi tukee tällä hetkellä vain yhtä energiamittaria. Päivitä laiteohjelmisto.';

  @override
  String get consumptionWithUnit => 'Käyttö (kWh)';

  @override
  String get exportWithUnit => 'Tuotto (kWh)';

  @override
  String get importWithUnit => 'Kulutus (kWh)';

  @override
  String get resourceWarningHeader => 'Resurssien rajoitukset';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'MQTT:n ja Modbus TCP:n samanaikainen käyttö ei ole mahdollista järjestelmän rajallisten resurssien vuoksi. Poista ensin $protocol käytöstä.';
  }

  @override
  String get mqttEnabled => 'MQTT käytössä';

  @override
  String get redirectMQTT => 'Siirry MQTT-asetuksiin';

  @override
  String get redirectModbus => 'Siirry Modbus-asetuksiin';

  @override
  String get unsupportedSettingDescription =>
      'Nykyisellä laiteohjelmistoversiolla joitakin laitteen asetuksia ei tueta. Päivitä laiteohjelmisto, jotta voit käyttää uusia ominaisuuksia.';

  @override
  String get updateNow => 'Päivitä nyt';

  @override
  String get zgw241Hint =>
      'Tämän päivityksen myötä Modbus TCP on oletusarvoisesti käytössä ja MQTT on poist käytöstä. Tämä voidaan muuttaa asetuksissa. Laite tukee nyt jopa 16 eri energiamittaria ja siihen on tehty useita eri optimointeja, jotka voivat aiheuttaa muutoksia laitteen asetuksissa. Käynnistä laite uudelleen asetusten muuttamisen jälkeen.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Laitteen asetukset muutettu onnistuneesti';

  @override
  String get deviceConfiguration => 'Laitteen asetukset';

  @override
  String get tiltModeToggle => 'Kallistustoiminto';

  @override
  String get tiltModeToggleFooter =>
      'Jos laite on otettu käyttöön Matterissa, kaikki asetukset täytyy muuttaa siellä.';

  @override
  String get shaderMovementDirection => 'Käänteinen ylös/alas';

  @override
  String get shaderMovementDirectionDescription =>
      'Moottorin ylös/alas -liikkeen suunnan kääntäminen päinvastaiseksi';

  @override
  String get tiltTime => 'Kallistuksen kestoaika';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Ota käyttöön',
      'false': 'Poista käytöstä',
      'other': 'Muuta',
    });
    return '$_temp0 kallistustoiminto';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Ota käyttöön',
      'false': 'Poista käytöstä',
      'other': 'Muuta',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Säleiden asetukset';

  @override
  String get generalTextPosition => 'Asento';

  @override
  String get generalTextSlatPosition => 'Säleiden asento';

  @override
  String get slatSettingDescription =>
      'Aktivoi lamellitoiminto säätääksesi kaihtimien kallistuskulmaa.';

  @override
  String get scenePositionSliderDescription =>
      'Asento määrittää, kuinka pitkälle kaihtimet avataan tai suljetaan.';

  @override
  String get sceneSlatPositionSliderDescription =>
      'Lamellien asento määrittää rullaverhouslamellien kallistuskulman, jos kallistustoiminto on aktivoitu.';

  @override
  String get referenceRun => 'Kalibrointiajo';

  @override
  String get slatAutoSettingHint =>
      'Tässä tilassa verhojen asennolla ei ole merkitystä ennen kuin säleet ovat säätyneet haluttuun kallistusasentoon.';

  @override
  String get slatReversalSettingHint =>
      'Tässä tilassa verhot sulkeutuvat kokonaan ennen kuin säleet säätyvät haluttuun kallistusasentoon.';

  @override
  String get release340Header =>
      'Uusi ESB64NP-IPM, uppoasennettava verhomoottorien ohjainlaite on nyt saatavana!';

  @override
  String get release340ESB64Header => 'Mihin kaikkeen uusi ESB64NP-IPM pystyy?';

  @override
  String get release340ESB64Note1 =>
      'Matter Gateway -sertifioitu verhomoottorien ohjainlaitteemme, jossa on myös säleiden säätötoiminto.';

  @override
  String get release340ESB64Note2 =>
      'Kaksi langallista painiketuloa manuaalista kytkentää ja Matteriin edelleen välittämistä varten.';

  @override
  String get release340ESB64Note3 =>
      'Laajennettavissa langattomalla EnOcean plug-in adapterilla (EOA64) ja esim. langattomalla FT55ES-wg painikkeella.';

  @override
  String get release340ESB64Note4 =>
      'Avoin integraatioille OpenAPI-standardiin perustuvan REST API:n ansiosta';

  @override
  String get activateTiltModeDialogText =>
      'Jos kallistustoiminto otetaan käyttöön, kaikki asetukset menetetään. Haluatko varmasti ottaa kallistustoiminnon käyttöön?';

  @override
  String get deactivateTiltModeDialogText =>
      'Jos kallistustoiminto poistetaan käytöstä, kaikki asetukset menetetään. Haluatko varmasti poistaa kallistustoiminnon käytöstä?';

  @override
  String get buttonSceneESBDescription =>
      'Kohtauspainike asettaa rullaportin tiettyyn asentoon.';

  @override
  String get sceneValueOverride =>
      'Pidä painiketta painettuna 4 sekunnin ajan korvaa sijainti nykyisellä arvolla (lähtöarvo).';

  @override
  String get sceneCalibration =>
      'Kalibrointiajossa rullasuljin siirretään kerran kokonaan alas ja ylös pääteasentojen määrittämiseksi ja asennon tunnistuksen mahdollistamiseksi.';

  @override
  String get up => 'Ylös';

  @override
  String get down => 'Down';
}
