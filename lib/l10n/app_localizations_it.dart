// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Italian (`it`).
class AppLocalizationsIt extends AppLocalizations {
  AppLocalizationsIt([String locale = 'it']) : super(locale);

  @override
  String get appName => 'ELTAKO Connect';

  @override
  String get discoveryHint =>
      'Attivare il Bluetooth sul dispositivo per connettersi';

  @override
  String devicesFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count dispositivi trovati',
      one: '1 dispositivo trovato',
      zero: 'nessun dispositivo trovato',
    );
    return '$_temp0.';
  }

  @override
  String discoveryDemodeviceName(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'dispositivi demo',
      one: 'dispositivo demo',
    );
    return '$_temp0';
  }

  @override
  String get discoverySu12Description =>
      'Interruttore orario Bluetooth a 2 canali';

  @override
  String get discoveryImprint => 'Impronta';

  @override
  String get discoveryLegalnotice => 'Avviso legale';

  @override
  String get generalSave => 'Salvare';

  @override
  String get generalCancel => 'Annullare';

  @override
  String get detailsHeaderHardwareversion => 'Versione hardware';

  @override
  String get detailsHeaderSoftwareversion => 'Versione software';

  @override
  String get detailsHeaderConnected => 'Connesso';

  @override
  String get detailsHeaderDisconnected => 'Disconnesso';

  @override
  String get detailsTimersectionHeader => 'Programmi';

  @override
  String get detailsTimersectionTimercount => 'di 60 programmi utilizzati';

  @override
  String get detailsConfigurationsectionHeader => 'Configurazione';

  @override
  String get detailsConfigurationPin => 'PIN del dispositivo';

  @override
  String detailsConfigurationChannelsDescription(
    Object channel1,
    Object channel2,
  ) {
    return 'Canale 1: $channel1| Canale 2:  $channel2';
  }

  @override
  String get settingsCentralHeader => 'Central On/Off';

  @override
  String get detailsConfigurationCentralDescription =>
      'Si applica solo se il canale è impostato su AUTO';

  @override
  String get detailsConfigurationDevicedisplaylock =>
      'Blocco del display del dispositivo';

  @override
  String get timerOverviewHeader => 'Programmi';

  @override
  String get timerOverviewTimersectionTimerinactivecount => 'inattivo';

  @override
  String get timerDetailsListsectionDays1 => 'Lunedì';

  @override
  String get timerDetailsListsectionDays2 => 'Martedì';

  @override
  String get timerDetailsListsectionDays3 => 'Mercoledì';

  @override
  String get timerDetailsListsectionDays4 => 'Giovedì';

  @override
  String get timerDetailsListsectionDays5 => 'Venerdì';

  @override
  String get timerDetailsListsectionDays6 => 'Sabato';

  @override
  String get timerDetailsListsectionDays7 => 'Domenica';

  @override
  String get timerDetailsHeader => 'Programma %@';

  @override
  String get timerDetailsSunrise => 'Alba';

  @override
  String get generalToggleOff => 'Off';

  @override
  String get generalToggleOn => 'On';

  @override
  String get timerDetailsImpuls => 'Impulso';

  @override
  String get generalTextTime => 'Tempo';

  @override
  String get generalTextAstro => 'Astro';

  @override
  String get generalTextAuto => 'Auto';

  @override
  String get timerDetailsOffset => 'Time Offset';

  @override
  String get timerDetailsPlausibility =>
      'Attivare il controllo di plausibilità';

  @override
  String get timerDetailsPlausibilityDescription =>
      'Se l\'ora di spegnimento è impostata su un\'ora precedente a quella di accensione, entrambi i programmi vengono ignorati, ad esempio l\'accensione all\'alba e lo spegnimento alle 6:00 del mattino. Ci potrebbero essere programmazioni non volute come l\'accensione al tramonto e lo spegnimento alle 01:00 del mattino';

  @override
  String get generalDone => 'Pronto';

  @override
  String get generalDelete => 'Cancellare';

  @override
  String get timerDetailsImpulsDescription =>
      'Per modifiche, accedere alla configurazione del dispositivo';

  @override
  String get settingsNameHeader => 'Nome del dispositivo';

  @override
  String get settingsNameDescription =>
      'Il nome è visibile quando si utilizza il Bluetooth.';

  @override
  String get settingsFactoryresetHeader => 'Impostazioni di fabbrica';

  @override
  String get settingsFactoryresetDescription =>
      'Quali contenuti devono essere reimpostati?';

  @override
  String get settingsFactoryresetResetbluetooth =>
      'Ripristino delle impostazioni Bluetooth';

  @override
  String get settingsFactoryresetResettime =>
      'Ripristino delle impostazioni dell\'ora';

  @override
  String get settingsFactoryresetResetall =>
      'Impostare le impostazioni di fabbrica';

  @override
  String get settingsDeletetimerHeader => 'Cancellare i programmi';

  @override
  String get settingsDeletetimerDescription =>
      'Scegliere i canali da eliminare.';

  @override
  String get settingsDeletetimerAllchannels => 'Tutti i canali';

  @override
  String get settingsImpulseHeader => 'Tempo di commutazione degli impulsi';

  @override
  String get settingsImpulseDescription =>
      'Il tempo di commutazione dell\'impulso imposta la durata dell\'impulso.';

  @override
  String get generalTextRandommode => 'Modalità casuale';

  @override
  String get settingsChannelsTimeoffsetHeader =>
      'Sfalsamento dell\'ora del solstizio';

  @override
  String settingsChannelsTimeoffsetDescription(
    Object summerOffset,
    Object winterOffset,
  ) {
    return 'Estate: ${summerOffset}min | Inverno: ${winterOffset}min';
  }

  @override
  String get settingsLocationHeader => 'Posizione';

  @override
  String get settingsLocationDescription =>
      'Impostare la posizione per utilizzare le funzioni astro.';

  @override
  String get settingsLanguageHeader => 'Lingua del dispositivo';

  @override
  String get settingsLanguageSetlanguageautomatically =>
      'Impostazione automatica della lingua';

  @override
  String settingsLanguageDescription(Object deviceType) {
    return 'Scegliere la lingua per il $deviceType';
  }

  @override
  String get settingsLanguageGerman => 'Tedesco';

  @override
  String get settingsLanguageFrench => 'Francese';

  @override
  String get settingsLanguageEnglish => 'Inglese';

  @override
  String get settingsLanguageItalian => 'Italiano';

  @override
  String get settingsLanguageSpanish => 'Spagnolo';

  @override
  String get settingsDatetimeHeader => 'Data e ora';

  @override
  String get settingsDatetimeSettimeautomatically =>
      'Applicare il tempo di sistema';

  @override
  String get settingsDatetimeSettimezoneautomatically =>
      'Impostazione automatica del fuso orario';

  @override
  String get generalTextTimezone => 'Fuso orario';

  @override
  String get settingsDatetime24Hformat => 'Formato 24 ore';

  @override
  String get settingsDatetimeSetsummerwintertimeautomatically =>
      'Estate/inverno automaticamente';

  @override
  String get settingsDatetimeWinter => 'Inverno';

  @override
  String get settingsDatetimeSummer => 'Estate';

  @override
  String get settingsPasskeyHeader => 'PIN attuale del dispositivo';

  @override
  String get settingsPasskeyDescription =>
      'Inserire il PIN attuale del dispositivo';

  @override
  String get timerDetailsActiveprogram => 'Programma attivo';

  @override
  String get timerDetailsActivedays => 'Giorni attivi';

  @override
  String get timerDetailsSuccessdialogHeader => 'Successo';

  @override
  String get timerDetailsSuccessdialogDescription =>
      'Programma aggiunto con successo';

  @override
  String get settingsRandommodeDescription =>
      'La modalità casuale funziona solo con i programmi basati su timer, ma non con quelli basati su impulsi o su astro (alba/tramonto). Con la modalità casuale attivata, tutti i programmi di questo canale vengono sfalsati in modo casuale fino a 15 minuti. I programmi on-timers sono sfalsati in anticipo, quelli off-timers sono ritardati.';

  @override
  String get settingsSolsticeHeader => 'Sfalsamento dell\'ora del solstizio';

  @override
  String get settingsSolsticeDescription =>
      'L\'ora impostata definisce l\'offset temporale rispetto al solstizio. Il solstizio viene rispettivamente invertito.';

  @override
  String get settingsSolsticeHint =>
      'Esempio:\nIn inverno la commutazione avviene 30 minuti prima del tramonto, in risposta la commutazione all\'alba avviene anch\'essa con 30 minuti di anticipo.';

  @override
  String get generalTextMinutesShort => 'min';

  @override
  String get settingsPinDescription =>
      'Il PIN è necessario per la connessione.';

  @override
  String get settingsPinHeader => 'PIN del nuovo dispositivo';

  @override
  String get settingsPinNewpinDescription => 'Inserire un nuovo PIN';

  @override
  String get settingsPinNewpinRepeat => 'Ripetere il nuovo PIN';

  @override
  String get detailsProductinfo => 'Informazioni sul prodotto';

  @override
  String get settingsDatetimeSettimeautodescription =>
      'Scegliere l\'orario preferito';

  @override
  String minutes(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Minuti',
      one: 'Minuto',
    );
    return '$_temp0';
  }

  @override
  String hours(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Ore',
      one: 'Ora',
    );
    return '$_temp0';
  }

  @override
  String seconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'secondi',
      one: 'secondo',
    );
    return '$_temp0.';
  }

  @override
  String generalTextChannel(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'canali',
      one: 'canale',
    );
    return '$_temp0';
  }

  @override
  String generalLabelChannel(Object number) {
    return 'Canale $number';
  }

  @override
  String get generalTextDate => 'Data';

  @override
  String get settingsDatetime24HformatDescription =>
      'Scegliere il formato preferito';

  @override
  String get settingsDatetimeSetsummerwintertime => 'Estate/inverno';

  @override
  String get settingsDatetime24HformatValue24 => '24h';

  @override
  String get settingsDatetime24HformatValue12 => 'AM/PM';

  @override
  String get detailsEdittimer => 'Modifica dei programmi';

  @override
  String get settingsPinOldpinRepeat => 'Ripetere il PIN attuale';

  @override
  String get settingsPinCheckpin => 'Verifica del PIN';

  @override
  String detailsDevice(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'dispositivo',
      one: 'dispositivo',
    );
    return '$_temp0';
  }

  @override
  String get detailsDisconnect => 'Disconnessione';

  @override
  String get settingsCentralDescription =>
      'L\'ingresso A1 controlla l\'On/Off centrale.\nL\'On/Off centrale si applica solo se il canale è impostato su On/Off centrale.';

  @override
  String get settingsCentralHint =>
      'Esempio:\nCanale 1 = On/Off centrale\nCanale 2 = Spento\nA1 = On centrale -> Solo C1 passa a On, C2 rimane Off';

  @override
  String get settingsCentralToggleheader => 'Interruttori di ingresso centrale';

  @override
  String get settingsCentralActivechannelsdescription =>
      'Canali attuali con l\'impostazione Central On/Off:';

  @override
  String get settingsSolsticeSign => 'Segno';

  @override
  String get settingsDatetimeTimezoneDescription => 'Ora dell\'Europa centrale';

  @override
  String get generalButtonContinue => 'Continua';

  @override
  String get settingsPinConfirmationDescription => 'Modifica del PIN riuscita';

  @override
  String get settingsPinFailDescription => 'Modifica del PIN non riuscita';

  @override
  String get settingsPinFailHeader => 'Errore';

  @override
  String get settingsPinFailShort =>
      'Il PIN deve essere composto esattamente da 6 cifre.';

  @override
  String get settingsPinFailWrong => 'Il PIN non è corretto';

  @override
  String get settingsPinFailMatch => 'I PIN non corrispondono';

  @override
  String get discoveryLostconnectionHeader => 'Connessione persa';

  @override
  String get discoveryLostconnectionDescription =>
      'Il dispositivo è stato scollegato.';

  @override
  String get settingsChannelConfigCentralDescription =>
      'Si comporta come AUTO e ascolta anche gli ingressi della centrale cablata';

  @override
  String get settingsChannelConfigOnDescription =>
      'Commuta il canale in modo permanente su ON e ignora i programmi';

  @override
  String get settingsChannelConfigOffDescription =>
      'Commuta il canale in modo permanente su OFF e ignora i programmi';

  @override
  String get settingsChannelConfigAutoDescription =>
      'Commuta in relazione all\'ora e ai programmi astronomici';

  @override
  String get bluetoothPermissionDescription =>
      'Per la configurazione dei dispositivi è necessario il Bluetooth.';

  @override
  String get timerListitemOn => 'Accendere';

  @override
  String get timerListitemOff => 'Spegnere';

  @override
  String get timerListitemUnknown => 'Sconosciuto';

  @override
  String get timerDetailsAstroHint =>
      'La posizione deve essere impostata nelle impostazioni affinché i programmi astro funzionino correttamente.';

  @override
  String get timerDetailsTrigger => 'Aziona';

  @override
  String get timerDetailsSunset => 'Tramonto';

  @override
  String get settingsLocationCoordinates => 'Coordinate';

  @override
  String get settingsLocationLatitude => 'Latitudine';

  @override
  String get settingsLocationLongitude => 'Longitudine';

  @override
  String timerOverviewEmptyday(Object day) {
    return 'Al momento non vengono utilizzati programmi per $day';
  }

  @override
  String get timerOverviewProgramloaded => 'I programmi vengono caricati';

  @override
  String get timerOverviewProgramchanged => 'Il programma è stato modificato';

  @override
  String get settingsDatetimeProcessing =>
      'La data e l\'ora sono state modificate';

  @override
  String get deviceNameEmpty => 'L\'input non deve essere vuoto';

  @override
  String deviceNameHint(Object count) {
    return 'L\'input non deve contenere più di $count caratteri.';
  }

  @override
  String get deviceNameChanged => 'Il nome del dispositivo è stato modificato';

  @override
  String get deviceNameChangedSuccessfully =>
      'Il nome del dispositivo è stato modificato con successo.';

  @override
  String get deviceNameChangedFailed => 'Si è verificato un errore.';

  @override
  String get settingsPinConfirm => 'Confermare';

  @override
  String get deviceShowInstructions =>
      '1. Attivare il Bluetooth dell\'orologio con SET\n2. Toccare il pulsante in alto per avviare la ricerca.';

  @override
  String get deviceNameNew => 'Inserire il nuovo nome del dispositivo';

  @override
  String get settingsLanguageRetrieved => 'La lingua viene recuperata';

  @override
  String get detailsProgramsShow => 'Mostra i programmi';

  @override
  String get generalTextProcessing => 'Attendere prego';

  @override
  String get generalTextRetrieving => 'vengono recuperati';

  @override
  String get settingsLocationPermission =>
      'Consentire a ELTAKO Connect di accedere alla posizione di questo dispositivo';

  @override
  String get timerOverviewChannelloaded => 'I canali sono caricati';

  @override
  String get generalTextRandommodeChanged =>
      'La modalità casuale viene modificata';

  @override
  String get detailsConfigurationsectionChanged =>
      'La configurazione viene modificata';

  @override
  String get settingsSettimeFunctions =>
      'Le funzioni temporali vengono modificate';

  @override
  String get imprintContact => 'Contatto';

  @override
  String get imprintPhone => 'Telefono';

  @override
  String get imprintMail => 'Mail';

  @override
  String get imprintRegistrycourt => 'Registro del tribunale';

  @override
  String get imprintRegistrynumber => 'Numero di registrazione';

  @override
  String get imprintCeo => 'Direttore generale';

  @override
  String get imprintTaxnumber =>
      'Numero di identificazione dell\'imposta sulle vendite';

  @override
  String get settingsLocationCurrent => 'Posizione attuale';

  @override
  String get generalTextReset => 'Reset';

  @override
  String get discoverySearchStart => 'Avviare la ricerca';

  @override
  String get discoverySearchStop => 'Interrompere la ricerca';

  @override
  String get settingsImpulsSaved =>
      'Il tempo di commutazione degli impulsi viene memorizzato';

  @override
  String get settingsCentralNochannel =>
      'Non ci sono canali con l\'impostazione On/Off centrale.';

  @override
  String get settingsFactoryresetBluetoothConfirmationDescription =>
      'La connessione Bluetooth è stata ripristinata con successo.';

  @override
  String get settingsFactoryresetBluetoothFailDescription =>
      'Ripristino delle connessioni Bluetooth non riuscito.';

  @override
  String get imprintPublisher => 'Editore';

  @override
  String get discoveryDeviceConnecting => 'Connessione riuscita';

  @override
  String get discoveryDeviceRestarting => 'Riavvio...';

  @override
  String get generalTextConfigurationsaved =>
      'Configurazione del canale salvata.\n';

  @override
  String get timerOverviewChannelssaved => 'Salva i canali';

  @override
  String get timerOverviewSaved => 'Timer salvato\n';

  @override
  String get timerSectionList => 'Vista elenco\n';

  @override
  String get timerSectionDayview => 'vista giorno';

  @override
  String get generalTextChannelInstructions => 'Impostazioni del canale';

  @override
  String get generalTextPublisher => 'Editore';

  @override
  String get settingsDeletetimerDialog =>
      'Volete davvero cancellare tutti i programmi?';

  @override
  String get settingsFactoryresetResetbluetoothDialog =>
      'Volete davvero ripristinare tutte le impostazioni Bluetooth?';

  @override
  String get settingsCentralTogglecentral => 'Central\nOn/Off';

  @override
  String generalTextConfirmation(Object serviceName) {
    return '$serviceName modifica riuscita.';
  }

  @override
  String generalTextFailed(Object serviceName) {
    return '$serviceName modifica fallita.';
  }

  @override
  String get settingsChannelConfirmationDescription =>
      'I canali sono stati cambiati con successo.';

  @override
  String get timerDetailsSaveHeader => 'Salvare il programma';

  @override
  String get timerDetailsDeleteHeader => 'Cancellare il programma';

  @override
  String get timerDetailsSaveDescription =>
      'Salvataggio del programma riuscito.';

  @override
  String get timerDetailsDeleteDescription =>
      'L\'eliminazione del programma è riuscita.';

  @override
  String get timerDetailsAlertweekdays =>
      'Il programma non può essere salvato perché non è stato selezionato alcun giorno della settimana.';

  @override
  String get generalTextOk => 'OK';

  @override
  String get settingsDatetimeChangesuccesfull =>
      'L\'ora è stata modificata con successo.';

  @override
  String get discoveryConnectionFailed => 'Connessione fallita';

  @override
  String get discoveryDeviceResetrequired =>
      'Non è stato possibile stabilire una connessione con il dispositivo. Per risolvere il problema, eliminare il dispositivo dalle impostazioni Bluetooth. Se il problema persiste, contattare il nostro supporto tecnico.';

  @override
  String get generalTextSearch => 'Dispositivi di ricerca';

  @override
  String get generalTextOr => 'o';

  @override
  String get settingsFactoryresetProgramsConfirmationDescription =>
      'Tutti i programmi sono stati eliminati con successo.';

  @override
  String get generalTextManualentry => 'Inserimento manuale';

  @override
  String get settingsLocationSaved => 'Posizione salvata';

  @override
  String get settingsLocationAutosearch => 'Ricerca automatica della posizione';

  @override
  String get imprintPhoneNumber => '+49 711 / 9435 0000';

  @override
  String get imprintMailAddress => '<EMAIL>';

  @override
  String get settingsFactoryresetResetallDialog =>
      'Volete davvero ripristinare le impostazioni di fabbrica del dispositivo?';

  @override
  String get settingsFactoryresetFactoryConfirmationDescription =>
      'Il dispositivo è stato ripristinato con successo alle impostazioni di fabbrica.';

  @override
  String get settingsFactoryresetFactoryFailDescription =>
      'Il reset dell\'unità non è riuscito.';

  @override
  String get imprintPhoneNumberIos => '+49711/94350000';

  @override
  String get mfzFunctionA2Title => 'Ritardo di risposta a 2 stadi (A2)';

  @override
  String get mfzFunctionA2TitleShort => 'Ritardo di risposta a 2 stadi (A2)';

  @override
  String get mfzFunctionA2Description =>
      'Quando viene applicata la tensione di controllo, inizia il tempo T1 compreso tra 0 e 60 secondi. Al termine di questo periodo, il contatto 1-2 si chiude e inizia il lasso di tempo t2 compreso tra 0 e 60 secondi. Al termine di questo periodo, il contatto 3-4 si chiude. Dopo un\'interruzione, la sequenza temporale ricomincia con t1.';

  @override
  String get mfzFunctionRvTitle => 'Ritardo di spegnimento (RV)';

  @override
  String get mfzFunctionRvTitleShort => 'RV | Ritardo di spegnimento';

  @override
  String get mfzFunctionRvDescription =>
      'Quando viene applicata la tensione di controllo, il contatto passa cambia a 15-18. \nQuando la tensione di controllo viene interrotta, inizia l\'intervallo di tempo, al termine del quale il contatto di lavoro torna in posizione di riposo. Può essere attivato durante il timeout.';

  @override
  String get mfzFunctionTiTitle => 'Intermittenza inizio ON (TI)';

  @override
  String get mfzFunctionTiTitleShort => 'TI | Intermittenza inizio ON ';

  @override
  String get mfzFunctionTiDescription =>
      'Finché la tensione di controllo è applicata, il contatto di comando si chiude e si apre. Il tempo di commutazione in entrambe le direzioni può essere impostato separatamente. Quando viene applicata la tensione di controllo, il contatto di lavoro passa immediatamente a 15-18.';

  @override
  String get mfzFunctionAvTitle => 'Ritardo di risposta (AV)';

  @override
  String get mfzFunctionAvTitleShort => 'AV | Ritardo di risposta';

  @override
  String get mfzFunctionAvDescription =>
      'Quando viene applicata la tensione di controllo, inizia la sequenza temporale, al termine della quale il contatto di lavoro passa a 15-18. Dopo un\'interruzione, la sequenza temporale ricomincia.';

  @override
  String get mfzFunctionAvPlusTitle =>
      'Ritardo di risposta additivo (AV+; ritardo di accensione)';

  @override
  String get mfzFunctionAvPlusTitleShort =>
      'AV+ | Ritardo di risposta additivo';

  @override
  String get mfzFunctionAvPlusDescription =>
      'Funziona come AV, ma dopo un\'interruzione il tempo già trascorso rimane memorizzato.';

  @override
  String get mfzFunctionAwTitle =>
      'Contatto ad impulso alla diseccitazione (AW)';

  @override
  String get mfzFunctionAwTitleShort =>
      'AW | contatto ad impulso alla diseccitazione';

  @override
  String get mfzFunctionAwDescription =>
      'Quando viene interrotta la tensione di comando il contatto passa da 15-16 a 15-18 e ritorna automaticamente a 15-16 allo scadere del tempo impostato. Se durante il tempo impostato viene applicata la tensione di comando, il contatto ritorna immediatamente a 15-16 e il tempo rimanente viene cancellato.';

  @override
  String get mfzFunctionIfTitle => 'Riduttore di impulsi (IF; solo MFZ12.1)';

  @override
  String get mfzFunctionIfTitleShort => 'IF | Generatore di impulsi';

  @override
  String get mfzFunctionIfDescription =>
      'Quando viene applicata la tensione di comando, il contatto passa a 15-18 per il tempo impostato. Le ulteriori attivazioni vengono valutate solo allo scadere del tempo impostato.';

  @override
  String get mfzFunctionEwTitle => 'Contatto ad impulso all\'eccitazione (EW)';

  @override
  String get mfzFunctionEwTitleShort =>
      'EW | Contatto ad impulso all\'eccitazione';

  @override
  String get mfzFunctionEwDescription =>
      'All\'applicazione della tensione di comando il contatto passa da 15-16 a 15-18 e ritorna automaticamente a 15-16 allo scadere del tempo impostato. Se durante il tempo impostato viene interrotta la tensione di comando il contatto ritorna immediatamente a 15-16 ed il tempo rimanente viene cancellato.';

  @override
  String get mfzFunctionEawTitle =>
      'Contatto ad impulso all\'eccitazione ed alla diseccitazione (EAW)';

  @override
  String get mfzFunctionEawTitleShort =>
      'EAW | Contatto ad impulso all\'eccitazione ed alla diseccitazione';

  @override
  String get mfzFunctionEawDescription =>
      'Quando la tensione di controllo viene applicata o interrotta, il contatto di lavoro passa a 15-18 e ritorna dopo il tempo di pulizia impostato.';

  @override
  String get mfzFunctionTpTitle => 'Intermittenza inizio con pausa (TP;)';

  @override
  String get mfzFunctionTpTitleShort => 'TP | Intermittenza inizio con pausa';

  @override
  String get mfzFunctionTpDescription =>
      'Descrizioni funzionali come TI, ma quando viene applicata la tensione di controllo, il contatto non passa a 15-18, ma rimane inizialmente a 15-16 o aperto.';

  @override
  String get mfzFunctionIaTitle =>
      'Ritardato all\'eccitazione comandato da impulso (es. apertura porte automatiche)';

  @override
  String get mfzFunctionIaTitleShort =>
      'IA | Ritardato all\'eccitazione comandato da impulso';

  @override
  String get mfzFunctionIaDescription =>
      'All\'applicazione di un impulso di comando di almeno 50 ms inizia il periodo t1. Appena trascorso il contatto passa a 15-18 per il periodo t2 (MFZ12DX = 1 secondo). Per es. apriporte automatico. Se t1 viene impostato al tempo minimo di 0,1 secondi, agisce come generatore di impulso, impulso generato con il periodo t2 indipendentemente dalla lunghezza del segnale di comando (min. 150ms.)';

  @override
  String get mfzFunctionArvTitle =>
      'Ritardo nella risposta e nel rilascio (ARV)';

  @override
  String get mfzFunctionArvTitleShort =>
      'ARV | Ritardo nella risposta e nel rilascio';

  @override
  String get mfzFunctionArvDescription =>
      'Quando viene applicata la tensione di controllo, inizia il timeout, al termine del quale il contatto di lavoro passa alla posizione 15 -18. Se la tensione di controllo viene interrotta, inizia un nuovo timeout, al termine del quale il contatto di comando torna in posizione di riposo.\nDopo un\'interruzione del ritardo di risposta, il timeout inizia nuovamente.';

  @override
  String get mfzFunctionArvPlusTitle =>
      'Risposta additiva e ritardo di rilascio (ARV+)';

  @override
  String get mfzFunctionArvPlusTitleShort =>
      'ARV+ | Risposta additiva e ritardo di rilascio';

  @override
  String get mfzFunctionArvPlusDescription =>
      'Funziona come ARV, ma dopo un\'interruzione del ritardo di risposta, il tempo già trascorso rimane memorizzato.';

  @override
  String get mfzFunctionEsTitle => 'Interruttore a impulsi (ES)';

  @override
  String get mfzFunctionEsTitleShort => 'ES | Interruttore a impulsi';

  @override
  String get mfzFunctionEsDescription =>
      'Il contatto di lavoro si muove avanti e indietro con impulsi di controllo da 50 ms.';

  @override
  String get mfzFunctionEsvTitle =>
      'Interruttore a impulsi con ritardo di spegnimento e avviso di spegnimento anticipato (ESV)';

  @override
  String get mfzFunctionEsvTitleShort =>
      'ESV | Interruttore a impulsi con ritardo di spegnimento';

  @override
  String get mfzFunctionEsvDescription =>
      'Funzione come SRV. Inoltre, con preallarme di spegnimento: a partire da circa 30 secondi prima dello scadere del tempo, l\'illuminazione lampeggia 3 volte a intervalli di tempo decrescenti.';

  @override
  String get mfzFunctionErTitle => 'Funzione relè monostabile (ER)';

  @override
  String get mfzFunctionErTitleShort => 'ER | Funzione relè monostabile';

  @override
  String get mfzFunctionErDescription =>
      'Finché il contatto di comando è chiuso, il contatto di lavoro commuta da 15-16 a 15-18.';

  @override
  String get mfzFunctionSrvTitle =>
      'Interruttore a impulsi con ritardo di spegnimento (SRV)';

  @override
  String get mfzFunctionSrvTitleShort =>
      'SRV | Interruttore a impulsi con ritardo di spegnimento';

  @override
  String get mfzFunctionSrvDescription =>
      'Il contatto di lavoro commuta avanti e indietro con impulsi di comando a partire da 50ms. Nella posizione di contatto 15-18, il dispositivo torna automaticamente alla posizione di riposo 15-16 allo scadere del tempo di ritardo.';

  @override
  String get detailsFunctionsHeader => 'Funzioni';

  @override
  String mfzFunctionTimeHeader(Object index) {
    return 'Tempo (t$index)';
  }

  @override
  String get mfzFunctionOnDescription => 'acceso permanente';

  @override
  String get mfzFunctionOffDescription => 'spento permanente';

  @override
  String get mfzFunctionMultiplier => 'Fattore';

  @override
  String get discoveryMfz12Description => 'Relè orario multifunzione Bluetooth';

  @override
  String get mfzFunctionOnTitle => 'acceso permanente';

  @override
  String get mfzFunctionOnTitleShort => 'acceso permanente';

  @override
  String get mfzFunctionOffTitle => 'spento permanente';

  @override
  String get mfzFunctionOffTitleShort => 'spento permenente';

  @override
  String get mfzMultiplierSecondsFloatingpoint => '0.1 secondi';

  @override
  String get mfzMultiplierMinutesFloatingpoint => '0.1 minuti';

  @override
  String get mfzMultiplierHoursFloatingpoint => '0.1 ore';

  @override
  String get mfzOverviewFunctionsloaded => 'Le funzioni sono caricate';

  @override
  String get mfzOverviewSaved => 'Funzione salvata';

  @override
  String get settingsBluetoothHeader => 'Bluetooth';

  @override
  String get bluetoothChangedSuccessfully =>
      'L\'impostazione Bluetooth è stata modificata con successo.';

  @override
  String get settingsBluetoothInformation =>
      'Nota: se questa impostazione è attivata, il dispositivo è permanentemente visibile a tutti tramite Bluetooth!';

  @override
  String get settingsBluetoothContinuousconnection => 'Visibilità permanente';

  @override
  String settingsBluetoothContinuousconnectionDescription(Object deviceType) {
    return 'Attivando la visibilità permanente, il Bluetooth rimane attivo al dispositivo ($deviceType) e non deve essere attivato manualmente prima di stabilire una connessione.';
  }

  @override
  String get settingsBluetoothTimeout => 'Timeout della connessione';

  @override
  String get settingsBluetoothPinlimit => 'Limite del PIN';

  @override
  String settingsBluetoothTimeoutDescription(Object timeout) {
    return 'La connessione viene scollegata dopo $timeout minuti di inattività.';
  }

  @override
  String settingsBluetoothPinlimitDescription(Object attempts) {
    return 'Per motivi di sicurezza, è possibile effettuare un massimo di $attempts tentativi per l\'inserimento del PIN. Il Bluetooth viene quindi disattivato e deve essere riattivato manualmente per una nuova connessione. manualmente per una nuova connessione.';
  }

  @override
  String get settingsBluetoothPinAttempts => 'tentativi';

  @override
  String get settingsResetfunctionHeader => 'Funzioni di reset';

  @override
  String get settingsResetfunctionDialog =>
      'Vuoi davvero ripristinare tutte le funzioni?';

  @override
  String get settingsFactoryresetFunctionsConfirmationDescription =>
      'Tutte le funzioni sono state ripristinate con successo.';

  @override
  String get mfzFunctionTime => 'Tempo (t)';

  @override
  String get discoveryConnectionFailedInfo => 'Nessuna connessione Bluetooth';

  @override
  String get detailsConfigurationDevicedisplaylockDialogtext =>
      'Subito dopo aver bloccato il display del dispositivo, il Bluetooth viene disattivato e deve essere riattivato manualmente per stabilire una nuova connessione.';

  @override
  String get detailsConfigurationDevicedisplaylockDialogquestion =>
      'Sei sicuro di bloccare il display del dispositivo?';

  @override
  String get settingsDemodevices => 'Mostra unità demo';

  @override
  String get generalTextSettings => 'Impostazioni';

  @override
  String get discoveryWifi => 'WiFi';

  @override
  String get settingsInformations => 'Informazioni';

  @override
  String get detailsConfigurationDimmingbehavior => 'funzionamento dimmer';

  @override
  String get detailsConfigurationSwitchbehavior => 'funzionamento impulso';

  @override
  String get detailsConfigurationBrightness => 'Luminosità';

  @override
  String get detailsConfigurationMinimum => 'Minimo';

  @override
  String get detailsConfigurationMaximum => 'Massimo';

  @override
  String get detailsConfigurationSwitchesGr => 'Gruppo relè (GR)';

  @override
  String get detailsConfigurationSwitchesGs => 'Interruttore di gruppo (GS)';

  @override
  String get detailsConfigurationSwitchesCloserer =>
      'Interruttore di chiusura (ER)';

  @override
  String get detailsConfigurationSwitchesClosererDescription =>
      'Off -> Tenere premuto (On) -> Rilasciare (Off)';

  @override
  String get detailsConfigurationSwitchesOpenerer =>
      'Interruttore di apertura (ER-Invers)';

  @override
  String get detailsConfigurationSwitchesOpenererDescription =>
      'On -> Tenere premuto (Off) -> Rilasciare (On)';

  @override
  String get detailsConfigurationSwitchesSwitch => 'Interruttore';

  @override
  String get detailsConfigurationSwitchesSwitchDescription =>
      'Ad ogni impulso, la luce si accende e si spegne';

  @override
  String get detailsConfigurationSwitchesImpulsswitch => 'Relè passo passo';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription =>
      'L\'interruttore viene premuto e rilasciato brevemente per accendere o spegnere la luce.';

  @override
  String get detailsConfigurationSwitchesClosererDescription2 =>
      'Tenere premuto l\'interruttore. Quando viene rilasciato, il motore si ferma';

  @override
  String get detailsConfigurationSwitchesImpulsswitchDescription2 =>
      'L\'interruttore viene premuto brevemente per avviare il motore e brevemente per arrestarlo di nuovo.';

  @override
  String get detailsConfigurationWifiloginScan => 'Scansione del codice QR';

  @override
  String get detailsConfigurationWifiloginScannotvalid =>
      'Il codice scansionato non è valido';

  @override
  String get detailsConfigurationWifiloginDescription => 'Inserire il codice';

  @override
  String get detailsConfigurationWifiloginPassword => 'Password';

  @override
  String get discoveryEsbipDescription => 'Attuatore per tapparelle e tende IP';

  @override
  String get discoveryEsripDescription => 'Relè passo passo IP';

  @override
  String get discoveryEudipDescription => 'Dimmer universale IP';

  @override
  String get generalTextLoad => 'Caricamento';

  @override
  String get wifiBasicautomationsNotFound =>
      'Non è stata trovata alcuna automazione.';

  @override
  String get wifiCodeInvalid => 'Codice non valido';

  @override
  String get wifiCodeValid => 'Codice valido';

  @override
  String get wifiAuthorizationLogin => 'Collegare';

  @override
  String get wifiAuthorizationLoginFailed => 'Accesso non riuscito';

  @override
  String get wifiAuthorizationSerialnumber => 'Numero di serie';

  @override
  String get wifiAuthorizationProductiondate => 'Data di produzione';

  @override
  String get wifiAuthorizationProofofpossession => 'PoP';

  @override
  String get generalTextWifipassword => 'Password del WiFi';

  @override
  String get generalTextUsername => 'Nome utente';

  @override
  String get generalTextEnter => 'O INSERIRE MANUALMENTE';

  @override
  String get wifiAuthorizationScan => 'Scansionare il codice ELTAKO.';

  @override
  String get detailsConfigurationDevicesNofunctionshinttext =>
      'Questo dispositivo non supporta attualmente altre impostazioni';

  @override
  String get settingsUsedemodelay => 'Utilizzare il ritardo demo';

  @override
  String get settingsImpulsLoad =>
      'Il tempo di commutazione degli impulsi è caricato';

  @override
  String get settingsBluetoothLoad =>
      'L\'impostazione Bluetooth è in fase di caricamento.';

  @override
  String get detailsConfigurationsectionLoad =>
      'Le configurazioni sono caricate';

  @override
  String get generalTextLogin => 'Accedi';

  @override
  String get generalTextAuthentication => 'Autenticare';

  @override
  String get wifiAuthorizationScanDescription =>
      'Cerca il codice ELTAKO sul dispositivo o sulla scheda informativa inclusa e allinealo nella cornice della fotocamera in alto.';

  @override
  String get wifiAuthorizationScanShort => 'Scansione del codice ELTAKO';

  @override
  String get detailsConfigurationEdgemode => 'Modalità Edge';

  @override
  String get detailsConfigurationEdgemodeLeadingedge => 'Leading edge';

  @override
  String get generalTextNetwork => 'Rete';

  @override
  String get wifiAuthenticationSuccessful => 'Autenticazione riuscita';

  @override
  String get detailsConfigurationsectionSavechange =>
      'Configurazione modificata';

  @override
  String get discoveryWifiAdddevice => 'Aggiungi dispositivo Wi-Fi';

  @override
  String get wifiAuthenticationDelay => 'Questo può durare fino a 1 minuto';

  @override
  String get generalTextRetry => 'Riprova';

  @override
  String get wifiAuthenticationCredentials =>
      'Inserisci le credenziali del tuo WiFi';

  @override
  String get wifiAuthenticationSsid => 'SSID';

  @override
  String get wifiAuthenticationDelaylong =>
      'Può durare fino a 1 minuto finché il dispositivo non è pronto\ne viene visualizzato nell\'app';

  @override
  String get wifiAuthenticationCredentialsShort =>
      'Inserisci le credenziali Wi-Fi';

  @override
  String get wifiAuthenticationTeachin => 'Insegna il dispositivo al WiFi';

  @override
  String get wifiAuthenticationEstablish =>
      'Stabilire la connessione al dispositivo';

  @override
  String wifiAuthenticationEstablishLong(Object ssid) {
    return 'Il dispositivo si connette al Wi-Fi $ssid';
  }

  @override
  String get wifiAuthenticationFailed =>
      'Connessione fallita. Scollegare il dispositivo dall\'alimentazione per alcuni secondi e riprovare a collegarlo';

  @override
  String get wifiAuthenticationReset => 'Reimposta l\'autenticazione';

  @override
  String get wifiAuthenticationResetHint =>
      'Il codice ELTAKO deve quindi essere nuovamente scansionato';

  @override
  String get wifiAuthenticationInvaliddata =>
      'Dati di autenticazione non validi';

  @override
  String get wifiAuthenticationReauthenticate => 'Autenticare di nuovo';

  @override
  String get wifiAddhkdeviceHeader => 'Aggiungi dispositivo';

  @override
  String get wifiAddhkdeviceDescription =>
      'Collegate il nuovo dispositivo ELTAKO alla vostra WLAN tramite l\'app Apple Home.';

  @override
  String get wifiAddhkdeviceStep1 => 'Aprite l\'app Apple Home.';

  @override
  String get wifiAddhkdeviceStep2 =>
      'Fare clic sul più nell\'angolo superiore destro dell\'applicazione e selezionare **Aggiungi dispositivo**.';

  @override
  String get wifiAddhkdeviceStep3 =>
      'Seguire le istruzioni dell\'applicazione.';

  @override
  String get wifiAddhkdeviceStep4 =>
      'Ora il dispositivo può essere configurato nell\'app ELTAKO Connect.';

  @override
  String get detailsConfigurationRuntime => 'Tempo di esecuzione';

  @override
  String get detailsConfigurationRuntimeMode => 'Modalità';

  @override
  String get generalTextManually => 'Manualmente';

  @override
  String get detailsConfigurationRuntimeAutoDescription =>
      'L\'attuatore di ombreggiatura determina autonomamente il tempo di funzionamento durante ogni corsa dalla posizione finale inferiore a quella superiore (consigliata).\nDopo la messa in funzione, tale movimento\ndovrebbe essere effettuato dal basso verso l\'alto senza interruzioni.\n';

  @override
  String get detailsConfigurationRuntimeManuallyDescription =>
      'Il tempo di funzionamento del motore di ombreggiatura viene impostato manualmente tramite la durata sottostante.\nAssicurati che il tempo di funzionamento configurato corrisponda al tempo di funzionamento effettivo del tuo motore di schermatura.\nDopo l\'avvio iniziale o le modifiche, tale movimento dovrebbe essere eseguito dal basso verso l\'alto senza interruzioni.';

  @override
  String get detailsConfigurationRuntimeDemoDescription =>
      'La modalità demo è disponibile solo tramite API REST';

  @override
  String get generalTextDemomodeActive => 'Modalità demo attiva';

  @override
  String get detailsConfigurationRuntimeDuration => 'Durata';

  @override
  String get detailsConfigurationSwitchesGs4 => 'Interruttore di gruppo (GS4)';

  @override
  String get detailsConfigurationSwitchesGs4Description =>
      'Interruttore di gruppo con funzione di inversione jog per il comando delle tende';

  @override
  String get screenshotSu12 => 'Faro esterno';

  @override
  String get screenshotS2U12 => 'Faro esterno';

  @override
  String get screenshotMfz12 => 'Pompa';

  @override
  String get screenshotEsr62 => 'Lampada';

  @override
  String get screenshotEud62 => 'Plafoniera';

  @override
  String get screenshotEsb62 => 'Persiane balcone';

  @override
  String get detailsConfigurationEdgemodeLeadingedgeDescription =>
      'LC1-LC3 sono posizioni di comfort con diverse curve di regolazione per le lampade LED dimmerabili a 230 V, che a causa della loro struttura non possono essere regolate a sufficienza su AUTO e devono quindi essere forzate al controllo dell\'angolo di fase.';

  @override
  String get detailsConfigurationEdgemodeAutoDescription =>
      'AUTO consente di regolare la luminosità di tutti i tipi di lampade.';

  @override
  String get detailsConfigurationEdgemodeTrailingedge => 'Bordo d\'uscita';

  @override
  String get detailsConfigurationEdgemodeTrailingedgeDescription =>
      'LC4-LC6 sono posizioni Comfort con diverse curve di dimmerazione per lampade LED dimmerabili a 230 V.';

  @override
  String get updateHeader => 'Aggiornamento del firmware';

  @override
  String get updateTitleStepSearch => 'Ricerca di un aggiornamento';

  @override
  String get updateTitleStepFound => 'È stato trovato un aggiornamento';

  @override
  String get updateTitleStepDownload => 'Download dell\'aggiornamento';

  @override
  String get updateTitleStepInstall => 'Installazione dell\'aggiornamento';

  @override
  String get updateTitleStepSuccess => 'Aggiornamento riuscito';

  @override
  String get updateTitleStepUptodate => 'Già aggiornato';

  @override
  String get updateTitleStepFailed => 'Aggiornamento fallito';

  @override
  String get updateButtonSearch => 'Ricerca di aggiornamenti';

  @override
  String get updateButtonInstall => 'Installare l\'aggiornamento';

  @override
  String get updateCurrentversion => 'Versione attuale';

  @override
  String get updateNewversion =>
      'Disponibile un nuovo aggiornamento del firmware';

  @override
  String get updateHintPower =>
      'L\'utente deve essere spento e il dispositivo non deve essere scollegato dall\'alimentazione.';

  @override
  String get updateButton => 'Aggiornamento';

  @override
  String get updateHintCompatibility =>
      'Si consiglia di effettuare un aggiornamento, altrimenti alcune funzioni dell\'app saranno limitate.';

  @override
  String get generalTextDetails => 'Dettagli';

  @override
  String get updateMessageStepMetadata =>
      'Informazioni sull\'aggiornamento Lade';

  @override
  String get updateMessageStepPrepare => 'L\'aggiornamento è stato autorizzato';

  @override
  String get updateTitleStepUpdatesuccessful =>
      'L\'aggiornamento è stato effettuato';

  @override
  String get updateTextStepFailed =>
      'Tuttavia, l\'aggiornamento è molto complicato, ma è possibile provarlo in un paio di minuti o verificare se l\'apparecchio è stato aggiornato automaticamente (collegamento a Internet).';

  @override
  String get configurationsNotavailable =>
      'Non ci sono ancora configurazioni disponibili';

  @override
  String get configurationsAddHint =>
      'Creare nuove configurazioni collegandosi a un dispositivo e salvando una configurazione.';

  @override
  String get configurationsEdit => 'Modifica della configurazione';

  @override
  String get generalTextName => 'Nome';

  @override
  String get configurationsDelete => 'Cancellare la configurazione';

  @override
  String configurationsDeleteHint(Object configName) {
    return 'La configurazione: $configName deve essere cancellata?';
  }

  @override
  String get configurationsSave => 'Salvare la configurazione';

  @override
  String get configurationsSaveHint =>
      'Qui è possibile salvare la configurazione del dispositivo attuale o caricare una configurazione precedentemente salvata.';

  @override
  String get configurationsImport => 'Importazione della configurazione';

  @override
  String configurationsImportHint(Object configName) {
    return 'La configurazione $configName deve essere trasferita?';
  }

  @override
  String generalTextConfigurations(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Configurazioni',
      one: 'Configurazione',
    );
    return '$_temp0';
  }

  @override
  String get configurationsStepPrepare =>
      'La configurazione è in fase di preparazione';

  @override
  String get configurationsStepName => 'Inserire un nome per la configurazione';

  @override
  String get configurationsStepSaving => 'La configurazione viene salvata';

  @override
  String get configurationsStepSavedsuccessfully =>
      'La configurazione è stata salvata con successo';

  @override
  String get configurationsStepSavingfailed =>
      'Il salvataggio della configurazione non è riuscito';

  @override
  String get configurationsStepChoose => 'Selezionare una configurazione';

  @override
  String get configurationsStepImporting => 'La configurazione viene importata';

  @override
  String get configurationsStepImportedsuccessfully =>
      'La configurazione è stata importata con successo';

  @override
  String get configurationsStepImportingfailed =>
      'Importazione della configurazione fallita';

  @override
  String get discoveryAssuDescription =>
      'Interruttore orario per prese da esterno Bluetooth';

  @override
  String get settingsDatetimeDevicetime => 'Tempo effettivo del dispositivo';

  @override
  String get settingsDatetimeLoading =>
      'Le impostazioni dell\'ora sono caricate';

  @override
  String get discoveryEud12Description =>
      'Interruttore dimmer universale Bluetooth';

  @override
  String get generalTextOffdelay => 'Ritardo di spegnimento';

  @override
  String get generalTextRemainingbrightness => 'Luminosità residua';

  @override
  String get generalTextSwitchonvalue => 'Valore di accensione';

  @override
  String get motionsensorTitleNoremainingbrightness =>
      'Nessuna luminosità residua';

  @override
  String get motionsensorTitleAlwaysremainingbrightness =>
      'Con luminosità residua';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogram =>
      'Luminosità residua tramite programma di commutazione';

  @override
  String get motionsensorTitleRemainingbrightnesswithprogramandzea =>
      'Luminosità residua tramite ZE e ZA';

  @override
  String get motionsensorTitleNoremainingbrightnessauto =>
      'Nessuna luminosità residua (semiautomatico)';

  @override
  String get generalTextMotionsensor => 'Rilevatore di movimento';

  @override
  String get generalTextLightclock => 'Sveglia luminosa';

  @override
  String get generalTextSnoozeclock => 'Funzione Autospegnimento';

  @override
  String generalDescriptionLightclock(Object mode) {
    return 'All\'accensione ($mode), la luce si accende dopo circa 1 secondo alla luminosità minima e si attenua lentamente senza modificare l\'ultimo livello di luminosità salvato.';
  }

  @override
  String generalDescriptionSnoozeclock(Object mode) {
    return 'Quando si spegne ($mode), l\'illuminazione viene abbassata dalla posizione di regolazione attuale alla luminosità minima e si spegne. L\'illuminazione può essere spenta in qualsiasi momento durante il processo di regolazione della luminosità premendo brevemente il pulsante. Una pressione prolungata durante il processo di regolazione della luminosità fa cessare la funzione autospegnimento.';
  }

  @override
  String get generalTextImmediately => 'Immediatamente';

  @override
  String get generalTextPercentage => 'Percentuale';

  @override
  String get generalTextSwitchoffprewarning => 'Preavviso di spegnimento';

  @override
  String get generalDescriptionSwitchoffprewarning =>
      'Dimmerazione lenta fino alla luminosità minima';

  @override
  String get generalDescriptionOffdelay =>
      'Il dispositivo si accende quando viene applicata la tensione di controllo. Se la tensione di controllo viene interrotta, inizia un intervallo di tempo, al termine del quale l\'apparecchio si spegne. L\'apparecchio può essere acceso a valle durante il lasso di tempo.';

  @override
  String get generalDescriptionBrightness =>
      'La luminosità con cui la lampada viene accesa dal dimmer.';

  @override
  String get generalDescriptionRemainingbrightness =>
      'Il valore di regolazione in percentuale a cui la lampada viene regolata dopo lo spegnimento del rilevatore di movimento.';

  @override
  String get generalDescriptionRuntime =>
      'Tempo di funzionamento della funzione di sveglia luminosa dalla luminosità minima alla luminosità massima.';

  @override
  String get generalTextUniversalbutton => 'Pulsante universale';

  @override
  String get generalTextDirectionalbutton => 'Pulsante di direzione';

  @override
  String get eud12DescriptionAuto =>
      'Rilevamento automatico UT/RT (con sensore direzionale a diodo RTD)';

  @override
  String get eud12DescriptionRt => 'con diodo di rilevamento direzionale RTD';

  @override
  String get generalTextProgram => 'Programma';

  @override
  String get eud12MotionsensorOff =>
      'Con il rilevatore di movimento impostato su Off';

  @override
  String get eud12ClockmodeTitleProgramze => 'Programma e Central On';

  @override
  String get eud12ClockmodeTitleProgramza => 'Programma e Central Off';

  @override
  String get eud12ClockmodeTitleProgrambuttonon => 'Programma e UT/RT On';

  @override
  String get eud12ClockmodeTitleProgrambuttonoff => 'Programma e UT/RT Off';

  @override
  String get eud12TiImpulseTitle => 'Tempo di impulso On (t1)';

  @override
  String get eud12TiImpulseHeader =>
      'Valore di regolazione Tempo di impulso On';

  @override
  String get eud12TiImpulseDescription =>
      'Il valore di regolazione in percentuale a cui la lampada viene regolata al tempo di impulso ON.';

  @override
  String get eud12TiOffTitle => 'Tempo di impulso Off (t2)';

  @override
  String get eud12TiOffHeader => 'Valore di regolazione Tempo di impulso Off';

  @override
  String get eud12TiOffDescription =>
      'Il valore di regolazione in percentuale a cui la lampada viene regolata al tempo di impulso OFF.';

  @override
  String get generalTextButtonpermanentlight => 'Luce permanente a pulsante';

  @override
  String get generalDescriptionButtonpermanentlight =>
      'Impostazione della luce continua a pulsante da 0 a 10 ore con incrementi di 0,5 ore. Attivazione premendo il pulsante per più di 1 secondo (1x sfarfallio), disattivazione premendo il pulsante per più di 2 secondi.';

  @override
  String get generalTextNobuttonpermanentlight => 'No TSP';

  @override
  String get generalTextBasicsettings => 'Impostazioni di base';

  @override
  String get generalTextInputswitch => 'Ingresso pulsante locale (A1)';

  @override
  String get generalTextOperationmode => 'Modalità operativa';

  @override
  String get generalTextDimvalue => 'Comportamento acceso';

  @override
  String get eud12TitleUsememory => 'Utilizzare il valore della memoria';

  @override
  String get eud12DescriptionUsememory =>
      'Il valore di memoria corrisponde all\'ultimo valore di regolazione impostato. Se il valore di memoria è disattivato, la dimmerazione è sempre impostata sul valore di accensione.';

  @override
  String get generalTextStartup => 'Luminosità all\'accensione';

  @override
  String get generalDescriptionSwitchonvalue =>
      'Il valore di accensione è un valore di luminosità regolabile che garantisce un\'accensione sicura.';

  @override
  String get generalTitleSwitchontime => 'Tempo di accensione';

  @override
  String get generalDescriptionSwitchontime =>
      'Al termine del tempo di accensione impostato, la lampada viene dimmerata dal valore di accensione al valore di memoria.';

  @override
  String get generalDescriptionStartup =>
      'Alcune lampade a LED richiedono una corrente di spunto maggiore per accendersi in modo affidabile. La lampada viene accesa a questo valore di accensione e poi dimmerata al valore di memoria dopo il tempo di accensione.';

  @override
  String get eud12ClockmodeSubtitleProgramze =>
      'Cliccare brevemente su Central On';

  @override
  String get eud12ClockmodeSubtitleProgramza => 'Breve clic su central off';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonon =>
      'Doppio clic sul pulsante universale/pulsante di direzione On';

  @override
  String get eud12ClockmodeSubtitleProgrambuttonoff =>
      'Doppio clic sul pulsante universale/pulsante di direzione Off';

  @override
  String get eud12FunctionStairlighttimeswitchTitleShort =>
      'TLZ | Timer per l\'illuminazione delle scale';

  @override
  String get eud12FunctionMinTitleShort => 'MIN';

  @override
  String get eud12FunctionMmxTitleShort => 'MMX';

  @override
  String get eud12FunctionTiDescription =>
      'Timer con tempo di accensione e spegnimento regolabile da 0,5 secondi a 9,9 minuti. La luminosità può essere impostata da una luminosità minima a una massima.';

  @override
  String get eud12FunctionAutoDescription =>
      'Interruttore dimmer universale con impostazione per rilevatore di movimento, sveglia luminosa e funzione autospegnimento';

  @override
  String get eud12FunctionErDescription =>
      'Commutando il relè, è possibile impostare la luminosità da minima a massima.';

  @override
  String get eud12FunctionEsvDescription =>
      'Interruttore dimmer universale con impostazione di un ritardo di spegnimento da 1 a 120 minuti. Preavviso di spegnimento al termine del dimmeraggio selezionabile e regolabile da 1 a 3 minuti. Entrambi gli ingressi centrali sono attivi.';

  @override
  String get eud12FunctionTlzDescription =>
      'Impostazione della durata della luce del pulsante da 0 a 10 ore con incrementi di 0,5 ore. Attivazione premendo il pulsante per più di 1 secondo (1x sfarfallio), disattivazione premendo il pulsante per più di 2 secondi.';

  @override
  String get eud12FunctionMinDescription =>
      'Interruttore dimmer universale, passa alla luminosità minima impostata quando viene applicata la tensione di controllo. La luce viene dimmerata fino alla massima luminosità entro il tempo di dimmerazione impostato, compreso tra 1 e 120 minuti. Quando si toglie la tensione di controllo, la luce si spegne immediatamente, anche durante il tempo di regolazione. Entrambi gli ingressi centrali sono attivi.';

  @override
  String get eud12FunctionMmxDescription =>
      'Interruttore dimmer universale, passa alla luminosità minima impostata quando viene applicata la tensione di controllo. Durante il tempo di regolazione impostato, da 1 a 120 minuti, la luce viene regolata alla massima luminosità. Tuttavia, quando la tensione di controllo viene rimossa, il dimmer scende alla luminosità minima impostata. Quindi si spegne. Entrambi gli ingressi centrali sono attivi.';

  @override
  String get motionsensorSubtitleNoremainingbrightness =>
      'Con il rilevatore di movimento impostato su Off';

  @override
  String get motionsensorSubtitleAlwaysremainingbrightness =>
      'Con il rilevatore di movimento impostato su Off';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogram =>
      'Programma di commutazione attivato e disattivato con BWM spento';

  @override
  String get motionsensorSubtitleRemainingbrightnesswithprogramandzea =>
      'Central On attiva il sensore di movimento, Central Off disattiva il sensore di movimento, così come il programma di commutazione.';

  @override
  String get motionsensorSubtitleNoremainingbrightnessauto =>
      'Il rilevatore di movimento si spegne solo';

  @override
  String get detailsDimsectionHeader => 'Dimmerazione';

  @override
  String get generalTextFast => 'Veloce';

  @override
  String get generalTextSlow => 'Lentamente';

  @override
  String get eud12TextDimspeed => 'Velocità di oscuramento';

  @override
  String get eud12TextSwitchonspeed => 'Velocità di accensione';

  @override
  String get eud12TextSwitchoffspeed => 'Velocità di spegnimento';

  @override
  String get eud12DescriptionDimspeed =>
      'La velocità di regolazione è la velocità con cui il dimmer passa dalla luminosità attuale alla luminosità desiderata.';

  @override
  String get eud12DescriptionSwitchonspeed =>
      'La velocità di accensione è la velocità che il dimmer richiede per accendersi completamente.';

  @override
  String get eud12DescriptionSwitchoffspeed =>
      'La velocità di spegnimento è la velocità necessaria al dimmer per spegnersi completamente.';

  @override
  String get settingsFactoryresetResetdimHeader =>
      'Ripristino delle impostazioni di regolazione della luminosità';

  @override
  String get settingsFactoryresetResetdimDescription =>
      'Tutte le impostazioni di regolazione della luminosità devono essere ripristinate?';

  @override
  String get settingsFactoryresetResetdimConfirmationDescription =>
      'Le impostazioni di regolazione della luminosità sono state ripristinate con successo';

  @override
  String get eud12TextSwitchonoffspeed => 'Velocità di accensione/spegnimento';

  @override
  String get eud12DescriptionSwitchonoffspeed =>
      'La velocità di accensione/spegnimento è la velocità che il dimmer richiede per accendersi o spegnersi completamente.';

  @override
  String get timerDetailsDimtoval => 'Acceso con valore di regolazione in %';

  @override
  String get timerDetailsDimtovalDescription =>
      'Il dimmer si accende sempre con il valore di regolazione fisso in %.';

  @override
  String timerDetailsDimtovalSubtitle(Object brightness) {
    return 'Accendere con $brightness%';
  }

  @override
  String get timerDetailsDimtomem => 'Acceso con valore di memoria';

  @override
  String get timerDetailsDimtomemSubtitle => 'Accensione con valore di memoria';

  @override
  String get timerDetailsMotionsensorwithremainingbrightness =>
      'Luminosità residua (BWM) On';

  @override
  String get timerDetailsMotionsensornoremainingbrightness =>
      'Luminosità residua (BWM) Off';

  @override
  String get settingsRandommodeHint =>
      'Con la modalità casuale attivata, tutti i programmi di questo canale vengono sfalsati in modo casuale fino a 15 minuti. I programmi on-timers sono sfalsati in anticipo, quelli off-timers sono ritardati.';

  @override
  String get runtimeOffsetDescription =>
      'Tempo addizionale, dopo la scadenza del tempo impostato';

  @override
  String get loadingTextDimvalue => 'Il valore di regolazione è caricato';

  @override
  String get discoveryEudipmDescription => 'Dimmer universale IP Matter';

  @override
  String get generalTextOffset => 'Superamento';

  @override
  String get eud12DimvalueTestText => 'Invia luminosità';

  @override
  String get eud12DimvalueTestDescription =>
      'Durante il test si tiene conto della velocità di oscuramento attualmente impostata.';

  @override
  String get eud12DimvalueLoadText => 'Livello di luminosità';

  @override
  String get settingsDatetimeNotime =>
      'Le impostazioni di data e ora devono essere lette sul display del dispositivo.';

  @override
  String get generalMatterText => 'Matter';

  @override
  String get generalMatterMessage =>
      'Configurare il dispositivo Matter utilizzando l\'app Google Home, Amazon Alexa o Samsung SmartThings.';

  @override
  String get generalMatterOpengooglehome => 'Aprire Google Home';

  @override
  String get generalMatterOpenamazonalexa => 'Aprire Amazon Alexa';

  @override
  String get generalMatterOpensmartthings => 'Aprire SmartThings';

  @override
  String generalLabelProgram(Object number) {
    return 'Programma $number';
  }

  @override
  String get generalTextDone => 'Fatto';

  @override
  String get settingsRandommodeDescriptionShort =>
      'Con la modalità casuale attivata, tutti i programmi di questo canale vengono sfalsati in modo casuale fino a 15 minuti. I programmi on-timers sono sfalsati in anticipo, quelli off-timers sono ritardati.';

  @override
  String get all => 'Tutti';

  @override
  String get discoveryBluetooth => 'Bluetooth';

  @override
  String get success => 'Successo';

  @override
  String get error => 'Errore';

  @override
  String get timeProgramAdd => 'Aggiungi programma orario';

  @override
  String get noConnection => 'Nessuna connessione';

  @override
  String get timeProgramOnlyActive => 'Programmi configurati';

  @override
  String get timeProgramAll => 'Tutti i programmi';

  @override
  String get active => 'Attivo';

  @override
  String get inactive => 'Inattivo';

  @override
  String timeProgramSaved(Object number) {
    return 'Tempo di salvataggio del programma $number';
  }

  @override
  String get deviceLanguageSaved => 'Lingua del dispositivo salvata';

  @override
  String generalTextTimeShort(Object time) {
    return 'orologio $time';
  }

  @override
  String programDeleteHint(Object index) {
    return 'Il programma $index deve essere davvero cancellato?';
  }

  @override
  String milliseconds(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Millisecondi',
      one: 'Millisecondo',
    );
    return '$_temp0';
  }

  @override
  String millisecondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Milliseconds',
      one: '$count Millisecond',
    );
    return '$_temp0';
  }

  @override
  String secondsWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Seconds',
      one: '$count Second',
    );
    return '$_temp0';
  }

  @override
  String minutesWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Minutes',
      one: '$count Minute',
    );
    return '$_temp0';
  }

  @override
  String hoursWithValue(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count Hours',
      one: '$count Hour',
    );
    return '$_temp0';
  }

  @override
  String get settingsPinFailEmpty => 'Il PIN non deve essere vuoto';

  @override
  String get detailsConfigurationWifiloginScanNoMatch =>
      'Il codice scansionato non corrisponde al dispositivo';

  @override
  String get wifiAuthorizationPopIsEmpty => 'Il PoP non può essere vuoto';

  @override
  String get wifiAuthenticationCredentialsHint =>
      'Poiché l\'applicazione non può accedere alla password Wi-Fi privata, non è possibile verificare la correttezza dell\'inserimento. Se non viene stabilita alcuna connessione, controllare la password e inserirla nuovamente.';

  @override
  String get generalMatterOpenApplehome => 'Aprire casa Apple';

  @override
  String get timeProgramNoActive => 'Nessun programma configurato';

  @override
  String get timeProgramNoEmpty =>
      'Non è disponibile un programma per il tempo libero';

  @override
  String get nameOfConfiguration => 'Nome della configurazione';

  @override
  String get currentDevice => 'Dispositivo attuale';

  @override
  String get export => 'Esportazione';

  @override
  String get import => 'Importazione';

  @override
  String get savedConfigurations => 'Configurazioni salvate';

  @override
  String get importableServicesLabel =>
      'È possibile importare le seguenti impostazioni:';

  @override
  String get notImportableServicesLabel => 'Impostazioni incompatibili';

  @override
  String get deviceCategoryMeterGateway => 'Gateway del contatore';

  @override
  String get deviceCategory2ChannelTimeSwitch =>
      'Interruttore orario a 2 canali';

  @override
  String get devicategoryOutdoorTimeSwitchBluetooth =>
      'Interruttore orario esterno Bluetooth';

  @override
  String get settingsModbusHeader => 'Modbus';

  @override
  String get settingsModbusDescription =>
      'Regolare il baud rate, la parità e il timeout per configurare la velocità di trasmissione, il rilevamento degli errori e il tempo di attesa.';

  @override
  String get settingsModbusRTU => 'Modbus RTU';

  @override
  String get settingsModbusBaudrate => 'Baudrate';

  @override
  String get settingsModbusParity => 'Parità';

  @override
  String get settingsModbusTimeout => 'Timeout Modbus';

  @override
  String get locationServiceDisabled => 'La posizione è disabilitata';

  @override
  String get locationPermissionDenied =>
      'Consentite alla localizzazione di richiedere la vostra posizione attuale.';

  @override
  String get locationPermissionDeniedPermanently =>
      'I permessi di localizzazione sono permanentemente negati, si prega di consentire il permesso di localizzazione nelle impostazioni del dispositivo per richiedere la posizione corrente.';

  @override
  String get lastSync => 'Ultima sincronizzazione';

  @override
  String get dhcpActive => 'DHCP attivo';

  @override
  String get ipAddress => 'IP';

  @override
  String get subnetMask => 'Maschera di sottorete';

  @override
  String get standardGateway => 'Gateway predefinito';

  @override
  String get dns => 'DNS';

  @override
  String get alternateDNS => 'DNS alternativo';

  @override
  String get errorNoNetworksFound => 'Nessuna rete wifi trovata';

  @override
  String get availableNetworks => 'Reti disponibili';

  @override
  String get enableWifiInterface => 'Abilitare l\'interfaccia WiFi';

  @override
  String get enableLANInterface => 'Abilitazione dell\'interfaccia LAN';

  @override
  String get hintDontDisableAllInterfaces =>
      'Assicurarsi che non tutte le interfacce siano disattivate. L\'ultima interfaccia attivata ha la priorità.';

  @override
  String get ssid => 'SSID';

  @override
  String get searchNetworks => 'Reti di ricerca';

  @override
  String get errorNoNetworkEnabled => 'Almeno una stazione deve essere attiva';

  @override
  String get errorActiveNetworkInvalid =>
      'Non tutte le stazioni attive sono valide';

  @override
  String get invalidNetworkConfiguration => 'Configurazione di rete non valida';

  @override
  String get generalDefault => 'Predefinito';

  @override
  String get mqttHeader => 'MQTT';

  @override
  String get mqttConnected => 'Collegato al broker MQTT';

  @override
  String get mqttDisconnected => 'Nessuna connessione al broker MQTT';

  @override
  String get mqttBrokerURI => 'URI del broker';

  @override
  String get mqttBrokerURIHint => 'URI del broker MQTT';

  @override
  String get mqttPort => 'Porto';

  @override
  String get mqttPortHint => 'Porta MQTT';

  @override
  String get mqttClientId => 'ID cliente';

  @override
  String get mqttClientIdHint => 'ID cliente MQTT';

  @override
  String get mqttUsername => 'Nome utente';

  @override
  String get mqttUsernameHint => 'Nome utente MQTT';

  @override
  String get mqttPassword => 'Password';

  @override
  String get mqttPasswordHint => 'Password MQTT';

  @override
  String get mqttCertificate => 'Certificato';

  @override
  String get mqttCertificateHint => 'Certificato MQTT';

  @override
  String get mqttTopic => 'Argomento';

  @override
  String get mqttTopicHint => 'Argomento MQTT';

  @override
  String get electricityMeter => 'Contatore elettrico';

  @override
  String get electricityMeterCurrent => 'Attuale';

  @override
  String get electricityMeterHistory => 'La storia';

  @override
  String get electricityMeterReading => 'Lettura del contatore';

  @override
  String get connectivity => 'Connettività';

  @override
  String electricMeter(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'contatori elettrici',
      one: 'contatore elettrico',
    );
    return '$_temp0';
  }

  @override
  String get discoveryZGW16Description =>
      'Contatori di energia Modbus-MQTT-Gateway';

  @override
  String get bluetoothConnectionLost => 'Perdita della connessione Bluetooth';

  @override
  String get bluetoothConnectionLostDescription =>
      'La connessione Bluetooth al dispositivo è stata interrotta. Controllare la connessione al dispositivo.';

  @override
  String get openBluetoothSettings => 'Aprire le impostazioni Bluetooth';

  @override
  String get password => 'Password';

  @override
  String get setInitialPassword => 'Impostazione della password iniziale';

  @override
  String initialPasswordMinimumLength(Object length) {
    return 'La password deve essere lunga almeno $length caratteri';
  }

  @override
  String get repeatPassword => 'Ripetere la password';

  @override
  String get passwordsDoNotMatch => 'Le password non corrispondono';

  @override
  String get savePassword => 'Salva la password';

  @override
  String get savePasswordHint =>
      'La password viene salvata per le connessioni future sul dispositivo.';

  @override
  String get retrieveNtpServer => 'Recuperare l\'ora dal server NTP';

  @override
  String get retrieveNtpServerFailed =>
      'Non è stato possibile stabilire la connessione al server NTP.';

  @override
  String get retrieveNtpServerSuccess =>
      'La connessione al server NTP è riuscita.';

  @override
  String get settingsPasswordNewPasswordDescription =>
      'Inserire la nuova password';

  @override
  String get settingsPasswordConfirmationDescription =>
      'Modifica della password riuscita';

  @override
  String get dhcpRangeStart => 'Inizio intervallo DHCP';

  @override
  String get dhcpRangeEnd => 'Fine intervallo DHCP';

  @override
  String get forwardOnMQTT => 'Inoltro a MQTT';

  @override
  String get showAll => 'Mostra tutti';

  @override
  String get hide => 'Nascondere';

  @override
  String get changeToAPMode => 'Passare alla modalità AP';

  @override
  String get changeToAPModeDescription =>
      'Si sta per collegare il dispositivo a una rete WiFi; in questo caso la connessione al dispositivo viene interrotta e occorre ricollegarsi al dispositivo tramite la rete configurata.';

  @override
  String get consumption => 'Consumo';

  @override
  String get currentDay => 'Giorno corrente';

  @override
  String get twoWeeks => '2 settimane';

  @override
  String get oneYear => '1 anno';

  @override
  String get threeYears => '3 anni';

  @override
  String passwordMinLength(Object length) {
    return 'Password needs at least $length characters.';
  }

  @override
  String get passwordNeedsLetter => 'La password deve contenere una lettera.';

  @override
  String get passwordNeedsNumber => 'La password deve contenere un numero.';

  @override
  String get portEmpty => 'La porta non può essere vuota';

  @override
  String get portInvalid => 'Porta non valida';

  @override
  String portOutOfRange(Object rangeEnd, Object rangeStart) {
    return 'La porta deve essere compresa tra $rangeStart e $rangeEnd.';
  }

  @override
  String get ipAddressEmpty => 'L\'indirizzo IP non può essere vuoto';

  @override
  String get ipAddressInvalid => 'Indirizzo IP non valido';

  @override
  String get subnetMaskEmpty => 'La maschera di sottorete non può essere vuota';

  @override
  String get subnetMaskInvalid => 'Maschera di sottorete non valida';

  @override
  String get gatewayEmpty => 'Il gateway non può essere vuoto';

  @override
  String get gatewayInvalid => 'Gateway non valido';

  @override
  String get dnsEmpty => 'Il DNS non può essere vuoto';

  @override
  String get dnsInvalid => 'DNS non valido';

  @override
  String get uriEmpty => 'L\'URI non può essere vuoto';

  @override
  String get uriInvalid => 'URI non valido';

  @override
  String get electricityMeterChangedSuccessfully =>
      'Cambio del contatore elettrico riuscito';

  @override
  String get networkChangedSuccessfully =>
      'La configurazione di rete è stata modificata con successo';

  @override
  String get mqttChangedSuccessfully =>
      'La configurazione di MQTT è stata modificata con successo';

  @override
  String get modbusChangedSuccessfully =>
      'Le impostazioni Modbus sono state modificate con successo';

  @override
  String get loginData => 'Cancellare i dati di accesso';

  @override
  String get valueConfigured => 'Configurato';

  @override
  String get electricityMeterHistoryNoData => 'Nessun dato disponibile';

  @override
  String get locationChangedSuccessfully =>
      'La posizione è stata modificata con successo';

  @override
  String get settingsNameFailEmpty => 'Il nome non può essere vuoto';

  @override
  String settingsNameFailLength(Object length) {
    return 'Name must not be longer than $length characters';
  }

  @override
  String get solsticeChangedSuccesfully =>
      'Le impostazioni del Solstizio sono state modificate con successo';

  @override
  String get relayFunctionChangedSuccesfully =>
      'Relè-Funzione modificata con successo';

  @override
  String get relayFunctionHeader => 'Funzione relè';

  @override
  String get dimmerValueChangedSuccesfully =>
      'Il comportamento all\'accensione è stato modificato con successo';

  @override
  String get dimmerBehaviourChangedSuccesfully =>
      'Il comportamento di oscuramento è stato modificato con successo';

  @override
  String get dimmerBrightnessDescription =>
      'La luminosità minima e massima riguarda tutte le luminosità regolabili del dimmer.';

  @override
  String get dimmerSettingsChangedSuccesfully =>
      'Le impostazioni di base sono state modificate con successo';

  @override
  String get liveUpdateEnabled => 'Test dal vivo abilitato';

  @override
  String get liveUpdateDisabled => 'Test dal vivo disattivato';

  @override
  String get liveUpdateDescription =>
      'Il valore dell\'ultimo cursore modificato verrà inviato al dispositivo.';

  @override
  String get demoDevices => 'Dispositivi demo';

  @override
  String get showDemoDevices => 'Mostra i dispositivi demo';

  @override
  String get deviceCategoryTimeSwitch => 'Interruttore orario';

  @override
  String get deviceCategoryMultifunctionalRelay => 'Relè multifunzione';

  @override
  String get deviceCategoryDimmer => 'Dimmer';

  @override
  String get deviceCategoryShutter =>
      'Attuatore per tapparelle e ombreggianti\n';

  @override
  String get deviceCategoryRelay => 'Relè';

  @override
  String get search => 'Ricerca';

  @override
  String get configurationsHeader => 'Configurazioni';

  @override
  String get configurationsDescription =>
      'Gestite le vostre configurazioni qui.';

  @override
  String get configurationsNameFailEmpty =>
      'Il nome della configurazione non può essere vuoto';

  @override
  String get configurationDeleted => 'Configurazione eliminata';

  @override
  String codeFound(Object codeType) {
    return 'Codice $codeType trovato';
  }

  @override
  String get errorCameraPermission =>
      'Consentire alla telecamera di eseguire la scansione del codice ELTAKO.';

  @override
  String get authorizationSuccessful =>
      'Autorizzato con successo sul dispositivo';

  @override
  String get wifiAuthenticationResetConfirmationDescription =>
      'Il dispositivo è ora pronto per una nuova autorizzazione.';

  @override
  String get settingsResetConnectionHeader => 'Ripristino della connessione';

  @override
  String get settingsResetConnectionDescription =>
      'Vuoi davvero ripristinare la connessione?';

  @override
  String get settingsResetConnectionConfirmationDescription =>
      'La connessione è stata ripristinata con successo.';

  @override
  String get wiredInputChangedSuccesfully =>
      'Il comportamento dell\'interruttore è stato modificato con successo';

  @override
  String get runtimeChangedSuccesfully =>
      'Il comportamento del runtime è stato modificato con successo';

  @override
  String get expertModeActivated => 'Modalità esperto attivata';

  @override
  String get expertModeDeactivated => 'Modalità esperto disattivata';

  @override
  String get license => 'Licenza';

  @override
  String get retry => 'Riprova';

  @override
  String get provisioningConnectingHint =>
      'È in corso la connessione del dispositivo. L\'operazione può richiedere fino a 1 minuto.';

  @override
  String get serialnumberEmpty => 'Il numero di serie non può essere vuoto';

  @override
  String get interfaceStateInactiveDescriptionBLE =>
      'Il Bluetooth è disattivato; attivarlo per scoprire i dispositivi Bluetooth.';

  @override
  String get interfaceStateDeniedDescriptionBLE =>
      'Le autorizzazioni Bluetooth non sono state concesse.';

  @override
  String get interfaceStatePermanentDeniedDescriptionBLE =>
      'Le autorizzazioni Bluetooth non sono state concesse. Abilitarli nelle impostazioni del dispositivo.';

  @override
  String get requestPermission => 'Richiesta di autorizzazione';

  @override
  String get goToSettings => 'Vai alle impostazioni';

  @override
  String get enableBluetooth => 'Abilitare il bluetooth';

  @override
  String get installed => 'Installato';

  @override
  String teachInDialogDescription(Object type) {
    return 'Would you like to teach in your device with $type?';
  }

  @override
  String get useMatter => 'Utilizzare la materia';

  @override
  String get relayMode => 'Attivare la modalità relè';

  @override
  String get whatsNew => 'Novità di questa versione';

  @override
  String get migrationHint =>
      'Per utilizzare le nuove funzionalità è necessaria una migrazione.';

  @override
  String get migrationHeader => 'Migrazione';

  @override
  String get migrationProgress => 'Migrazione in corso...';

  @override
  String get letsGo => 'Andiamo!';

  @override
  String get noDevicesFound => 'Nessun dispositivo trovato';

  @override
  String get interfaceStateEmpty => 'Non sono stati trovati dispositivi';

  @override
  String get ssidEmpty => 'L\'SSID non può essere vuoto';

  @override
  String get passwordEmpty => 'La password non può essere vuota';

  @override
  String get settingsDeleteSettingsHeader => 'Ripristino delle impostazioni';

  @override
  String get settingsDeleteSettingsDescription =>
      'Volete davvero ripristinare tutte le impostazioni?';

  @override
  String get settingsDeleteSettingsConfirmationDescription =>
      'Tutte le impostazioni sono state ripristinate con successo.';

  @override
  String get locationNotFound => 'Posizione non trovata';

  @override
  String get timerProgramEmptySaveHint =>
      'Il programma orario è vuoto. Si desidera annullare la modifica?';

  @override
  String get timerProgramDaysEmptySaveHint =>
      'Non è stato selezionato alcun giorno. Volete comunque salvare il programma orario?';

  @override
  String get timeProgramNoDays => 'Deve essere attivato almeno un giorno';

  @override
  String timeProgramColliding(Object program) {
    return 'Il programma orario si scontra con il programma $program';
  }

  @override
  String timeProgramDuplicated(Object program) {
    return 'Il programma orario è un duplicato del programma $program';
  }

  @override
  String get screenshotZgw16 => 'Casa indipendente';

  @override
  String get interfaceStateUnknown => 'Non sono stati trovati dispositivi';

  @override
  String get settingsPinChange => 'Modifica del PIN';

  @override
  String get timeProgrammOneTime => 'una tantum';

  @override
  String get timeProgrammRepeating => 'ripetizione';

  @override
  String get generalIgnore => 'Ignorare';

  @override
  String get timeProgramChooseDay => 'Scegliere il giorno';

  @override
  String get generalToday => 'Oggi';

  @override
  String get generalTomorrow => 'Domani';

  @override
  String get bluetoothAndPINChangedSuccessfully =>
      'Bluetooth e PIN modificati con successo';

  @override
  String get generalTextDimTime => 'Tempo di oscuramento';

  @override
  String get discoverySu62Description =>
      'Interruttore orario a 1 canale Bluetooth';

  @override
  String get bluetoothAlwaysOnTitle => 'Sempre acceso';

  @override
  String get bluetoothAlwaysOnDescription =>
      'Il Bluetooth è permanentemente attivato.';

  @override
  String get bluetoothAlwaysOnHint =>
      'Nota: se questa impostazione è attivata, il dispositivo è permanentemente visibile a tutti tramite Bluetooth! Si consiglia di modificare il PIN predefinito.';

  @override
  String get bluetoothManualStartupOnTitle => 'Temporaneo su';

  @override
  String get bluetoothManualStartupOnDescription =>
      'Dopo l\'alimentazione, il Bluetooth si attiva per 3 minuti.';

  @override
  String get bluetoothManualStartupOnHint =>
      'Nota: lo standby di accoppiamento si attiva per 3 minuti e poi si spegne. Per stabilire una nuova connessione, è necessario tenere premuto il pulsante per circa 5 secondi.';

  @override
  String get bluetoothManualStartupOffTitle => 'Avvio manuale';

  @override
  String get bluetoothManualStartupOffDescription =>
      'Il Bluetooth viene attivato manualmente tramite l\'ingresso a pulsante.';

  @override
  String get bluetoothManualStartupOffHint =>
      'Nota: per attivare il Bluetooth, è necessario tenere premuto il pulsante dell\'input per circa 5 secondi.';

  @override
  String get timeProgrammOneTimeRepeatingDescription =>
      'I programmi possono essere eseguiti ripetutamente, eseguendo sempre un\'operazione di commutazione nei giorni e negli orari configurati, oppure possono essere eseguiti una sola volta all\'ora di commutazione configurata.';

  @override
  String versionHeader(Object version) {
    return 'Versione $version';
  }

  @override
  String get releaseNotesHeader => 'Note di rilascio';

  @override
  String get release30Header => 'È arrivata la nuova app Eltako Connect!';

  @override
  String get release30FeatureDesignHeader => 'Nuovo design';

  @override
  String get release30FeatureDesignDescription =>
      'L\'app è stata completamente rivista e ha un nuovo design. Ora è ancora più facile e intuitiva da usare.';

  @override
  String get release30FeaturePerformanceHeader => 'Prestazioni migliorate';

  @override
  String get release30FeaturePerformanceDescription =>
      'Godetevi un\'esperienza più fluida e tempi di caricamento ridotti, per un\'esperienza utente senza problemi.';

  @override
  String get release30FeatureConfigurationHeader =>
      'Configurazioni cross-device';

  @override
  String get release30FeatureConfigurationDescription =>
      'Salvare le configurazioni dei dispositivi e trasferirle ad altri dispositivi. Anche se non hanno lo stesso hardware, è possibile, ad esempio, trasferire la configurazione del dispositivo S2U12DBT1+1-UC a un ASSU-BT o viceversa.';

  @override
  String get release31Header =>
      'È arrivato il nuovo interruttore orario a 1 canale da incasso con Bluetooth!';

  @override
  String get release31Description => 'Cosa può fare il SU62PF-BT/UC?';

  @override
  String get release31SU62Name => 'SU62PF-BT/UC';

  @override
  String get release31DeviceNote1 => 'Fino a 60 programmi orari.';

  @override
  String get release31DeviceNote2 =>
      'Funzione Astro: L\'orologio commuta i dispositivi in base all\'alba e al tramonto.';

  @override
  String get release31DeviceNote3 =>
      'Modalità casuale: gli orari di commutazione possono essere spostati in modo casuale fino a 15 minuti.';

  @override
  String get release31DeviceNote4 =>
      'Cambio dell\'ora legale/invernale: L\'orologio passa automaticamente all\'ora legale o a quella invernale.';

  @override
  String get release31DeviceNote5 =>
      'Tensione di alimentazione e controllo universale 12-230V UC.';

  @override
  String get release31DeviceNote6 =>
      'Ingresso a pulsante per la commutazione manuale.';

  @override
  String get release31DeviceNote7 =>
      '1 contatto NA a potenziale zero 10 A/250 V CA.';

  @override
  String get release31DeviceNote8 =>
      'Esecuzione una tantum di programmi a tempo.';

  @override
  String get generalNew => 'Novità';

  @override
  String yearsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count anni fa',
      one: 'ultimo anno',
    );
    return '$_temp0.';
  }

  @override
  String monthsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count mesi fa',
      one: 'ultimo mese',
    );
    return '$_temp0';
  }

  @override
  String weeksAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count settimane fa',
      one: 'ultima settimana',
    );
    return '$_temp0.';
  }

  @override
  String daysAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count giorni fa',
      one: 'Ieri',
    );
    return '$_temp0.';
  }

  @override
  String minutesAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count minuti fa',
      one: 'Un minuto fa',
    );
    return '$_temp0.';
  }

  @override
  String hoursAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count ore fa',
      one: 'un\'ora fa',
    );
    return '$_temp0.';
  }

  @override
  String secondsAgo(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count secondi fa',
      one: 'Un secondo fa',
    );
    return '$_temp0.';
  }

  @override
  String get justNow => 'Solo ora';

  @override
  String get discoveryEsripmDescription =>
      'Relè di commutazione a impulsi IP Matter';

  @override
  String get generalTextKidsRoom => 'Funzione luce notturna';

  @override
  String generalDescriptionKidsRoom(Object mode) {
    return 'Quando si accende con un\'azione prolungata del pulsante ($mode), la luce si accende al livello di luminosità più basso dopo circa 1 secondo e si attenua lentamente finché si tiene premuto il pulsante, senza modificare l\'ultimo livello di luminosità salvato.';
  }

  @override
  String get generalTextSceneButton => 'Pulsante di scena';

  @override
  String get settingsEnOceanConfigHeader => 'Configurazione EnOcean';

  @override
  String get enOceanConfigChangedSuccessfully =>
      'La configurazione EnOcean è stata modificata con successo';

  @override
  String get activateEnOceanRepeater => 'Attivare il ripetitore EnOcean';

  @override
  String get enOceanRepeaterLevel => 'Livello del ripetitore';

  @override
  String get enOceanRepeaterLevel1 => 'Livello 1';

  @override
  String get enOceanRepeaterLevel2 => 'Livello 2';

  @override
  String get enOceanRepeaterOffDescription =>
      'I sensori non ricevono segnali wireless.';

  @override
  String get enOceanRepeaterLevel1Description =>
      'Solo i segnali wireless provenienti dai sensori vengono ricevuti, controllati e inoltrati alla massima potenza di trasmissione. I segnali wireless provenienti da altri ripetitori vengono ignorati per ridurre la quantità di dati.';

  @override
  String get enOceanRepeaterLevel2Description =>
      'Oltre ai segnali wireless dei sensori, vengono elaborati anche i segnali wireless dei ripetitori a livello 1. Un segnale wireless può quindi essere ricevuto e amplificato al massimo due volte. I ripetitori wireless non hanno bisogno di essere appresi. Ricevono e amplificano i segnali wireless di tutti i sensori wireless nella loro area di ricezione.';

  @override
  String get settingsSensorHeader => 'Sensori';

  @override
  String get sensorChangedSuccessfully =>
      'I sensori sono stati sostituiti con successo';

  @override
  String get wiredButton => 'Pulsante cablato';

  @override
  String get enOceanId => 'ID EnOcean';

  @override
  String get enOceanAddManually => 'Immettere o scansionare l\'ID EnOcean';

  @override
  String get enOceanIdInvalid => 'ID EnOcean non valido';

  @override
  String get enOceanAddAutomatically => 'Teach-in con Telegramma EnOcean';

  @override
  String get enOceanAddDescription =>
      'Il protocollo wireless EnOcean consente di accoppiare i pulsanti con l\'attuatore.\n\nScegliete l\'autoapprendimento automatico con il Telegramma EnOcean per apprendere i pulsanti premendo un solo tasto o selezionate l\'opzione manuale per scansionare o digitare l\'ID EnOcean del vostro pulsante.';

  @override
  String get enOceanTelegram => 'Telegramma';

  @override
  String enOceanCodeScan(Object sensorType) {
    return 'Inserire l\'ID EnOcean del $sensorType o scansionare il codice EnOcean-QR del $sensorType per aggiungerlo.';
  }

  @override
  String get enOceanCode => 'Codice QR EnOcean';

  @override
  String enOceanCodeScanDescription(Object sensorType) {
    return 'Cercare il codice EnOcean sul $sensorType e scansionarlo con la fotocamera.';
  }

  @override
  String get enOceanButton => 'Pulsante EnOcean';

  @override
  String get enOceanBackpack => 'Adattatore EnOcean';

  @override
  String get sensorNotAvailable => 'Nessun sensore è stato ancora accoppiato';

  @override
  String get sensorAdd => 'Aggiungere sensori';

  @override
  String get sensorCancel => 'Annullare il teach-in';

  @override
  String get sensorCancelDescription =>
      'Volete davvero cancellare il processo di insegnamento?';

  @override
  String get getEnOceanBackpack => 'Ordinate il vostro adattatore EnOcean';

  @override
  String get enOceanBackpackMissing =>
      'Per entrare nel fantastico mondo della connettività e della comunicazione perfetta, è necessario un adattatore EnOcean.\nFare clic qui per ulteriori informazioni';

  @override
  String sensorEditChangedSuccessfully(Object sensorName) {
    return '$sensorName modificato con successo';
  }

  @override
  String sensorConnectedVia(Object deviceName) {
    return 'connesso tramite $deviceName';
  }

  @override
  String get lastSeen => 'Ultimo visto';

  @override
  String get setButtonOrientation => 'Impostare l\'orientamento';

  @override
  String get setButtonType => 'Impostare il tipo di pulsante';

  @override
  String get button1Way => 'Pulsante a 1 via';

  @override
  String get button2Way => 'Pulsante a 2 vie';

  @override
  String get button4Way => 'Pulsante a 4 vie';

  @override
  String get buttonUnset => 'non impostato';

  @override
  String get button => 'Pulsante';

  @override
  String get sensor => 'Sensore';

  @override
  String sensorsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count sensori trovati',
      one: '1 sensore trovato',
      zero: 'nessun sensore trovato',
    );
    return '$_temp0.';
  }

  @override
  String get sensorSearch => 'Ricerca di sensori';

  @override
  String get searchAgain => 'Cerca di nuovo';

  @override
  String sensorTeachInHeader(Object sensorType) {
    return 'Insegna in $sensorType';
  }

  @override
  String sensorChooseHeader(Object sensorType) {
    return 'Scegliere $sensorType';
  }

  @override
  String get sensorChooseDescription =>
      'Scegliere un pulsante per l\'insegnamento';

  @override
  String get sensorCategoryDescription =>
      'Selezionare la categoria del sensore che si desidera aggiungere.';

  @override
  String get sensorName => 'Nome del pulsante';

  @override
  String get sensorNameFooter => 'Assegnate un nome al vostro pulsante';

  @override
  String sensorAddedSuccessfully(Object sensorName) {
    return '$sensorName è stato aggiunto con successo';
  }

  @override
  String sensorDelete(Object sensorType) {
    return 'Cancellare $sensorType';
  }

  @override
  String sensorDeleteHint(Object sensorName, Object sensorType) {
    return 'Si vuole veramente cancellare il $sensorType $sensorName?';
  }

  @override
  String sensorDeletedSuccessfully(Object sensorName) {
    return '$sensorName è stato cancellato con successo.';
  }

  @override
  String get buttonTapDescription =>
      'Toccare il pulsante che si desidera aggiungere.';

  @override
  String get waitingForTelegram => 'L\'attuatore attende il telegramma';

  @override
  String get copied => 'Copiato';

  @override
  String pairingFailed(Object sensorType) {
    return '$sensorType già accoppiato';
  }

  @override
  String get generalDescriptionUniversalbutton =>
      'Con il pulsante universale, la direzione viene invertita rilasciando brevemente il pulsante. I comandi brevi accendono o spengono il sistema.';

  @override
  String get generalDescriptionDirectionalbutton =>
      'Il pulsante di direzione è \"accendi e oscura\" in alto e \"spegni e oscura\" in basso.';

  @override
  String get matterForwardingDescription => 'Inoltro dei telegrammi Matter';

  @override
  String get none => 'Nessuno';

  @override
  String get buttonNoneDescription => 'Il pulsante non ha alcuna funzionalità';

  @override
  String get buttonUnsetDescription =>
      'Il pulsante non ha un comportamento impostato';

  @override
  String get sensorButtonTypeChangedSuccessfully =>
      'Il tipo di pulsante è stato modificato con successo';

  @override
  String forExample(Object example) {
    return 'ad esempio $example';
  }

  @override
  String get enOceanQRCodeInvalidDescription =>
      'Possibile solo a partire dalla data di produzione 44/20';

  @override
  String get input => 'Ingresso';

  @override
  String get buttonSceneValueOverride =>
      'Sovrascrivere il valore del pulsante di scena';

  @override
  String get buttonSceneValueOverrideDescription =>
      'Il valore del pulsante di scena verrà sovrascritto con il valore attuale della luminosità attraverso una pressione prolungata del pulsante';

  @override
  String get buttonSceneDescription =>
      'Il pulsante di scena si accende a un valore di luminosità specifico.';

  @override
  String get buttonPress => 'Pulsante premuto';

  @override
  String get triggerOn =>
      'Pulsante universale o pulsante di direzione premuto sul lato di accensione';

  @override
  String get triggerOff =>
      'Pulsante universale o pulsante di direzione premuto sul lato di spegnimento';

  @override
  String get centralOn => 'Centrale On';

  @override
  String get centralOff => 'Centrale Off';

  @override
  String get centralButton => 'Pulsante centrale';

  @override
  String get enOceanAdapterNotFound => 'Nessun adattatore EnOcean trovato';

  @override
  String get updateRequired => 'Aggiornamento richiesto';

  @override
  String get updateRequiredDescription =>
      'La vostra applicazione richiede un aggiornamento per supportare questo nuovo dispositivo.';

  @override
  String get release32Header =>
      'Il nuovo BR64 con Matter ed EnOcean e il nuovo interruttore orario da incasso Bluetooth SU62PF-BT/UC sono ora disponibili!';

  @override
  String get release32EUD64Header =>
      'È arrivato il nuovo dimmer a 1 canale da incasso con Matter over Wi-Fi e fino a 300W!';

  @override
  String get release32EUD64Note1 =>
      'Configurazione della velocità di oscuramento, della velocità di accensione/spegnimento, della modalità luce notturna/autospegnimento e molto altro ancora.';

  @override
  String get release32EUD64Note2 =>
      'Le funzionalità dell\'EUD64NPN-IPM possono essere ampliate tramite adattatori, come l\'adattatore EnOcean EOA64.';

  @override
  String get release32EUD64Note3 =>
      'Fino a 30 interruttori wireless EnOcean possono essere collegati direttamente all\'EUD64NPN-IPM in combinazione con l\'adattatore EnOcean EOA64 e inoltrati a Matter.';

  @override
  String get release32EUD64Note4 =>
      'Due ingressi a pulsante cablati possono essere collegati direttamente all\'EUD64NPN-IPM o inoltrati a Matter.';

  @override
  String get release32ESR64Header =>
      'È arrivato il nuovo attuatore da incasso a 1 canale a potenziale zero con Matter over Wi-Fi e fino a 16A!';

  @override
  String get release32ESR64Note1 =>
      'Configurazione di varie funzioni come interruttore a impulsi (ES), funzione relè (ER), normalmente chiusa (ER-Inversa) e molto altro ancora.';

  @override
  String get release32ESR64Note2 =>
      'Le funzionalità dell\'ESR64PF-IPM possono essere ampliate tramite adattatori, come l\'adattatore EnOcean EOA64.';

  @override
  String get release32ESR64Note3 =>
      'Fino a 30 interruttori wireless EnOcean possono essere collegati direttamente all\'ESR64PF-IPM in combinazione con l\'adattatore EnOcean EOA64 e inoltrati a Matter.';

  @override
  String get release32ESR64Note4 =>
      'Un ingresso a pulsante cablato può essere collegato direttamente al ESR64PF-IPM o inoltrato a Matter.';

  @override
  String buttonsFound(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count pulsanti trovati',
      one: '1 pulsante trovato',
      zero: 'Nessun pulsante trovato',
    );
    return '$_temp0';
  }

  @override
  String get doubleImpuls => 'con un doppio impulso';

  @override
  String get impulseDescription =>
      'Se il canale è acceso, viene spento da un impulso.';

  @override
  String get locationServiceEnable => 'Attivare la posizione';

  @override
  String get locationServiceDisabledDescription =>
      'La posizione è disattivata. La versione del sistema operativo ha bisogno della posizione per poter trovare i dispositivi Bluetooth.';

  @override
  String get locationPermissionDeniedNoPosition =>
      'Le autorizzazioni di localizzazione non sono state concesse. La versione del sistema operativo in uso richiede i permessi di localizzazione per poter trovare i dispositivi Bluetooth. Consentire l\'autorizzazione alla localizzazione nelle impostazioni del dispositivo.';

  @override
  String get interfaceStatePermanentDeniedDescriptionDevicesAround =>
      'L\'autorizzazione per i dispositivi vicini non è stata concessa. Attivare l\'autorizzazione nelle impostazioni del dispositivo.';

  @override
  String get permissionNearbyDevices => 'Dispositivi vicini';

  @override
  String get release320Header =>
      'È arrivato il nuovo e più potente dimmer universale EUD12NPN-BT/600W-230V!';

  @override
  String get release320EUD600Header =>
      'Cosa può fare il nuovo dimmer universale?';

  @override
  String get release320EUD600Note1 =>
      'Dimmer universale con potenza fino a 600W';

  @override
  String get release320EUD600Note2 =>
      'Espandibile con l\'estensione di potenza LUD12 fino a 3800W';

  @override
  String get release320EUD600Note3 =>
      'Funzionamento locale con pulsante universale o direzionale';

  @override
  String get release320EUD600Note4 => 'Funzioni centrali On / Off';

  @override
  String get release320EUD600Note5 =>
      'Ingresso per rilevatore di movimento per una maggiore comodità';

  @override
  String get release320EUD600Note6 =>
      'Timer integrato con 10 programmi di commutazione';

  @override
  String get release320EUD600Note7 => 'Funzione astronomica';

  @override
  String get release320EUD600Note8 => 'Luminosità di accensione individuale';

  @override
  String get mqttClientCertificate => 'Certificato del cliente';

  @override
  String get mqttClientCertificateHint => 'Certificato cliente MQTT';

  @override
  String get mqttClientKey => 'Chiave cliente';

  @override
  String get mqttClientKeyHint => 'Chiave cliente MQTT';

  @override
  String get mqttClientPassword => 'Password Cliente';

  @override
  String get mqttClientPasswordHint => 'Password cliente MQTT';

  @override
  String get mqttEnableHomeAssistantDiscovery =>
      'Attivare il rilevamento MQTT di HomeAssistant';

  @override
  String get modbusTcp => 'Modbus TCP';

  @override
  String get enableInterface => 'Attivare l\'interfaccia';

  @override
  String get busAddress => 'Indirizzo Bus';

  @override
  String busAddressWithAddress(Object index) {
    return 'Indirizzo Bus indice';
  }

  @override
  String get deviceType => 'Tipo di dispositivo';

  @override
  String registerTable(num count) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: 'Tabella dei registri',
      one: 'Tabella dei registri',
    );
    return '$_temp0';
  }

  @override
  String get currentValues => 'Valori attuali';

  @override
  String get requestRTU => 'Interrogazione RTU';

  @override
  String get requestPriority => 'Priorità della query';

  @override
  String get mqttForwarding => 'Inoltro MQTT';

  @override
  String get historicData => 'Dati storici';

  @override
  String get dataFormat => 'Formato dei dati';

  @override
  String get dataType => 'Tipo di dati';

  @override
  String get description => 'Descrizione';

  @override
  String get readWrite => 'Lettura/Scrittura';

  @override
  String get unit => 'Unità';

  @override
  String get registerTableReset => 'Tabella dei registri di reset';

  @override
  String get registerTableResetDescription =>
      'La tabella dei registri deve essere davvero azzerata?';

  @override
  String get notConfigured => 'Non configurato';

  @override
  String get release330ZGW16Header =>
      'Aggiornamento importante per il modello ZGW16WL-IP';

  @override
  String get release330Header =>
      'ZGW16WL-IP con un massimo di 16 contatori elettrici';

  @override
  String get release330ZGW16Note1 =>
      'Supporta fino a 16 contatori elettrici ELTAKO Modbus';

  @override
  String get release330ZGW16Note2 => 'Supporto Modbus TCP';

  @override
  String get release330ZGW16Note3 => 'Supporto MQTT Discovery';

  @override
  String get screenshotButtonLivingRoom => 'Pulsante del soggiorno';

  @override
  String get registerChangedSuccessfully => 'Registro modificato con successo';

  @override
  String get serverCertificateEmpty =>
      'Il certificato del server non può essere vuoto';

  @override
  String get registerTemplates => 'Modelli di registro';

  @override
  String get registerTemplateChangedSuccessfully =>
      'Il modello di registro è stato modificato con successo';

  @override
  String get registerTemplateReset => 'Azzeramento del modello di registro';

  @override
  String get registerTemplateResetDescription =>
      'Il modello di registro deve essere davvero azzerato?';

  @override
  String get registerTemplateNotAvailable =>
      'Nessun modello di registro disponibile';

  @override
  String get rename => 'Rinominare';

  @override
  String get registerName => 'Nome del registro';

  @override
  String get registerRenameDescription =>
      'Inserire un nome personalizzato per il registro';

  @override
  String get restart => 'Riavviare il dispositivo';

  @override
  String get restartDescription => 'Volete davvero riavviare il dispositivo?';

  @override
  String get restartConfirmationDescription =>
      'Il dispositivo si sta riavviando';

  @override
  String get deleteAllElectricityMeters =>
      'Cancellare tutti i contatori elettrici';

  @override
  String get deleteAllElectricityMetersDescription =>
      'Volete davvero cancellare tutti i contatori elettrici?';

  @override
  String get deleteAllElectricityMetersConfirmationDescription =>
      'Tutti i contatori elettrici sono stati cancellati con successo';

  @override
  String get resetAllElectricityMeters =>
      'Azzeramento di tutte le configurazioni dei contatori elettrici';

  @override
  String get resetAllElectricityMetersDescription =>
      'Volete davvero resettare tutte le configurazioni dei contatori elettrici?';

  @override
  String get resetAllElectricityMetersConfirmationDescription =>
      'Tutte le configurazioni dei contatori elettrici sono state ripristinate con successo.';

  @override
  String get deleteElectricityMeterHistories =>
      'Cancellare tutte le cronologie dei contatori elettrici';

  @override
  String get deleteElectricityMeterHistoriesDescription =>
      'Volete davvero cancellare tutte le cronologie dei contatori elettrici?';

  @override
  String get deleteElectricityMeterHistoriesConfirmationDescription =>
      'Tutte le cronologie dei contatori elettrici sono state cancellate con successo';

  @override
  String get multipleElectricityMetersSupportMissing =>
      'Il dispositivo supporta attualmente un solo contatore elettrico. Aggiornare il firmware.';

  @override
  String get consumptionWithUnit => 'Utilizzo (kWh)';

  @override
  String get exportWithUnit => 'Consegna (kWh)';

  @override
  String get importWithUnit => 'Consumo (kWh)';

  @override
  String get resourceWarningHeader => 'Limiti delle risorse';

  @override
  String mqttAndTcpResourceWarning(Object protocol) {
    return 'Il funzionamento contemporaneo di MQTT e Modbus TCP non è possibile a causa delle risorse di sistema limitate. Disattivare prima $protocol.';
  }

  @override
  String get mqttEnabled => 'MQTT abilitato';

  @override
  String get redirectMQTT => 'Andare alle Impostazioni MQTT';

  @override
  String get redirectModbus => 'Andare alle impostazioni Modbus';

  @override
  String get unsupportedSettingDescription =>
      'Con la versione attuale del firmware, alcune impostazioni del dispositivo non sono supportate. Aggiornare il firmware per utilizzare le nuove funzioni';

  @override
  String get updateNow => 'Aggiornare ora';

  @override
  String get zgw241Hint =>
      'Con questo aggiornamento, Modbus TCP è abilitato per impostazione predefinita e MQTT è disabilitato. Questo può essere modificato nelle impostazioni. Con il supporto di un massimo di 16 contatori, sono state apportate molte ottimizzazioni; ciò può comportare modifiche alle impostazioni del dispositivo. Si prega di riavviare il dispositivo dopo aver regolato le impostazioni.';

  @override
  String get deviceConfigChangedSuccesfully =>
      'Il comportamento del runtime è stato modificato con successo';

  @override
  String get deviceConfiguration => 'Configurazione del dispositivo';

  @override
  String get tiltModeToggle => 'Modalità di inclinazione';

  @override
  String get tiltModeToggleFooter =>
      'Se il dispositivo è stato configurato in Matter, tutte le funzioni devono essere riconfigurate in quella sede.';

  @override
  String get shaderMovementDirection => 'Inversione su/giù';

  @override
  String get shaderMovementDirectionDescription =>
      'Invertire la direzione del movimento del motore verso l\'alto o verso il basso.';

  @override
  String get tiltTime => 'Tempo di esecuzione dell\'inclinazione';

  @override
  String changeTiltModeDialogTitle(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0 funzione di inclinazione';
  }

  @override
  String changeTiltModeDialogConfirmation(String target) {
    String _temp0 = intl.Intl.selectLogic(target, {
      'true': 'Enable',
      'false': 'Disable',
      'other': 'Change',
    });
    return '$_temp0';
  }

  @override
  String get generalTextSlatSetting => 'Impostazione delle lamelle';

  @override
  String get generalTextPosition => 'Posizione';

  @override
  String get generalTextSlatPosition => 'Posizione delle lamelle';

  @override
  String get slatSettingDescription =>
      'Attivare la funzione lamelle per regolare l\'angolo di inclinazione delle tende.';

  @override
  String get scenePositionSliderDescription =>
      'La posizione definisce l\'apertura o la chiusura delle tende.';

  @override
  String get sceneSlatPositionSliderDescription =>
      'La posizione delle lamelle definisce l\'angolo di inclinazione delle lamelle della tapparella, se la funzione di inclinazione è attivata.';

  @override
  String get referenceRun => 'Esecuzione della calibrazione';

  @override
  String get slatAutoSettingHint =>
      'In questa modalità, la posizione delle tende non ha importanza prima che le lamelle si regolino nella posizione di inclinazione desiderata.';

  @override
  String get slatReversalSettingHint =>
      'In questa modalità, le tende si chiudono completamente prima che le lamelle si regolino nella posizione di inclinazione desiderata.';

  @override
  String get release340Header =>
      'È arrivato il nuovo attuatore per ombreggiatura ad incasso ESB64NP-IPM!';

  @override
  String get release340ESB64Header => 'Di cosa è capace l\'ESB64NP-IPM?';

  @override
  String get release340ESB64Note1 =>
      'Il nostro attuatore per ombreggiature certificato Matter Gateway con funzione lamellare opzionale';

  @override
  String get release340ESB64Note2 =>
      'Due ingressi a pulsante cablati per la commutazione manuale e l\'inoltro a Matter';

  @override
  String get release340ESB64Note3 =>
      'Espandibile con l\'adattatore EnOcean (EOA64). Ad esempio con il pulsante EnOcean wireless F4T55';

  @override
  String get release340ESB64Note4 =>
      'Aperto alle integrazioni grazie all\'API REST basata sullo standard OpenAPI';

  @override
  String get activateTiltModeDialogText =>
      'Se si attiva la funzione di inclinazione, tutte le impostazioni andranno perse. Siete sicuri di voler abilitare la funzione di inclinazione?';

  @override
  String get deactivateTiltModeDialogText =>
      'Se la funzione di inclinazione viene disattivata, tutte le impostazioni andranno perse. Siete sicuri di voler disabilitare la funzione di inclinazione?';

  @override
  String get buttonSceneESBDescription =>
      'Il pulsante di scena imposta la tapparella su una posizione specifica.';

  @override
  String get sceneValueOverride =>
      'Tenere premuto il pulsante per 4 secondi per sovrascrivere la posizione con il valore attuale (valore di partenza).';

  @override
  String get sceneCalibration =>
      'La corsa di calibrazione sposta la tapparella completamente verso il basso e verso l\'alto una volta per determinare le posizioni finali e abilitare il rilevamento della posizione.';

  @override
  String get up => 'Su';

  @override
  String get down => 'In basso';
}
