//
//  i_sensor.dart
//  EltakoConnect
//
//  Created by <PERSON> on 14.06.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/device_information.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/enumeration/sensor_type_category.dart';
import 'package:eltako_connect/enumeration/sub_service.dart';
import 'package:eltako_connect/extension/map_extension.dart';
import 'package:eltako_connect/mixin/config_output.dart';
import 'package:eltako_connect/mixin/extended_output.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor/wired_button.dart';
import 'package:flutter/foundation.dart';

/// Interface for sensors
abstract class ISensor extends ChangeNotifier with ConfigOutput, ExtendedOutput {
  // region [PROPERTIES]

  /// Sensor category
  final SensorTypeCategory category;

  /// Product ID
  final String productId;

  /// Channel count
  int channelCount;

  /// ID
  String id;

  /// Name
  String get name => _name;

  /// Name
  set name(String value) {
    _name = value;
    notifyListeners();
  }

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Sensor type
  SensorType get sensorType;

  /// Sensor services
  List<SubService> get sensorServices;

  /// Sensor information
  Map<DeviceInformationType, String?> get information;

  // endregion

  // region [VARIABLES]

  /// Name
  String _name;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  ISensor({
    required this.category,
    required this.productId,
    required this.channelCount,
    required this.id,
    required String name,
  }) : _name = name;

  /// Create sensor from [map]
  static ISensor? fromMap(Map<String, dynamic> map) {
    // Get sensor type
    final sensorCategory = SensorTypeCategory.fromKey(map.get('type', fallback: ''));
    if (sensorCategory == null) return null;

    // Create sensor
    switch (sensorCategory) {
      case SensorTypeCategory.enOceanButton:
        return EnOceanButton.fromMap(map);
      case SensorTypeCategory.wiredButton:
        return WiredButton.fromMap(map);
      default:
        return null;
    }
  }

  @override
  String toString() => 'Sensor: $name';

  // endregion
}
