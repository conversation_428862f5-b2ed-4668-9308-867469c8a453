//
//  button_config.dart
//  EltakoConnect
//
//  Created by <PERSON> on 09.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/button_channel_configuration_function.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/enumeration/device_type_category.dart';
import 'package:eltako_connect/enumeration/product_characteristic_identifier.dart';
import 'package:eltako_connect/enumeration/push_button_state.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sub_device_configuration_add_on_type.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/map_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/mixin/config_output.dart';
import 'package:eltako_connect/mixin/extended_output.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/i_button_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/i_sensor_configuration.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/sub_device_sub_sensor_map.dart';
import 'package:eltako_connect/model/sub_device/characteristic/input_state_characteristic.dart';
import 'package:eltako_connect/model/sub_device/characteristic/push_button_state_characteristic.dart';

/// Class for storing button configurations
class ButtonConfig with ConfigOutput, ExtendedOutput {
  // region [PROPERTIES]

  /// Position
  final SensorChannelPosition position;

  /// Currently pressed
  final PushButtonState state;

  /// Type category of corresponding device
  final DeviceTypeCategory category;

  /// Automation
  IButtonConfiguration? configuration;

  /// Configuration logic
  ButtonChannelConfigurationLogic configurationLogic;

  /// Configuration function
  ButtonChannelConfigurationFunction function;

  /// Fallback delay
  Duration fallbackDelay;

  /// Target value
  int? value;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Controlled by matter
  bool get isMatterControlled => configurationLogic == ButtonChannelConfigurationLogic.matterControlled;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  ButtonConfig({
    required this.position,
    required this.category,
    required this.configurationLogic,
    this.function = ButtonChannelConfigurationFunction.none,
    this.state = PushButtonState.unknown,
    this.fallbackDelay = Duration.zero,
    this.value,
    this.configuration,
  });

  /// Unset button config
  factory ButtonConfig.unset({required SensorChannelPosition position, required DeviceTypeCategory category}) =>
      ButtonConfig(
        position: position,
        category: category,
        configurationLogic: ButtonChannelConfigurationLogic.unset,
        state: PushButtonState.notPressed,
      );

  /// Create button config from push button state [characteristic] and corresponding [sensorMap]
  static ButtonConfig? fromPushButtonStateCharacteristic(
    PushButtonStateCharacteristic characteristic, {
    required SubDeviceSubSensorMap sensorMap,
    required DeviceType deviceType,
  }) {
    final identifier = characteristic.characteristicInfo.identifier;
    return _fromCharacteristicInfo(
      identifier: identifier,
      state: characteristic.value,
      sensorMap: sensorMap,
      matterControlled: false,
      deviceType: deviceType,
    );
  }

  /// Create button config from input state [characteristic] and corresponding [sensorMap]
  static ButtonConfig? fromInputStateCharacteristic(
    InputStateCharacteristic characteristic, {
    required SubDeviceSubSensorMap sensorMap,
    required DeviceType deviceType,
  }) {
    final identifier = characteristic.characteristicInfo.identifier;
    return _fromCharacteristicInfo(
      identifier: identifier,
      state: characteristic.value?.pushButtonState,
      sensorMap: sensorMap,
      matterControlled: false,
      deviceType: deviceType,
    );
  }

  /// Create button config from [map]
  static ButtonConfig? fromMap(Map<String, dynamic> map) {
    // Extract values
    final position = SensorChannelPosition.fromKey(map.get('position', fallback: ''));
    final category = DeviceTypeCategory.fromKey(map.get('category', fallback: ''));
    final configurationLogic = ButtonChannelConfigurationLogic.fromKey(map.get('configuration_logic', fallback: ''));
    final function = ButtonChannelConfigurationFunction.fromKey(map.get('function', fallback: ''));
    final fallbackDelay = Duration(milliseconds: map.get('delay', fallback: 0));
    final value = map.getOrNull<int>('value');
    final state = PushButtonState.fromKey(map.get('state', fallback: '')) ?? PushButtonState.unknown;
    if (position == null || category == null || configurationLogic == null || function == null) {
      return null;
    }

    // Get configuration
    final configuration =
        ISensorConfiguration.fromMap(map.get('configuration', fallback: {}))?.tryCast<IButtonConfiguration>();

    // Create and return data
    return ButtonConfig(
      position: position,
      category: category,
      configurationLogic: configurationLogic,
      configuration: configuration,
      function: function,
      fallbackDelay: fallbackDelay,
      value: value,
      state: state,
    );
  }

  @override
  Map<String, dynamic> toMap({bool forConfig = false}) => {
    'position': position.key,
    'category': category.key,
    'configuration_logic': configurationLogic.key,
    'configuration': forConfig ? configuration?.toConfigMap() : configuration?.toMap(),
    'function': function.key,
    if (!forConfig) 'state': state.key,
    'delay': fallbackDelay.inMilliseconds,
    if (value != null) 'value': value,
  };

  @override
  Map<String, dynamic> toConfigMap() => toMap(forConfig: true);

  @override
  String toString() => '${position.name}: ${configurationLogic.name} (${function.name}) [${state.name}])';

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Create button config from characteristic info
  static ButtonConfig? _fromCharacteristicInfo({
    required ProductCharacteristicIdentifier identifier,
    required PushButtonState? state,
    required SubDeviceSubSensorMap sensorMap,
    required bool matterControlled,
    required DeviceType deviceType,
  }) {
    // Get position
    final position = SensorChannelPosition.fromSensorMap(identifier, sensorMap: sensorMap, deviceType: deviceType);
    if (position == null) return null;

    // Get data from automation
    final automation = sensorMap.automations?.firstWhereOrNull((a) => a.configuration.inputIdentifier == identifier);
    ButtonChannelConfigurationLogic configuration =
        automation != null
            ? ButtonChannelConfigurationLogic.fromAutomation(automation, deviceTypeCategory: deviceType.category)
            : ButtonChannelConfigurationLogic.unset;
    ButtonChannelConfigurationFunction function =
        automation != null
            ? ButtonChannelConfigurationFunction.fromAutomation(automation, deviceTypeCategory: deviceType.category)
            : ButtonChannelConfigurationFunction.none;

    // Check if matter controlled
    if (sensorMap.matterSubSensor?.config.channels.firstWhereOrNull(
          (c) => c.identifier == identifier.eltakoKey && c.isEnabled,
        ) !=
        null) {
      configuration = ButtonChannelConfigurationLogic.matterControlled;
      function = ButtonChannelConfigurationFunction.none;
    }

    // Get add-ons
    final fallbackDelayRaw =
        automation?.configuration.addOns
            .firstWhereOrNull((a) => a.identifier == SubDeviceConfigurationAddOnType.offTimer.eltakoKey)
            ?.time;
    final fallbackDelay = fallbackDelayRaw != null ? Duration(milliseconds: fallbackDelayRaw) : Duration.zero;
    final value =
        automation?.configuration.addOns
            .firstWhereOrNull((a) => a.identifier == SubDeviceConfigurationAddOnType.targetValue.eltakoKey)
            ?.data;

    // Create and return button config
    return ButtonConfig(
      position: position,
      category: deviceType.category,
      configurationLogic: configuration,
      function: function,
      state: state ?? PushButtonState.unknown,
      fallbackDelay: fallbackDelay,
      value: value,
      configuration: automation?.configuration.logicConfiguration?.tryCast<IButtonConfiguration>(),
    );
  }

  // endregion
}
