//
//  i_service.dart
//  EltakoConnect
//
//  Created by <PERSON> on 11.12.23.
//  Copyright © 2023 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/enumeration/service_key.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/i_service_data.dart';
import 'package:eltako_connect/service/i_service_info.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/widgets.dart';

/// Interface for services
abstract class IService<T extends IServiceData> with ChangeNotifier {
  // region [PROPERTIES]

  /// Key
  final ServiceKey key;

  /// Image path
  final dynamic icon;

  /// Device
  final IDevice device;

  /// Service info
  late final IServiceInfo serviceInfo;

  /// Data
  T? data;

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Name
  String get name => key.fullName;

  /// Localized name
  String get localizedName => key.localizedServiceName();

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  IService({required this.key, required this.icon, required this.device, IServiceInfo? serviceInfo}) {
    if (serviceInfo != null) this.serviceInfo = serviceInfo;
  }

  /// Get service widget
  Widget getWidget() => NavigationView.serviceViewNotImplemented(name: localizedName);

  /// Update widget from service data
  void updateWidgetFromServiceData() => notifyListeners();

  /// Get service data
  Future<DataResult<T>> getData({bool forceRefresh = false, bool prioritized = false});

  /// Set service data
  Future<VoidResult> setData(T data, {bool prioritized = false});

  /// Refresh
  void refresh() => notifyListeners();

  /// Check if service is compatible with [data]
  bool isCompatibleWith(IServiceData data) => data.runtimeType == T;

  // endregion
}
