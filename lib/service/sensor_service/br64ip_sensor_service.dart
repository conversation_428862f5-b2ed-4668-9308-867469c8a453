//
//  br64ip_sensor_service.dart
//  EltakoConnect
//
//  Created by <PERSON> on 13.06.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:eltako_connect/connection/wifi/wifi_demo_connection.dart';
import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/device/i_wifi_device.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_type.dart';
import 'package:eltako_connect/enumeration/enocean_state.dart';
import 'package:eltako_connect/enumeration/http_method.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/enumeration/matter_sub_sensor_channel_feature.dart';
import 'package:eltako_connect/enumeration/product_category.dart';
import 'package:eltako_connect/enumeration/product_characteristic_identifier.dart';
import 'package:eltako_connect/enumeration/product_type.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_connection_state.dart';
import 'package:eltako_connect/enumeration/sensor_connection_type.dart';
import 'package:eltako_connect/enumeration/sensor_orientation.dart';
import 'package:eltako_connect/enumeration/sub_device_configuration_logic.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/map_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_button_sensor.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_code_result.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_pairing_body.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/pairing/i_scan_result.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor/wired_button.dart';
import 'package:eltako_connect/model/sensor/sensor_connection.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/enocean_sub_sensor.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/enocean_sub_sensor_config.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/i_sub_sensor.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/matter_sub_sensor.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/matter_sub_sensor_channel_feature_config.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/matter_sub_sensor_config.dart';
import 'package:eltako_connect/model/sensor/sub_sensor/sub_device_sub_sensor_map.dart';
import 'package:eltako_connect/model/sub_device/sub_device.dart';
import 'package:eltako_connect/model/sub_device/sub_device_automation.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/automation_service/i_automation_service.dart';
import 'package:eltako_connect/service/enocean_config_service/i_enocean_config_service.dart';
import 'package:eltako_connect/service/enocean_config_service/wifi_enocean_config_service.dart';
import 'package:eltako_connect/service/matter_sub_sensor_service/i_matter_sub_sensor_service.dart';
import 'package:eltako_connect/service/sensor_service/br64ip_sensor_service_demo_factory.dart';
import 'package:eltako_connect/service/sensor_service/i_sensor_service.dart';
import 'package:eltako_connect/service/sensor_service/i_wifi_sensor_service.dart';
import 'package:eltako_connect/service/sensor_service/sensor_data.dart';
import 'package:eltako_connect/service/shader_service/i_shader_service.dart';
import 'package:eltako_connect/service/sub_device_service/i_sub_device_service.dart';
import 'package:eltako_connect/service/sub_sensor_service/i_sub_sensor_service.dart';
import 'package:eltako_connect/service/wifi_service_info.dart';
import 'package:eltako_connect/storage/json/br62_product_database.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:get_it/get_it.dart';

/// Wifi sensor service for BR64 devices
class Br64ipSensorService extends IWifiSensorService {
  // region [COMPUTED PROPERTIES]

  /// URL for device scanning
  String get scanUrl => '${iWifiDevice.baseURL}/services/enocean/scan';

  /// URL for sensors
  String get sensorsUrl => '${iWifiDevice.baseURL}/services/enocean/devices';

  /// Use demo mode
  bool get demoMode => iWifiConnection is WifiDemoConnection;

  // endregion

  // region [VARIABLES]

  /// Sensor maps
  final Map<String, SubDeviceSubSensorMap> _sensorMaps;

  /// Demo factory
  final Br64ipSensorServiceDemoFactory _demoFactory;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  Br64ipSensorService(IDevice device, {required super.supportedSensorConnectionTypes})
    : _demoFactory = Br64ipSensorServiceDemoFactory(device),
      _sensorMaps = {},
      super(
        device: device,
        serviceInfo: WifiServiceInfo(createEndpointURL: (m) => '${(device as IWifiDevice).baseURL}/devices'),
      );

  @override
  Future<DataResult<List<SensorConnection>>> getAvailableConnections() async {
    // Check if in demo mode
    if (demoMode) return _demoFactory.getAvailableConnections();

    // Create states
    final enOceanState = await _getEnOceanState();
    final states = [
      if (supportedSensorConnectionTypes.contains(SensorConnectionType.wired)) SensorConnection.wired(),
      if (supportsEnOcean) SensorConnection.enOcean(state: enOceanState),
      if (supportsMatter) SensorConnection.matter(),
    ];

    // Update supported types and return connections
    supportedSensorConnectionTypes =
        states.where((s) => s.state == SensorConnectionState.connected).map((s) => s.type).toList();
    return DataResult.success(data: states);
  }

  @override
  Future<DataResult<bool>> isTiltFeatureEnabled() async {
    // Check if tilt feature is supported
    if (!supportsTiltFeature) return const DataResult.success(message: 'Tilt feature not supported', data: false);

    // Check if in demo mode
    if (demoMode) return _demoFactory.isTiltFeatureEnabled();

    try {
      // Get service
      final shaderService = device.services.firstOfTypeOrNull<IShaderService>();
      if (shaderService == null) throw Exception('Failed to get shader service');

      // Get data
      final result = await shaderService.getData(forceRefresh: true, prioritized: true);
      final tiltEnabled = result.data?.shader.tiltMode?.value?.isEnabled;
      if (tiltEnabled == null) throw Exception('Failed to get shader data:\n${result.message}');

      // Return result
      return DataResult.success(data: tiltEnabled);
    } catch (ex) {
      error('${device.tag} $ex', category: LogCategory.device);
      return DataResult.error(message: ex.toString(), error: LocalizedError.deviceError);
    }
  }

  @override
  Future<DataResult<SensorData>> getData({bool forceRefresh = true, bool prioritized = true}) async {
    try {
      // Check if in demo mode
      if (demoMode) return _demoFactory.getData();

      // Collect data
      final subDevices = await _getSubDevices(forceRefresh: forceRefresh, prioritized: prioritized);
      final subSensors = await _getSubSensors(forceRefresh: forceRefresh, prioritized: prioritized);
      final automations = await _getAutomations(forceRefresh: forceRefresh, prioritized: prioritized);
      final matterSubSensors = await _getMatterSensors(forceRefresh: forceRefresh, prioritized: prioritized);
      final actuatorDevices = subDevices.where((d) => d.deviceInfo.category == ProductCategory.actuator).toList();

      // Map sensors to sub devices
      final sensors = <ISensor>[];
      final productDatabase = GetIt.I.get<BR62ProductDatabase>();
      _sensorMaps.clear();
      for (final subDevice in subDevices) {
        // Collect data
        final sensorAutomations = automations.where((a) => a.configuration.inputId == subDevice.id).toList();
        final matterSubSensor = matterSubSensors.firstWhereOrNull((s) => s.id == subDevice.id);
        if (subDevice.deviceInfo.type == ProductType.input) {
          final sensorMap = SubDeviceSubSensorMap(
            subDevice: subDevice,
            actuatorDevices: actuatorDevices,
            automations: sensorAutomations,
            matterSubSensor: matterSubSensor,
          );
          final sensor = WiredButton.fromSensorMap(sensorMap, deviceType: device.deviceInfo.deviceType);
          if (sensor == null) {
            warning('Could not cast sensor with product ID ${subDevice.deviceInfo.id}');
            continue;
          }
          sensors.add(sensor);
          _sensorMaps[sensor.id] = sensorMap;
          continue;
        }

        final subSensor = subSensors.firstWhereOrNull((s) => s.id == subDevice.id);
        final enOceanSubSensor = subSensor?.tryCast<EnOceanSubSensor>();
        if (subSensor == null) continue;

        // Create sensor map
        final sensorMap = SubDeviceSubSensorMap(
          subDevice: subDevice,
          actuatorDevices: actuatorDevices,
          enOceanSubSensor: enOceanSubSensor,
          matterSubSensor: matterSubSensor,
          automations: sensorAutomations,
        );

        // Get sensor type
        final sensorType = productDatabase.getSubDeviceInfo(subDevice.deviceInfo.id)?.type;
        if (sensorType == null) {
          warning('Could not get product info about sensor with product ID ${subDevice.deviceInfo.id}');
          continue;
        }

        // Cast and add sensor
        final ISensor? sensor;
        switch (sensorType) {
          case ProductType.button:
            sensor = EnOceanButton.fromSensorMap(sensorMap, deviceType: device.deviceInfo.deviceType);
          default:
            warning('Unknown sub sensor type: ${sensorType.index}');
            continue;
        }
        if (sensor == null) {
          warning('Could not cast sensor with product ID ${subDevice.deviceInfo.id}');
          continue;
        }
        sensors.add(sensor);

        /// Save sensor map
        _sensorMaps[sensor.id] = sensorMap;
      }

      // Create and return sensor data
      final data = SensorData(sensors: sensors);
      this.data = data;
      return DataResult.success(data: data);
    } catch (e) {
      final message = 'Failed to get sensor data:\n$e';
      warning('${device.tag} $message');
      return DataResult.error(message: message, error: LocalizedError.deviceError);
    }
  }

  @override
  Future<VoidResult> setSensor(ISensor sensor, {bool prioritized = true}) async {
    try {
      // Check if in demo mode
      if (demoMode) return _demoFactory.setSensor(sensor);

      // Check sensor type
      final buttonSensor = sensor.tryCast<IButtonSensor>();
      if (buttonSensor == null) throw Exception('Failed to cast sensor to IButtonSensor');

      // Get sensor map
      final sensorMap = _getSensorMap(sensor);
      if (sensorMap == null) throw Exception('Failed to get sensor map for sensor ${sensor.id}');

      // Update sensor map
      sensorMap.subDevice.name = sensor.name;

      // Update sensor orientation and sub device
      await _setSensorOrientation(sensor, prioritized: prioritized);
      await _updateSubDevice(sensorMap.subDevice);

      // Update automation (configuration logic)
      for (final buttonConfiguration in buttonSensor.channels) {
        // Get identifier
        final identifier = buttonConfiguration.position.createIdentifier(
          productId: sensorMap.subDevice.deviceInfo.id,
          deviceType: device.deviceInfo.deviceType,
        );
        if (identifier == null) {
          throw Exception('Failed to get identifier for channel ${buttonConfiguration.position.key}');
        }

        // Check configuration
        final ButtonChannelConfigurationType from;
        if (sensorMap.matterSubSensor?.config.channels.any(
              (c) => c.identifier == identifier.eltakoKey && c.isEnabled,
            ) ??
            false) {
          from = ButtonChannelConfigurationType.matter;
        } else if (sensorMap.automations
                ?.firstWhereOrNull((a) => a.configuration.inputIdentifier == identifier)
                ?.configuration
                .type
                .isSet ??
            false) {
          from = ButtonChannelConfigurationType.automation;
        } else {
          from = ButtonChannelConfigurationType.unset;
        }

        final ButtonChannelConfigurationType to;
        if (buttonConfiguration.configuration?.buttonLogic == ButtonChannelConfigurationLogic.matterControlled) {
          to = ButtonChannelConfigurationType.matter;
        } else if (buttonConfiguration.configuration?.apiLogic.isSet ?? false) {
          to = ButtonChannelConfigurationType.automation;
        } else {
          to = ButtonChannelConfigurationType.unset;
        }

        // Get corresponding automation
        final automation = SubDeviceAutomation.updateOrCreateAutomation(
          buttonConfiguration: buttonConfiguration,
          sensorMap: sensorMap,
          deviceType: device.deviceInfo.deviceType,
        );
        if (automation == null) {
          throw Exception('Failed to get automation for channel ${buttonConfiguration.position.key}');
        }

        // Check if anything needs to be done
        if (from == to && to != ButtonChannelConfigurationType.automation) continue;

        // Handle cases
        if (from == ButtonChannelConfigurationType.unset && to == ButtonChannelConfigurationType.automation) {
          // Create automation
          await _createAutomation(automation, sensorMap: sensorMap, position: buttonConfiguration.position);
        }
        if (from == ButtonChannelConfigurationType.unset && to == ButtonChannelConfigurationType.matter) {
          // Create matter sub sensor
          await _createOrUpdateMatterConfig(sensorMap: sensorMap, identifier: identifier);
        }
        if (from == ButtonChannelConfigurationType.automation && to == ButtonChannelConfigurationType.unset) {
          // Delete automation
          await _deleteAutomation(automation, sensorMap: sensorMap, position: buttonConfiguration.position);
        }
        if (from == ButtonChannelConfigurationType.automation && to == ButtonChannelConfigurationType.automation) {
          // Update automation
          await _updateAutomation(automation, sensorMap: sensorMap, position: buttonConfiguration.position);
        }
        if (from == ButtonChannelConfigurationType.automation && to == ButtonChannelConfigurationType.matter) {
          // Delete automation
          await _deleteAutomation(automation, sensorMap: sensorMap, position: buttonConfiguration.position);

          // Create matter sub sensor
          await _createOrUpdateMatterConfig(sensorMap: sensorMap, identifier: identifier);
        }
        if (from == ButtonChannelConfigurationType.matter && to == ButtonChannelConfigurationType.unset) {
          // Delete matter sub sensor
          await _checkAndDisableMatterConfig(sensorMap, identifier);
        }
        if (from == ButtonChannelConfigurationType.matter && to == ButtonChannelConfigurationType.automation) {
          // Delete matter sub sensor
          await _checkAndDisableMatterConfig(sensorMap, identifier);

          // Create automation
          await _createAutomation(automation, sensorMap: sensorMap, position: buttonConfiguration.position);
        }
      }

      // Return success
      return const VoidResult.success(message: 'Sensor updated');
    } catch (e) {
      final message = 'Could not set sensor:\n$e';
      warning('${device.tag} $message');
      return VoidResult.error(message: message, error: LocalizedError.deviceError);
    }
  }

  @override
  Future<VoidResult> startScanning({Duration? duration}) async {
    try {
      // Check if in demo mode
      if (demoMode) return _demoFactory.startScanning();

      // Get duration and create body
      final seconds = (duration ?? ISensorService.defaultScanDuration).inSeconds;
      final body = jsonEncode({'duration': seconds});

      // Start scanning
      final result = await iWifiManager.sendRawRequest(scanUrl, method: HttpMethod.post, device: device, body: body);
      if (!result.success) throw Exception(result.message);
      info('${device.tag} Scanning for EnOcean sensors started for $seconds second(s)...');

      // Return success
      return const VoidResult.success(message: 'Scanning started');
    } catch (e) {
      final message = 'Failed to start scanning:\n$e';
      warning('${device.tag} $message');
      return VoidResult.error(message: message, error: LocalizedError.deviceError);
    }
  }

  @override
  Future<DataResult<List<EnOceanScanResult>>> getScanResults({
    List<String> productIds = const [],
    bool includePairedSensors = false,
    bool includeUnknownSensors = false,
  }) async {
    try {
      // Check if in demo mode
      if (demoMode) {
        return _demoFactory.getScanResults(
          productIds: productIds,
          includePairedSensors: includePairedSensors,
          includeUnknownSensors: includeUnknownSensors,
        );
      }

      // Get scan results
      final result = await iWifiManager.sendRawRequest(scanUrl, device: device);
      final response = result.data?.text;
      if (response == null) throw Exception(result.message);

      // Decode json
      final root = jsonDecode(response) as List<dynamic>;
      final scanResults = <EnOceanScanResult>[];
      for (final result in root) {
        // Get scan result
        final scanResult = EnOceanScanResult.fromResponseJson(jsonEncode(result));
        if (scanResult == null) continue;

        // Check if scan result is needed
        if (!includePairedSensors && scanResult.isPaired) continue;
        if (!includeUnknownSensors && scanResult.potentialProducts.isEmpty) continue;

        // Check if product IDs are provided
        if (productIds.isEmpty) {
          scanResults.add(scanResult);
          continue;
        }

        // Check if product ID is in list
        bool idFound = false;
        for (final productId in productIds) {
          if (scanResult.potentialProducts.any((p) => p.id.trim().toLowerCase() == productId.trim().toLowerCase())) {
            idFound = true;
            break;
          }
        }

        // Add scan result if ID is found
        if (idFound) scanResults.add(scanResult);
      }

      // Return success
      return DataResult.success(data: scanResults.sorted((a, b) => a.signalsReceived > b.signalsReceived ? -1 : 1));
    } catch (e) {
      final message = 'Could not get scan results:\n$e';
      warning('${device.tag} $message');
      return DataResult.error(message: message, error: LocalizedError.deviceError);
    }
  }

  @override
  Future<DataResult<ISensor>> pairSensor(IScanResult scanResult, {required String name}) async {
    try {
      // Get EUR ID and product ID
      final int? eurid;
      final String? productId;
      switch (scanResult) {
        case EnOceanScanResult _:
          eurid = scanResult.eurId;
          productId = scanResult.selectedProduct?.id;
        case EnOceanCodeResult _:
          eurid = scanResult.eurId;
          productId = scanResult.productId;
        default:
          return DataResult.error(
            message: 'Scan result type not supported (${scanResult.runtimeType})',
            error: LocalizedError.invalidParameter,
          );
      }
      if (eurid == null) {
        return const DataResult.error(message: 'EURID is null', error: LocalizedError.invalidParameter);
      }
      if (productId == null) {
        return const DataResult.error(
          message: 'No product selected in scan result',
          error: LocalizedError.invalidParameter,
        );
      }

      // Check if in demo mode
      if (demoMode) return _demoFactory.pairSensor(eurid: eurid, productId: productId, name: name);

      // Check if sensor is already paired
      final knownSensor = await _getSensorWithEurId(eurid);
      if (knownSensor != null) {
        return const DataResult.error(message: 'Sensor already paired', error: LocalizedError.pairingError);
      }

      // Create EnOcean device
      final body = EnOceanPairingBody(eurId: eurid, productId: productId).toRequestJson();
      final creationResult = await iWifiManager.sendRawRequest(
        sensorsUrl,
        method: HttpMethod.post,
        device: device,
        body: body,
      );
      if (!creationResult.success) throw Exception(creationResult.message);

      // Get sensors
      final sensorResult = await getData();
      final sensorData = sensorResult.data;
      if (sensorData == null) throw Exception(sensorResult.message);

      // Find sensor
      final sensor = sensorData.sensors.firstWhereOrNull((s) => s.tryCast<EnOceanButton>()?.eurid == eurid);
      if (sensor == null) throw Exception('Sensor paired, but not found in sub devices');

      // Get sensor map
      final sensorMap = _getSensorMap(sensor);
      if (sensorMap == null) throw Exception('Failed to get sensor map for sensor ${sensor.id}');

      // Save sensor name
      sensor.name = name.trim();
      sensorMap.subDevice.name = name.trim();
      await _updateSubDevice(sensorMap.subDevice);

      // Return sensor
      return DataResult.success(data: sensor);
    } catch (e) {
      final message = 'Could not pair sensor:\n$e';
      warning('${device.tag} $message');
      return DataResult.error(message: message, error: LocalizedError.deviceError);
    }
  }

  @override
  Future<VoidResult> unpairSensor({required ISensor sensor}) async {
    try {
      // Check if in demo mode
      if (demoMode) return _demoFactory.unpairSensor(sensor: sensor);

      // Get sensor map
      final sensorMap = _getSensorMap(sensor);
      if (sensorMap == null) throw Exception('Failed to get sensor map for sensor ${sensor.id}');

      // Get IDs
      final deviceId = sensorMap.subDevice.id;
      if (deviceId.isEmpty) throw Exception('Device ID is empty');

      // Get automations
      final automations = await _getAutomations(forceRefresh: true, prioritized: true);

      // Delete sub sensor, sub device and automations
      final enOceanId = sensorMap.enOceanSubSensorID;
      final matterSubSensor = sensorMap.matterSubSensor;
      if (matterSubSensor != null) await _deleteMatterSubSensor(matterSubSensor);
      for (final automation in automations) {
        if (automation.configuration.inputId != deviceId) continue;
        await _deleteAutomation(automation, sensorMap: sensorMap);
      }
      if (enOceanId != null && enOceanId.isNotEmpty) await _deleteSubSensor(enOceanId);
      await _deleteSubDevice(sensorMap.subDevice);

      // Return success
      notifyListeners();
      return const VoidResult.success(message: 'Sensor unpaired and deleted');
    } catch (e) {
      final message = 'Could not unpair sensor:\n$e';
      warning('${device.tag} $message');
      return VoidResult.error(message: message, error: LocalizedError.deviceError);
    }
  }

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Get sensor map for [sensor]
  SubDeviceSubSensorMap? _getSensorMap(ISensor sensor) => _sensorMaps.getOrNull<SubDeviceSubSensorMap>(sensor.id);

  /// Get sub devices
  Future<List<SubDevice>> _getSubDevices({required bool forceRefresh, required bool prioritized}) async {
    // Get service
    final subDeviceService = device.services.firstOfTypeOrNull<ISubDeviceService>();
    if (subDeviceService == null) throw Exception('Failed to get sub device service');

    // Get data
    final subDevicesResult = await subDeviceService.getData(forceRefresh: forceRefresh, prioritized: prioritized);
    final subDevices = subDevicesResult.data?.subDevices;
    if (subDevices == null) throw Exception(subDevicesResult.message);

    // Return sub devices
    return subDevices;
  }

  /// Get sub sensors
  Future<List<ISubSensor>> _getSubSensors({required bool forceRefresh, required bool prioritized}) async {
    // Check if EnOcean is available
    if (!supportsEnOcean) {
      debug('${device.tag} No EnOcean backpack found');
      return [];
    }

    // Get service
    final subSensorService = device.services.firstOfTypeOrNull<ISubSensorService>();
    if (subSensorService == null) throw Exception('Failed to get sub sensor service');

    // Get data
    final subSensorsResult = await subSensorService.getData(forceRefresh: forceRefresh, prioritized: prioritized);
    final subSensors = subSensorsResult.data?.sensors;
    if (subSensors == null) throw Exception(subSensorsResult.message);

    // Return sub sensors
    return subSensors;
  }

  /// Get automations
  Future<List<SubDeviceAutomation>> _getAutomations({required bool forceRefresh, required bool prioritized}) async {
    // Get service
    final automationService = device.services.firstOfTypeOrNull<IAutomationService>();
    if (automationService == null) throw Exception('Failed to get automation service');

    // Get data
    final automationsResult = await automationService.getData(forceRefresh: forceRefresh, prioritized: prioritized);
    final automations = automationsResult.data?.automations;
    if (automations == null) throw Exception(automationsResult.message);

    // Return automations
    return automations;
  }

  /// Get matter sub sensors
  Future<List<MatterSubSensor>> _getMatterSensors({required bool forceRefresh, required bool prioritized}) async {
    // Get service
    final matterService = device.services.firstOfTypeOrNull<IMatterSubSensorService>();
    if (matterService == null) throw Exception('Failed to get matter sub sensor service');

    // Get data
    final matterResult = await matterService.getData(forceRefresh: forceRefresh, prioritized: prioritized);
    final sensors = matterResult.data?.sensors;
    if (sensors == null) throw Exception(matterResult.message);

    // Return sensors
    return sensors;
  }

  /// Set sensor orientation
  Future<void> _setSensorOrientation(ISensor sensor, {required bool prioritized}) async {
    // Check if orientation is supported by the sensor
    final enOceanButton = sensor.tryCast<EnOceanButton>();
    final orientation = enOceanButton?.orientation;
    if (orientation == null || orientation == SensorOrientation.unknown) return;

    // Get sensor map
    final sensorMap = _getSensorMap(sensor);
    if (sensorMap == null) throw Exception('Failed to get sensor map for sensor ${sensor.id}');

    // Update sensor
    final orientationBody = EnOceanSubSensorConfig(orientation: orientation).toRequestJson();
    final orientationResult = await iWifiManager.sendRawRequest(
      '$sensorsUrl/${sensorMap.enOceanSubSensorID}/config',
      method: HttpMethod.put,
      device: device,
      body: orientationBody,
    );
    if (!orientationResult.success) throw Exception('Failed to set orientation: ${orientationResult.message}');
  }

  /// Update [subDevice]
  Future<void> _updateSubDevice(SubDevice subDevice) async {
    // Get service
    final subDeviceService = device.services.firstOfTypeOrNull<ISubDeviceService>();
    if (subDeviceService == null) throw Exception('Failed to get sub device service');

    // Update sub device
    final subDeviceResult = await subDeviceService.setSubDevice(subDevice);
    if (!subDeviceResult.success) throw Exception('Failed to update sub device: ${subDeviceResult.message}');
  }

  /// Delete [subDevice]
  Future<void> _deleteSubDevice(SubDevice subDevice) async {
    // Get service
    final subDeviceService = device.services.firstOfTypeOrNull<ISubDeviceService>();
    if (subDeviceService == null) throw Exception('Failed to get sub device service');

    // Delete sub device
    final subDeviceResult = await subDeviceService.deleteSubDevice(subDevice.id);
    if (!subDeviceResult.success) throw Exception('Failed to delete sub device: ${subDeviceResult.message}');
  }

  /// Delete sub sensor with given [id]
  Future<void> _deleteSubSensor(String id) async {
    // Create URL
    final url = '$sensorsUrl/$id';

    // Make request
    final result = await iWifiManager.sendRawRequest(url, method: HttpMethod.delete, device: device);
    if (!result.success) throw Exception(result.message);
  }

  /// Create new [automation] (and log if [position] is given)
  Future<void> _createAutomation(
    SubDeviceAutomation automation, {
    required SubDeviceSubSensorMap sensorMap,
    SensorChannelPosition? position,
  }) async {
    // Get service
    final automationService = device.services.firstOfTypeOrNull<IAutomationService>();
    if (automationService == null) throw Exception('Failed to get automation service');

    // Create automation
    final automationResult = await automationService.createAutomation(automation);
    if (!automationResult.success) throw Exception('Failed to create automation: ${automationResult.message}');
    if (position != null) {
      debug('${device.tag} + ${position.name}: Created automation (${automation.configuration.type.key})');
    }

    // Update sensor map
    sensorMap.automations ??= [];
    sensorMap.automations?.add(automation);
  }

  /// Update existing [automation] (and log if [position] is given)
  Future<void> _updateAutomation(
    SubDeviceAutomation automation, {
    required SubDeviceSubSensorMap sensorMap,
    SensorChannelPosition? position,
  }) async {
    // Check automation
    if (automation.configuration.type == SubDeviceConfigurationLogic.unset) {
      return _deleteAutomation(automation, sensorMap: sensorMap, position: position);
    }

    // Get service
    final automationService = device.services.firstOfTypeOrNull<IAutomationService>();
    if (automationService == null) throw Exception('Failed to get automation service');

    // Update automation
    final automationResult = await automationService.updateAutomation(automation);
    if (!automationResult.success) throw Exception('Failed to update automation: ${automationResult.message}');
    if (position != null) {
      debug('${device.tag} * ${position.name}: Updated automation (${automation.configuration.type.key})');
    }

    // Update sensor map
    sensorMap.automations?.firstWhereOrNull((a) => a.configuration.id == automation.configuration.id)
      ?..isEnabled = automation.isEnabled
      ..name = automation.name
      ..configuration = automation.configuration;
  }

  /// Delete existing [automation] (and log if [position] is given)
  Future<void> _deleteAutomation(
    SubDeviceAutomation automation, {
    required SubDeviceSubSensorMap sensorMap,
    SensorChannelPosition? position,
  }) async {
    // Get service
    final automationService = device.services.firstOfTypeOrNull<IAutomationService>();
    if (automationService == null) throw Exception('Failed to get automation service');

    // Delete automation
    final automationResult = await automationService.deleteAutomation(automation.configuration.id);
    if (!automationResult.success) throw Exception('Failed to delete automation: ${automationResult.message}');
    if (position != null) debug('${device.tag} - ${position.name}: Deleted automation');

    // Update sensor map
    sensorMap.automations?.removeWhere((a) => a.configuration.id == automation.configuration.id);
  }

  /// Create new matter sub [sensor]
  Future<void> _createMatterSubSensor(MatterSubSensor sensor) async {
    // Get service
    final matterService = device.services.firstOfTypeOrNull<IMatterSubSensorService>();
    if (matterService == null) throw Exception('Failed to get matter sub sensor service');

    // Create sensor
    final matterResult = await matterService.createSensor(sensor);
    if (!matterResult.success) throw Exception('Failed to create matter sub sensor: ${matterResult.message}');
    debug('Created matter sub sensor (ID: ${sensor.serviceDeviceId})');
  }

  /// Update existing matter sub [sensor]
  Future<void> _updateMatterSubSensor(MatterSubSensor sensor) async {
    // Get service
    final matterService = device.services.firstOfTypeOrNull<IMatterSubSensorService>();
    if (matterService == null) throw Exception('Failed to get matter sub sensor service');

    // Update sensor
    final matterResult = await matterService.updateSensor(sensor);
    if (!matterResult.success) throw Exception('Failed to update matter sub sensor: ${matterResult.message}');
    debug('Updated matter sub sensor (Endpoint-ID: ${sensor.endpointId})');
  }

  /// Delete existing matter sub [sensor]
  Future<void> _deleteMatterSubSensor(MatterSubSensor sensor) async {
    // Get service
    final matterService = device.services.firstOfTypeOrNull<IMatterSubSensorService>();
    if (matterService == null) throw Exception('Failed to get matter sub sensor service');

    // Delete sensor
    final matterResult = await matterService.deleteSensor(sensor.serviceDeviceId);
    if (!matterResult.success) throw Exception('Failed to delete matter sub sensor: ${matterResult.message}');
  }

  /// Check if sensor with given [eurId] is already paired
  Future<ISensor?> _getSensorWithEurId(int eurId) async {
    // Get sensor data
    final sensorData = await getData();
    final sensors = sensorData.data?.sensors;
    if (sensors == null) throw Exception(sensorData.message);

    // Check if sensor is already paired
    return sensors.firstWhereOrNull((s) => s.tryCast<EnOceanButton>()?.eurid == eurId);
  }

  /// Get EnOcean state
  Future<EnOceanState> _getEnOceanState() async {
    final service = device.services.firstOfTypeOrNull<IEnOceanConfigService>() ?? WifiEnOceanConfigService(device);
    return (await service.getEnOceanState()).data ?? EnOceanState.unavailable;
  }

  /// Check and delete matter configuration for given [identifier]
  Future<void> _checkAndDisableMatterConfig(
    SubDeviceSubSensorMap sensorMap,
    ProductCharacteristicIdentifier identifier,
  ) async {
    // Check if matter sub sensor is configured
    final matterSubSensor = sensorMap.matterSubSensor;
    if (matterSubSensor == null) return;

    // Check if channel is configured
    final channelIndex = matterSubSensor.config.channels.indexWhere((c) => c.identifier == identifier.eltakoKey);
    if (channelIndex < 0) return;

    // Disable and update channel
    matterSubSensor.config.channels[channelIndex].isEnabled = false;
    await _updateMatterSubSensor(matterSubSensor);
  }

  Future<void> _createOrUpdateMatterConfig({
    required SubDeviceSubSensorMap sensorMap,
    required ProductCharacteristicIdentifier identifier,
  }) async {
    // Create or update matter sub sensor
    final matterSubSensor = sensorMap.matterSubSensor;
    if (matterSubSensor == null) {
      // Create new matter sub sensor
      final newMatterSensor = MatterSubSensor(
        id: sensorMap.subDevice.id,
        productId: sensorMap.subDevice.deviceInfo.id,
        serviceDeviceId: '',
        config: MatterSubSensorConfig(
          channels: [
            MatterSubSensorChannelFeatureConfig(
              identifier: identifier.eltakoKey,
              feature: MatterSubSensorChannelFeature.momentarySwitch,
            ),
          ],
        ),
      );
      await _createMatterSubSensor(newMatterSensor);
      sensorMap.matterSubSensor = newMatterSensor;
    } else {
      // Check if channel already configured
      final channel = matterSubSensor.config.channels.firstWhereOrNull((c) => c.identifier == identifier.eltakoKey);
      if (channel != null) {
        // Enable channel
        channel.isEnabled = true;
      } else {
        // Add channel
        matterSubSensor.config.channels.add(
          MatterSubSensorChannelFeatureConfig(
            identifier: identifier.eltakoKey,
            feature: MatterSubSensorChannelFeature.momentarySwitch,
          ),
        );
      }

      // Update matter sub sensor
      await _updateMatterSubSensor(matterSubSensor);
    }
  }

  // endregion
}
