//
//  br64ip_sensor_service_demo_factory.dart
//  EltakoConnect
//
//  Created by <PERSON> on 23.08.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:math';

import 'package:eltako_connect/enumeration/button_channel_configuration_function.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/button_type.dart';
import 'package:eltako_connect/enumeration/enocean_state.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_orientation.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/extension/bool_extension.dart';
import 'package:eltako_connect/extension/int_extension.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/manager/wifi/i_wifi_demo_factory.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_button_sensor.dart';
import 'package:eltako_connect/model/sensor/i_enocean_sensor.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/sensor/button_config.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor_config_data.dart';
import 'package:eltako_connect/model/sensor/sensor_connection.dart';
import 'package:eltako_connect/model/sub_device/sub_device/shader_config_data.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/sensor_service/br64ip_sensor_service.dart';
import 'package:eltako_connect/service/sensor_service/sensor_data.dart';
import 'package:eltako_connect/storage/json/br62_product_database.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:get_it/get_it.dart';

/// BR64IP sensor service demo factory
class Br64ipSensorServiceDemoFactory extends IWifiDemoFactory<Br64ipSensorService, SensorConfigData> {
  // region [CONSTANTS]

  /// Default scan duration
  static const _defaultScanDuration = Duration(seconds: 15);

  /// 1 way button product info
  static final _button1Way = GetIt.I.get<BR62ProductDatabase>().getSubDeviceInfo(SensorType.button1WayProductId);

  /// 2 way button product info
  static final _button2Way = GetIt.I.get<BR62ProductDatabase>().getSubDeviceInfo(SensorType.button2WayProductId);

  /// 4 way button product info
  static final _button4Way = GetIt.I.get<BR62ProductDatabase>().getSubDeviceInfo(SensorType.button4WayProductId);

  /// Product IDs for EnOcean buttons
  static final List<String> _enOceanButtonProductIds = [
    SensorType.button1WayProductId,
    SensorType.button2WayProductId,
    SensorType.button4WayProductId,
  ];

  // endregion

  // region [VARIABLES]

  /// Shader data
  ShaderConfigData? get _shaderDemoData => wifiDemoConnection.config.serviceData.firstOfTypeOrNull<ShaderConfigData>();

  /// Unpaired scan results
  List<EnOceanScanResult> _unpairedResults;

  /// Paired scan results
  List<EnOceanScanResult> _pairedResults;

  /// Demo scan active
  bool _isScanning;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  Br64ipSensorServiceDemoFactory(super.device) : _unpairedResults = [], _pairedResults = [], _isScanning = false;

  /// Get available sensor connections
  Future<DataResult<List<SensorConnection>>> getAvailableConnections() async {
    // Simulate delay
    await simulateDelay();

    // Get data
    final data = demoData;
    if (data == null) {
      const message = 'Could not get service data from wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return const DataResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Return available connections
    return DataResult.success(
      data: [
        SensorConnection.wired(),
        if (data.supportsEnOcean) SensorConnection.enOcean(state: EnOceanState.ok),
        SensorConnection.matter(),
      ],
    );
  }

  /// Get tilt feature state
  Future<DataResult<bool>> isTiltFeatureEnabled() async {
    // Simulate delay
    await simulateDelay();

    try {
      // Get shader data
      final data = _shaderDemoData;
      if (data == null) throw Exception('Could not get shader service data from wifi demo device config');

      // Return data
      return DataResult.success(data: data.tiltMode.isEnabled);
    } catch (ex) {
      error('${device.tag} $ex', category: LogCategory.device);
      return DataResult.error(message: ex.toString(), error: LocalizedError.deviceError);
    }
  }

  /// Get service data
  Future<DataResult<SensorData>> getData() async {
    // Simulate delay
    await simulateDelay();

    // Get data
    final data = demoData;
    if (data == null) {
      const message = 'Could not get service data from wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return const DataResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Randomly update signal strength and last seen
    _updateSensors(data);

    // Return data
    return DataResult.success(data: data.toSensorData());
  }

  /// Update [sensor]
  Future<VoidResult> setSensor(ISensor sensor) async {
    // Simulate delay
    await simulateDelay();

    // Find sensor
    final demoSensor = demoData?.pairedSensors.firstWhereOrNull((s) => s.id == sensor.id);
    if (demoSensor == null) {
      final message = 'Could not find sensor ${sensor.name} in wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return DataResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Update sensor
    demoSensor.name = sensor.name;
    if (demoSensor is IButtonSensor && sensor is IButtonSensor) {
      // Update button type
      demoSensor.buttonType = sensor.buttonType;

      // Update channels
      for (final channel in demoSensor.channels) {
        // Find corresponding channel
        final sensorChannel = sensor.channels.firstWhereOrNull((c) => c.position == channel.position);
        if (sensorChannel == null) {
          final message = 'Could not find channel ${channel.position} in wifi demo device config';
          warning('${device.tag} $message', category: LogCategory.device);
          continue;
        }

        // Update channel
        channel
          ..configurationLogic = sensorChannel.configurationLogic
          ..function = sensorChannel.function
          ..fallbackDelay = sensorChannel.fallbackDelay
          ..value = sensorChannel.value;
      }
    }

    // Return success
    return const VoidResult.success();
  }

  /// Simulate scanning for sensors
  Future<VoidResult> startScanning({Duration? duration}) async {
    // Check if already scanning
    if (_isScanning) return const VoidResult.success(message: 'Scanning already started');

    // Get data
    final data = demoData;
    if (data == null) {
      const message = 'Could not get service data from wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return const DataResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Simulate delay
    await simulateDelay();

    // Create scan results
    _pairedResults =
        data.pairedSensors
            .map((s) => s.tryCast<IEnOceanSensor>())
            .nonNulls
            .map((s) => _sensorToScanResult(s, isPaired: true))
            .toList();
    _unpairedResults =
        data.unpairedSensors
            .map((s) => s.tryCast<IEnOceanSensor>())
            .nonNulls
            .map((s) => _sensorToScanResult(s, signalsReceived: 1 + Random().nextInt(data.maxAddedSignalsReceived)))
            .toList();

    // Start scanning
    _isScanning = true;
    debug('Scanning started');

    // Stop scanning after duration
    Future.delayed(duration ?? _defaultScanDuration, _stopScanning);

    // Return success
    return const VoidResult.success(message: 'Scanning started');
  }

  /// Simulate scan results of last scan (optionally [includePairedSensors] and [includeUnknownSensors])
  Future<DataResult<List<EnOceanScanResult>>> getScanResults({
    List<String> productIds = const [],
    bool includePairedSensors = false,
    bool includeUnknownSensors = false,
  }) async {
    // Get data
    final data = demoData;
    if (data == null) {
      const message = 'Could not get service data from wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return const DataResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Simulate delay
    await simulateDelay();

    // Randomly update scan results
    if (_isScanning) {
      for (final result in _pairedResults) {
        _updateSensorScanResult(result, data: data, updateSignalsReceived: false);
      }
      for (final result in _unpairedResults) {
        _updateSensorScanResult(result, data: data);
      }
    }

    // Create and return data
    final List<EnOceanScanResult> results =
        _filteredScanResults(_unpairedResults, productIds: productIds)
          ..addAll(includePairedSensors ? _filteredScanResults(_pairedResults, productIds: productIds) : [])
          ..sort((a, b) => b.signalsReceived.compareTo(a.signalsReceived));
    return DataResult.success(data: results);
  }

  /// Pair sensor
  Future<DataResult<ISensor>> pairSensor({required int eurid, required String productId, required String name}) async {
    // Simulate delay
    await simulateDelay();

    // Get data
    final data = demoData;
    if (data == null) {
      const message = 'Could not get service data from wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return const DataResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Find or create sensor
    IEnOceanSensor? demoSensor = data.unpairedSensors.firstWhereOrNull((s) => s.eurid == eurid);
    if (demoSensor == null) {
      // Check for EnOcean button
      if (_enOceanButtonProductIds.contains(productId)) {
        // Create unset function
        ButtonConfig unset(SensorChannelPosition position) =>
            ButtonConfig.unset(position: position, category: device.deviceInfo.deviceType.category);

        // Get button type and channels
        final ButtonType buttonType;
        final List<ButtonConfig> channels;
        switch (productId) {
          case SensorType.button1WayProductId:
            buttonType = ButtonType.button1Way;
            channels = [unset(SensorChannelPosition.single)];
          case SensorType.button2WayProductId:
            buttonType = ButtonType.button2Way;
            channels = [unset(SensorChannelPosition.top), unset(SensorChannelPosition.bottom)];
          case SensorType.button4WayProductId:
            buttonType = ButtonType.button4Way;
            channels = [
              unset(SensorChannelPosition.topLeft),
              unset(SensorChannelPosition.bottomLeft),
              unset(SensorChannelPosition.topRight),
              unset(SensorChannelPosition.bottomRight),
            ];
          default:
            final message = 'Unknown product ID: $productId';
            error('${device.tag} $message', category: LogCategory.device);
            return DataResult.error(message: message, error: LocalizedError.deviceError);
        }

        // Create EnOcean button
        demoSensor = EnOceanButton(
          buttonType: buttonType,
          productId: productId,
          id: eurid.byteString,
          name: name,
          channelCount: buttonType == ButtonType.button1Way ? 1 : 4,
          channels: channels,
          eurid: eurid,
          lastSeen: DateTime.now(),
          signalStrength: (-1 * Random().nextInt(20)) - 30,
        );
      }

      // Check if sensor was created
      if (demoSensor == null) {
        final message = 'Could not pair sensor with EUR ID $eurid (Unknown product ID: $productId)';
        error('${device.tag} $message', category: LogCategory.device);
        return DataResult.error(message: message, error: LocalizedError.deviceError);
      }
    } else {
      // Remove sensor from unpaired
      data.unpairedSensors.remove(demoSensor);
    }

    // Update sensor name and add to paired
    demoSensor.name = name;
    data.pairedSensors.add(demoSensor);

    // Return success
    return DataResult.success(data: demoSensor);
  }

  /// Unpair [sensor]
  Future<VoidResult> unpairSensor({required ISensor sensor}) async {
    // Simulate delay
    await simulateDelay();

    // Find sensor
    final demoSensor = demoData?.pairedSensors.firstWhereOrNull((s) => s.id == sensor.id);
    if (demoSensor == null) {
      final message = 'Could not find sensor ${sensor.name} in wifi demo device config';
      error('${device.tag} $message', category: LogCategory.device);
      return VoidResult.error(message: message, error: LocalizedError.deviceError);
    }

    // Reset sensor and move to unpaired
    if (demoSensor is IEnOceanSensor) {
      if (demoSensor is EnOceanButton) {
        demoSensor.orientation = SensorOrientation.clockwise0;
        for (final channel in demoSensor.channels) {
          channel
            ..configurationLogic = ButtonChannelConfigurationLogic.unset
            ..function = ButtonChannelConfigurationFunction.none
            ..fallbackDelay = Duration.zero
            ..value = null;
        }
      }
      demoData
        ?..pairedSensors.remove(demoSensor)
        ..unpairedSensors.add(demoSensor);
    }

    // Return success
    return const VoidResult.success();
  }

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Calculate next random signal strength
  int _calculateSignalStrength(int minSignalStrength, int maxSignalStrength) =>
      min(minSignalStrength, maxSignalStrength) + Random().nextInt((maxSignalStrength - minSignalStrength).abs());

  /// Filter scan results by [productIds] (empty list includes all)
  List<EnOceanScanResult> _filteredScanResults(List<EnOceanScanResult> results, {required List<String> productIds}) =>
      results
          .where(
            (s) =>
                productIds.isEmpty ||
                productIds.any((id) => s.potentialProducts.any((pp) => pp.id.trimUp == id.trimUp)),
          )
          .nonNulls
          .toList();

  /// Convert [sensor] to scan result
  EnOceanScanResult _sensorToScanResult(IEnOceanSensor sensor, {bool isPaired = false, int signalsReceived = 0}) =>
      EnOceanScanResult(
        id: sensor.euridHex,
        signalStrength: (-1 * Random().nextInt(50)) - 30,
        lastSeen: sensor.lastSeen ?? DateTime.now(),
        isPaired: isPaired,
        signalsReceived: signalsReceived,
        potentialProducts:
            sensor.channelCount == 1 ? [_button1Way].nonNulls.toList() : [_button2Way, _button4Way].nonNulls.toList(),
      );

  /// Stop scanning
  void _stopScanning() {
    _isScanning = false;
    debug('Scanning stopped');
  }

  /// Update sensor demo data
  void _updateSensors(SensorConfigData data) {
    for (final sensor in data.pairedSensors) {
      // Check if sensor is EnOcean sensor
      final enOceanSensor = sensor.tryCast<IEnOceanSensor>();
      if (enOceanSensor == null) continue;

      // Check if update needed
      if (!BoolExtension.random(probability: data.signalStrengthUpdateProbability)) continue;

      // Update sensor
      enOceanSensor
        ..signalStrength = _calculateSignalStrength(data.minSignalStrength, data.maxSignalStrength)
        ..lastSeen = DateTime.now();
    }
  }

  /// Update sensor scan result
  void _updateSensorScanResult(
    EnOceanScanResult result, {
    required SensorConfigData data,
    bool updateSignalsReceived = true,
  }) {
    // Update signals received
    if (updateSignalsReceived && BoolExtension.random(probability: data.signalsReceivedUpdateProbability)) {
      result.signalsReceived += Random().nextInt(data.maxAddedSignalsReceived);
    }

    // Update signal strength and last seen
    if (!BoolExtension.random(probability: data.signalStrengthUpdateProbability)) return;
    result
      ..signalStrength = _calculateSignalStrength(data.minSignalStrength, data.maxSignalStrength)
      ..lastSeen = DateTime.now();
  }

  // endregion
}
