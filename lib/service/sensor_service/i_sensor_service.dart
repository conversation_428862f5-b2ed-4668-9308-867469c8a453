//
//  i_sensor_service.dart
//  EltakoConnect
//
//  Created by <PERSON> on 13.06.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/enumeration/sensor_connection_type.dart';
import 'package:eltako_connect/enumeration/service_key.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/mixin/debug_output.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/pairing/i_scan_result.dart';
import 'package:eltako_connect/model/sensor/sensor_connection.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/i_service.dart';
import 'package:eltako_connect/service/sensor_service/sensor_data.dart';
import 'package:eltako_connect/service/sensor_service/sensor_service_debug_output.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_service_view.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_service_view_model.dart';
import 'package:flutter/material.dart';

/// Interface for sensor services
abstract class ISensorService extends IService<SensorData> with DebugOutput, SensorServiceDebugOutput {
  // region [CONSTANTS]

  /// Data key
  static const ServiceKey dataKey = ServiceKey.sensor;

  /// Default scan duration
  static const Duration defaultScanDuration = Duration(seconds: 15);

  // endregion

  // region [PROPERTIES]

  /// Supported sensor connection types
  List<SensorConnectionType> get supportedSensorConnectionTypes => _supportedSensorConnectionTypes;

  /// Supported sensor connection types
  set supportedSensorConnectionTypes(List<SensorConnectionType> value) {
    _supportedSensorConnectionTypes = value;
    notifyListeners();
  }

  // endregion

  // region [COMPUTED PROPERTIES]

  /// Device supports EnOcean sensors
  bool get supportsEnOcean => supportedSensorConnectionTypes.contains(SensorConnectionType.enOcean);

  /// Device supports matter sensors
  bool get supportsMatter => supportedSensorConnectionTypes.contains(SensorConnectionType.matter);

  /// Device supports tilt feature (ESB64 only)
  bool get supportsTiltFeature => device.deviceInfo.deviceType == DeviceType.esb64npipm;

  // endregion

  // region [VARIABLES]

  /// Supported sensor connection types
  List<SensorConnectionType> _supportedSensorConnectionTypes;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  ISensorService({
    required super.device,
    required super.serviceInfo,
    required List<SensorConnectionType> supportedSensorConnectionTypes,
  }) : _supportedSensorConnectionTypes = supportedSensorConnectionTypes,
       super(key: dataKey, icon: Assets.icons.lightSwitch.circle);

  @override
  Future<VoidResult> setData(SensorData data, {bool prioritized = true}) async => const VoidResult.error(
    message: 'setData() not supported! Use setSensor() instead',
    error: LocalizedError.notSupported,
  );

  @override
  Widget getWidget() => showDebugView ? debugView : SensorServiceView(viewModel: SensorServiceViewModel(this));

  /// Get available sensor connections
  Future<DataResult<List<SensorConnection>>> getAvailableConnections();

  /// Check if tilt feature is supported and enabled
  Future<DataResult<bool>> isTiltFeatureEnabled();

  /// Start scanning for sensors (optionally for specific [duration])
  Future<VoidResult> startScanning({Duration? duration});

  /// Get scan results of last scan (optionally [includePairedSensors] and [includeUnknownSensors])
  Future<DataResult<List<EnOceanScanResult>>> getScanResults({bool includePairedSensors, bool includeUnknownSensors});

  /// Update [sensor]
  Future<VoidResult> setSensor(ISensor sensor, {bool prioritized = true});

  /// Pair sensor
  Future<DataResult<ISensor>> pairSensor(IScanResult scanResult, {required String name});

  /// Unpair [sensor]
  Future<VoidResult> unpairSensor({required ISensor sensor});

  // endregion

  // region [DEMO VIEW]

  // Future<void> _updateLeftSideOldVersion() async => _updateSensorOldVersion();
  //
  // Future<void> _updateRightSideOldVersion() async => _updateSensorOldVersion(leftSide: false);
  //
  // Future<void> _updateSensorOldVersion({bool leftSide = true}) async {
  //   debug('Setting data (${leftSide ? 'left' : 'right'} side) for last paired sensor with OLD VERSION...');
  //
  //   // Get last paired sensor
  //   final dataResult = await getData();
  //   final data = dataResult.data;
  //   final sensor = data?.sensors.lastOrNull?.tryCast<IButtonSensor>();
  //   if (sensor == null) {
  //     warning('No sensor found');
  //     return;
  //   }
  //
  //   // Update sensor configuration
  //   if (sensor.channelCount == 1) {
  //     sensor
  //       ..name = 'F1T55 - ${leftSide ? 'impulse' : 'switch'}'
  //       ..setButtonConfiguration(
  //         ButtonChannelConfigurationLogic.universal,
  //         position: SensorChannelPosition.single,
  //         function: leftSide ? ButtonChannelConfigurationFunction.es : ButtonChannelConfigurationFunction.er,
  //       );
  //   } else if (sensor.channelCount == 2) {
  //     final isEnOcean = sensor.category.isEnOcean;
  //     sensor
  //       ..name = isEnOcean
  //           ? 'F2T55 - ${leftSide ? 'top' : 'bottom'} only'
  //           : 'Wired input - ${leftSide ? 'impulse' : 'switch'}'
  //       ..tryCast<EnOceanButton>()?.orientation = SensorOrientation.clockwise0;
  //
  //     if (leftSide) {
  //       sensor
  //         // ..setButtonConfiguration(
  //         //   ButtonChannelConfigurationLogic.universal,
  //         //   position: SensorChannelPosition.top,
  //         //   function: ButtonChannelConfigurationFunction.es,
  //         // )
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.on,
  //           position: SensorChannelPosition.top,
  //           function: ButtonChannelConfigurationFunction.esv,
  //           fallBackDelay: const Duration(seconds: 1),
  //         )
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.changeoverSwitch,
  //           position: SensorChannelPosition.left,
  //         )
  //         ..unsetButtonConfiguration(SensorChannelPosition.bottom);
  //     } else {
  //       sensor
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.universal,
  //           position: SensorChannelPosition.bottom,
  //           function: ButtonChannelConfigurationFunction.er,
  //         )
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.universal,
  //           position: SensorChannelPosition.left,
  //           function: ButtonChannelConfigurationFunction.er,
  //         )
  //         ..unsetButtonConfiguration(SensorChannelPosition.top);
  //     }
  //   } else {
  //     sensor
  //       ..name = 'F4T55 - ${leftSide ? 'left' : 'right'} side only'
  //       ..tryCast<EnOceanButton>()?.orientation = SensorOrientation.clockwise0;
  //     if (leftSide) {
  //       sensor
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.universal,
  //           position: SensorChannelPosition.topLeft,
  //           function: ButtonChannelConfigurationFunction.es,
  //         )
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.universal,
  //           position: SensorChannelPosition.bottomLeft,
  //           function: ButtonChannelConfigurationFunction.er,
  //         )
  //         ..unsetButtonConfiguration(SensorChannelPosition.bottomRight);
  //     } else {
  //       sensor
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.on,
  //           position: SensorChannelPosition.topRight,
  //           function: ButtonChannelConfigurationFunction.esv,
  //           fallBackDelay: const Duration(seconds: 10),
  //         )
  //         ..setButtonConfiguration(
  //           ButtonChannelConfigurationLogic.matterControlled,
  //           position: SensorChannelPosition.topLeft,
  //         )
  //         ..unsetButtonConfiguration(SensorChannelPosition.bottomLeft);
  //     }
  //   }
  //
  //   // Update sensor
  //   final updateResult = await setSensor(sensor);
  //   if (updateResult.success) {
  //     info(updateResult.message);
  //   } else {
  //     warning(updateResult.message);
  //   }
  // }

  // endregion
}
