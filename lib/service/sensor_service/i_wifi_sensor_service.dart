//
//  i_wifi_sensor_service.dart
//  EltakoConnect
//
//  Created by <PERSON> on 13.06.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/service/sensor_service/i_sensor_service.dart';
import 'package:eltako_connect/service/sensor_service/sensor_data.dart';
import 'package:eltako_connect/service/wifi_connector.dart';

/// Interface for wifi sensor services
abstract class IWifiSensorService extends ISensorService with WifiConnector<SensorData> {
  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  IWifiSensorService({
    required super.device,
    required super.serviceInfo,
    required super.supportedSensorConnectionTypes,
  });

  // endregion
}
