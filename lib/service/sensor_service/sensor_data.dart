//
//  sensor_data.dart
//  EltakoConnect
//
//  Created by <PERSON> on 13.06.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/service_key.dart';
import 'package:eltako_connect/extension/map_extension.dart';
import 'package:eltako_connect/mixin/config_output.dart';
import 'package:eltako_connect/mixin/extended_output.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/service/i_service_data.dart';

/// Class for storing sensor data
class SensorData extends IServiceData with ConfigOutput, ExtendedOutput {
  // region [CONSTANTS]

  /// Data key
  static const ServiceKey dataKey = ServiceKey.sensor;

  // endregion

  // region [PROPERTIES]

  /// List of sensors
  List<ISensor> sensors;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  SensorData({required this.sensors}) : super(key: dataKey);

  /// Create sensor data from [j<PERSON>] response
  static SensorData? fromResponseJson(String json) => null;

  /// Create sensor data from [map]
  static SensorData? fromMap(Map<String, dynamic> map) {
    // Get sensors
    final sensorMaps = map.get('sensors', fallback: []);
    final List<ISensor> sensors = [];
    for (final map in sensorMaps) {
      // Get sensor
      final sensor = ISensor.fromMap(map);
      if (sensor == null) return null;

      // Add channel
      sensors.add(sensor);
    }

    // Create and return data
    return SensorData(sensors: sensors);
  }

  @override
  Map<String, dynamic> toMap({bool forConfig = false}) => {
    'sensors': sensors.map((sensor) => forConfig ? sensor.toConfigMap() : sensor.toMap()).toList(),
  };

  @override
  Map<String, dynamic> toConfigMap() => toMap(forConfig: true);

  // endregion
}
