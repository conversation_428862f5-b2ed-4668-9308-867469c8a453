//
//  i_sub_device_service.dart
//  EltakoConnect
//
//  Created by <PERSON> on 27.02.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/enumeration/service_key.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/mixin/debug_output.dart';
import 'package:eltako_connect/model/sub_device/sub_device.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/i_service.dart';
import 'package:eltako_connect/service/sub_device_service/sub_device_data.dart';
import 'package:flutter/material.dart';

/// Interface for sub device services
abstract class ISubDeviceService extends IService<SubDeviceData> with DebugOutput {
  // region [CONSTANTS]

  /// Data key
  static const ServiceKey dataKey = ServiceKey.subDevice;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  ISubDeviceService({required super.device, required super.serviceInfo})
    : super(key: dataKey, icon: Assets.icons.devices.circle);

  @override
  Widget getWidget() => debugView;

  @override
  Future<VoidResult> setData(SubDeviceData data, {bool prioritized = true}) async => const VoidResult.error(
    message: 'setData() not supported! Use setSubDevice() instead',
    error: LocalizedError.notSupported,
  );

  /// Update [subDevice]
  Future<VoidResult> setSubDevice(SubDevice subDevice);

  /// Delete sub device with given [id]
  Future<VoidResult> deleteSubDevice(String id);

  // endregion
}
