//
//  wifi_connector.dart
//  EltakoConnect
//
//  Created by <PERSON> on 01.02.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/connection/wifi/i_wifi_connection.dart';
import 'package:eltako_connect/connection/wifi/wifi_demo_connection.dart';
import 'package:eltako_connect/device/i_wifi_device.dart';
import 'package:eltako_connect/enumeration/connection_type.dart';
import 'package:eltako_connect/enumeration/data_operation_type.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/manager/wifi/i_wifi_manager.dart';
import 'package:eltako_connect/manager/wifi/wifi_demo_manager.dart';
import 'package:eltako_connect/mixin/request_list_output.dart';
import 'package:eltako_connect/mixin/request_output.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/i_service.dart';
import 'package:eltako_connect/service/i_service_data.dart';
import 'package:eltako_connect/service/modbus_service/modbus_data.dart';
import 'package:eltako_connect/service/mqtt_service/mqtt_data.dart';
import 'package:eltako_connect/service/name_service/name_data.dart';
import 'package:eltako_connect/service/system_service/i_system_data.dart';
import 'package:eltako_connect/service/wifi_service_info.dart';
import 'package:eltako_log/eltako_log.dart';

/// Mixin with additional wifi functionality
mixin WifiConnector<T extends IServiceData> on IService<T> {
  // region [COMPUTED PROPERTIES]

  /// Manager as IWifiManager
  IWifiManager get iWifiManager => device.connectionManager as IWifiManager;

  /// Connection as IWifiConnection
  IWifiConnection get iWifiConnection => device.connection as IWifiConnection;

  /// Device as WifiDevice
  IWifiDevice get iWifiDevice => device as IWifiDevice;

  /// Service info as WifiServiceInfo
  WifiServiceInfo get wifiInfo => serviceInfo as WifiServiceInfo;

  // endregion

  // region [PUBLIC FUNCTIONS]

  @override
  Future<DataResult<T>> getData({bool forceRefresh = true, bool prioritized = true}) async {
    // Check if data is already loaded
    final dataResult = await checkIfDataReady(forceRefresh: forceRefresh);
    if (dataResult.success) return dataResult;

    // Check if demo device
    if (device.connection.type == ConnectionType.wifiDemo) return _getDemoData();

    // Create URL
    final url = wifiInfo.createEndpointURL(DataOperationType.get);

    // Read data from device
    trace('${device.tag} Requesting $T from device (endpoint: $url)');
    final result = await iWifiManager.sendRequest<T>(url, device, method: wifiInfo.methodGet);
    final data = result.data;
    if (data == null) return DataResult.error(message: result.message, error: result.error);
    this.data = data;

    // Update name
    if (data is NameData) device.deviceInfo.name = data.name;
    updateWidgetFromServiceData();
    return DataResult.success(message: 'Successfully fetched $T from device', data: data);
  }

  @override
  Future<VoidResult> setData(T data, {bool prioritized = true}) async {
    // Check if write is allowed
    if (!data.isWritable) {
      return VoidResult.error(
        message: 'Could not write $T, because service is not writable',
        error: LocalizedError.notWritable,
      );
    }

    // Check if demo device
    if (device.connection.type == ConnectionType.wifiDemo) return _setDemoData(data);

    // Get request params
    final url = wifiInfo.createEndpointURL(DataOperationType.set);

    // Write data to device
    trace('${device.tag} Updating $T on device (endpoint: $url)');
    final result =
        device.connection.type == ConnectionType.wifi
            ? await iWifiManager.sendRawRequest(
              url,
              device: device,
              method: wifiInfo.methodSet,
              body:
                  data.tryCast<RequestOutput>()?.toRequestJson() ?? data.tryCast<RequestListOutput>()?.toRequestJson(),
            )
            : await iWifiManager.sendRequest<T>(url, device, method: wifiInfo.methodSet, body: data);
    if (!result.success) return VoidResult.error(message: result.message, error: result.error);

    // Update data
    this.data = data;

    // Update name
    if (data is NameData) device.deviceInfo.name = data.name;

    // Return success
    final message = 'Successfully updated $T';
    if (T != MqttData && T != ModbusData) debug('${device.tag} $message');
    return VoidResult.success(message: message);
  }

  /// Check if data is already available
  Future<DataResult<T>> checkIfDataReady({bool forceRefresh = false}) async {
    // Check if readable
    if (!wifiInfo.isReadable) {
      final message = 'Service is not readable, using $T from service data';
      trace('${device.tag} $message');
      updateWidgetFromServiceData();
      return DataResult.success(message: message, data: data);
    }

    // Check if refresh is needed
    if (forceRefresh) {
      data = null;
      updateWidgetFromServiceData();
    }

    // Check if data already available
    if (data != null) {
      final message = 'Already fetched, reusing $T from service data';
      trace('${device.tag} $message');
      updateWidgetFromServiceData();
      return DataResult.success(message: message, data: data);
    }

    // Data not present
    return const DataResult.error(message: 'Data not fetched yet', error: LocalizedError.notFound);
  }

  // endregion

  // region [PRIVATE FUNCTIONS]

  /// Get demo data
  Future<DataResult<T>> _getDemoData() async {
    // Simulate delay
    await iWifiManager.tryCast<WifiDemoManager>()?.simulateDelay();

    // Get demo data
    final demoData = device.connection.tryCast<WifiDemoConnection>()?.config.serviceData.firstOfTypeOrNull<T>();
    if (demoData == null) {
      final message = 'No demo data available for $T';
      warning('${device.tag} $message');
      return DataResult.error(message: message, error: LocalizedError.notFound);
    }

    // Return demo data
    updateWidgetFromServiceData();
    return DataResult.success(message: 'Successfully fetched $T from demo data', data: demoData);
  }

  Future<VoidResult> _setDemoData(T data) async {
    // Simulate delay
    await iWifiManager.tryCast<WifiDemoManager>()?.simulateDelay();

    // Get service data
    final serviceData = device.connection.tryCast<WifiDemoConnection>()?.config.serviceData;
    if (serviceData == null) {
      final message = 'No demo data available for $T';
      warning('${device.tag} $message');
      return VoidResult.error(message: message, error: LocalizedError.notFound);
    }

    // Find index of demo data
    final index = serviceData.indexWhere((d) => d is T);
    if (index == -1) {
      final message = 'Setting $T is not supported for demo devices';
      warning('${device.tag} $message');
      return VoidResult.error(message: message, error: LocalizedError.notSupported);
    }

    // Replace demo data
    serviceData[index] = data;

    // Check for special data
    if (data is NameData) device.deviceInfo.name = data.name;
    if (data is ISystemData) device.deviceInfo.name = data.name;

    // Return success
    updateWidgetFromServiceData();
    return VoidResult.success(message: 'Successfully updated $T in demo data');
  }

  // endregion
}
