//
//  configuration_detail.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/manager/config_manager.dart';
import 'package:eltako_connect/model/config_info.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';

/// Configuration Detail
class ConfigurationDetail extends StatelessWidget {
  /// Configuration
  final ConfigInfo config;

  /// Title notifier
  final ValueNotifier<String> _titleNotifier = ValueNotifier(StringExtension.empty);

  /// Default constructor
  ConfigurationDetail({required this.config, super.key}) {
    _titleNotifier.value = config.name;
  }

  Future<void> _save(BuildContext context) async {
    if (_titleNotifier.value.trim().isEmpty) {
      EltakoSnackBar(
        title: AppLocalizations.of(context).configurationsNameFailEmpty,
        state: EltakoSnackBarState.error,
      ).show(context);
      return;
    }
    final configManager = GetIt.I.get<ConfigManager>();
    final result = await configManager.rename(config.fileId, name: _titleNotifier.value);
    if (!result.success && context.mounted) {
      EltakoSnackBar.result(result).show(context);
      return;
    }
    config.name = _titleNotifier.value;
    if (!context.mounted) return;
    context.pop(config);
  }

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      titleNotifier: _titleNotifier,
      title: config.name,
      trailing: NavigationSaveItem(onTap: () async => _save(context)),
    ),
    child: SliverToBoxAdapter(
      child: Column(
        children: [
          EltakoTextField(
            controller: TextEditingController(text: config.name),
            onChanged: (value) => _titleNotifier.value = value,
            showClearButton: true,
          ),
          const SizedBox(height: EltakoPadding.xl),
          EltakoButton(
            label: Text(AppLocalizations.of(context).configurationsDelete),
            icon: Assets.icons.bin.outline.light.image(color: EltakoStyle.of(context).error, height: 18),
            color: EltakoStyle.of(context).error,
            onPressed: () async {
              final shouldDelete = await showEltakoDialog(
                context: context,
                title: AppLocalizations.of(context).configurationsDelete,
                content: EltakoStyledText(
                  text: AppLocalizations.of(context).configurationsDeleteHint(config.name),
                  textStyle: Theme.of(context).textTheme.bodyMedium,
                  styledText: config.name,
                  styledTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                actions: [
                  EltakoButton(
                    label: Text(AppLocalizations.of(context).generalCancel),
                    onPressed: () => Navigator.of(context).pop(false),
                  ),
                  EltakoButton(
                    label: Text(
                      AppLocalizations.of(context).generalDelete,
                      style: const TextStyle(color: Colors.white),
                    ),
                    eltakoButtonStyle: EltakoButtonStyle.filled,
                    color: EltakoStyle.of(context).error,
                    onPressed: () {
                      Navigator.of(context).pop(true);
                      EltakoSnackBar(
                        title: AppLocalizations.of(context).configurationDeleted,
                        state: EltakoSnackBarState.success,
                      ).show(context);
                    },
                  ),
                ],
              );
              if (shouldDelete) {
                final configManager = GetIt.I.get<ConfigManager>();
                await configManager.delete(config.fileId);
                if (!context.mounted) return;
                context.pop(-1);
              }
            },
          ),
        ],
      ),
    ),
  );
}
