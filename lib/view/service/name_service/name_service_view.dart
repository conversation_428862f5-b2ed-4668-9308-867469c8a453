//
//  name_service_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 11.01.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/view/service/name_service/i_name_service_view_model.dart';
import 'package:eltako_connect/widget/service_loading/service_loader.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Name service view
class NameServiceView extends StatelessWidget {
  // region [PRIVATE PROPERTIES]

  final INameServiceViewModel _viewModel;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  const NameServiceView({required INameServiceViewModel viewModel, super.key}) : _viewModel = viewModel;

  // endregion

  // region [PRIVATE FUNCTIONS]

  Future<void> _save(BuildContext context) async {
    _viewModel.deviceName.value = _viewModel.deviceName.value.trim();
    if (_viewModel.deviceName.value.isEmpty) {
      EltakoSnackBar(
        title: AppLocalizations.of(context).settingsNameFailEmpty,
        state: EltakoSnackBarState.error,
      ).show(context);
      return;
    }
    if (_viewModel.maxNameLength != null &&
        _viewModel.deviceName.value.length > _viewModel.maxNameLength! &&
        _viewModel.maxNameLength! != 0) {
      EltakoSnackBar(
        title: AppLocalizations.of(context).settingsNameFailLength(_viewModel.maxNameLength!),
        state: EltakoSnackBarState.error,
      ).show(context);
      return;
    }
    final VoidResult result = await _viewModel.save();
    if (!context.mounted) return;
    EltakoSnackBar.result(
      result,
      message: result.success ? AppLocalizations.of(context).deviceNameChangedSuccessfully : result.message,
    ).show(context);
    if (!result.success) return;
    context.pop();
  }

  // endregion

  // region [OVERRIDES]

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      title: AppLocalizations.of(context).settingsNameHeader,
      trailing: NavigationSaveItem(onTap: () async => _save(context)),
    ),
    child: ServiceLoader(
      loadServiceData: _viewModel.loadService,
      child: SliverToBoxAdapter(
        child: ValueListenableBuilder(
          valueListenable: _viewModel.deviceName,
          builder:
              (context, deviceName, child) => EltakoTextField(
                characterLimit: _viewModel.maxNameLength,
                controller: TextEditingController(text: _viewModel.deviceName.value),
                hintText: AppLocalizations.of(context).deviceNameNew,
                footerText: AppLocalizations.of(context).settingsNameDescription,
                showClearButton: true,
                onChanged: (value) => _viewModel.deviceName.value = value,
              ),
        ),
      ),
    ),
  );

  // endregion
}
