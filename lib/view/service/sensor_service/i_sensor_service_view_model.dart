//
//  i_sensor_service_view_model.dart
//  EltakoConnect
//
//  Created by dennis on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/pairing/i_scan_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/view/service/i_service_view_model.dart';

/// Sensor Service View Model Interface
abstract class ISensorServiceViewModel extends IServiceViewModel {
  /// Sensors
  final List<ISensor> sensors = [];

  /// EnOcean available
  bool get enOceanAvailable => false;

  /// Device
  IDevice get device;

  /// Delete the [ISensor]
  Future<VoidResult> delete(ISensor sensor);

  /// Start scanning for sensors (optionally for specific [duration])
  Future<VoidResult> startScanning({Duration? duration});

  /// Get scan results of last scan
  Future<DataResult<List<EnOceanScanResult>>> getScanResults();

  /// Pair sensor
  Future<DataResult<ISensor>> pairSensor({required String name, required IScanResult scanResult});

  /// Update [sensor]
  Future<VoidResult> setSensor(ISensor sensor);
}
