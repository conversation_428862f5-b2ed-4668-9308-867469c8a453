//
//  i_sensor_button_type_service_view_model.dart
//  EltakoConnect
//
//  Created by dennis on 26.08.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/button_type.dart';
import 'package:eltako_connect/view/service/i_service_view_model.dart';
import 'package:flutter/widgets.dart';

/// Sensor_button_type Service View Model Interface
abstract class ISensorButtonTypeServiceViewModel extends IServiceViewModel {
  /// Button type
  ValueNotifier<ButtonType> buttonType = ValueNotifier(ButtonType.button4Way);

  /// Supported button types
  final List<ButtonType> supportedButtonTypes = [ButtonType.button2Way, ButtonType.button4Way];
}
