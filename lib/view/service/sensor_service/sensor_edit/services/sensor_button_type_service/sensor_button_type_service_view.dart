//
//  sensor_button_type_service_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 26.08.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/theme/eltako_padding.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_edit/services/sensor_button_type_service/i_sensor_button_type_service_view_model.dart';
import 'package:eltako_connect/widget/service_loading/service_loader.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Sensor_button_type Service View
class SensorButtonTypeServiceView extends StatelessWidget {
  /// Default constructor
  const SensorButtonTypeServiceView({required ISensorButtonTypeServiceViewModel viewModel, super.key})
    : _viewModel = viewModel;

  final ISensorButtonTypeServiceViewModel _viewModel;

  Future<void> _save(BuildContext context) async {
    // TODO(DK): Implement snackbar again after firmware supports button type change => Won't fix, because firmware does not support switch product id change
    // final result = await _viewModel.save();
    if (!context.mounted) return;

    const EltakoSnackBar.notSupported().show(context);
    context.pop();
    return;
    // EltakoSnackBar.result(
    //   result,
    //   message: result.success ? AppLocalizations.of(context).sensorButtonTypeChangedSuccessfully : result.error.message,
    // ).show(context);
    //
    // if (!result.success) return;
    // context.pop();
  }

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      title: AppLocalizations.of(context).setButtonType,
      trailing: NavigationSaveItem(onTap: () async => _save(context)),
    ),
    child: ServiceLoader(
      loadServiceData: _viewModel.loadService,
      child: ValueListenableBuilder(
        valueListenable: _viewModel.buttonType,
        builder:
            (context, buttonType, child) => SliverList.builder(
              itemCount: _viewModel.supportedButtonTypes.length,
              itemBuilder:
                  (context, index) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: EltakoPadding.xxxxs),
                    child: EltakoListTile(
                      position: RowPositionHelper.fromIndex(index, _viewModel.supportedButtonTypes.length),
                      onTap: () => _viewModel.buttonType.value = _viewModel.supportedButtonTypes[index],
                      leading: EltakoIcon.getIcon(
                        _viewModel.supportedButtonTypes[index].icon,
                        context,
                      ).image(height: 32, width: 32),
                      title: _viewModel.supportedButtonTypes[index].localizedName,
                      trailing:
                          _viewModel.buttonType.value == _viewModel.supportedButtonTypes[index]
                              ? const Icon(Icons.check)
                              : null,
                    ),
                  ),
            ),
      ),
    ),
  );
}
