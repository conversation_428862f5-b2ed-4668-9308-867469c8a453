//
//  sensor_service_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:collection/collection.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/enumeration/sensor_type_category.dart';
import 'package:eltako_connect/env/environment.dart';
import 'package:eltako_connect/extension/sensors_list_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/view/service/sensor_service/i_sensor_service_view_model.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_edit/sensor_edit_service_view.dart';
import 'package:eltako_connect/view/service/sensor_service/sensor_edit/sensor_edit_service_view_model.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/add_sensor/add_sensor_view.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/enocean_backpack_missing.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/no_sensors_available.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_progress.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_sheet.dart';
import 'package:eltako_connect/widget/service_loading/service_loader.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Sensor Service View
class SensorServiceView extends StatelessWidget {
  /// Default constructor
  const SensorServiceView({required ISensorServiceViewModel viewModel, super.key}) : _viewModel = viewModel;

  final ISensorServiceViewModel _viewModel;

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      title: AppLocalizations.of(context).settingsSensorHeader,
      leading:
          Environment.current.type == EnvironmentType.screenshots
              ? NavigationItem(icon: Assets.icons.arrowLeft.outline.light, onTap: () => {})
              : null,
      trailing: NavigationItem(
        icon: EltakoIcon.getIcon(Assets.icons.plus.outline, context),
        onTap: () async {
          if (_viewModel.enOceanAvailable) {
            await showAddSensorSheet(context);
            await Future.delayed(const Duration(milliseconds: 300), _viewModel.loadService);
          } else {
            await showEltakoSheet(
              context: context,
              builder: (context, _) => EnOceanBackpackMissing(deviceType: _viewModel.device.deviceInfo.deviceType),
            );
          }
        },
      ),
    ),
    child: ServiceLoader(
      loadServiceData: _viewModel.loadService,
      child: ListenableBuilder(
        listenable: _viewModel,
        builder:
            (context, child) =>
                _viewModel.sensors.groupBySensorType.isEmpty
                    ? SliverToBoxAdapter(
                      child: NoSensorsAvailable(
                        onAddSensors:
                            () async =>
                                _viewModel.enOceanAvailable
                                    ? showAddSensorSheet(context)
                                    : showEltakoSheet(
                                      context: context,
                                      builder:
                                          (context, _) => EnOceanBackpackMissing(
                                            deviceType: _viewModel.device.deviceInfo.deviceType,
                                          ),
                                    ),
                      ),
                    )
                    : SliverList.builder(
                      itemBuilder:
                          (context, index) => _SensorsSection(
                            entry: _viewModel.sensors.groupBySensorType.entries.elementAt(index),
                            viewModel: _viewModel,
                          ),
                      itemCount: _viewModel.sensors.groupBySensorType.length,
                    ),
      ),
    ),
  );

  /// Show add sensor sheet
  Future<void> showAddSensorSheet(BuildContext context) async => showEltakoSheet(
    context: context,
    builder:
        (context, _) => AddSensorsView(
          device: _viewModel.device,
          cancelTeachIn:
              () =>
                  context
                    ..pop()
                    ..pop(),
          scanForSensors: _viewModel.startScanning,
          getScanResults: _viewModel.getScanResults,
          pairSensor: _viewModel.pairSensor,
          setSensor: _viewModel.setSensor,
        ),
  );
}

/// Config section
class _SensorsSection extends StatefulWidget {
  final MapEntry<SensorTypeCategory, List<ISensor>> entry;

  final ISensorServiceViewModel _viewModel;

  const _SensorsSection({required this.entry, required ISensorServiceViewModel viewModel}) : _viewModel = viewModel;

  @override
  State<_SensorsSection> createState() => _SensorsSectionState();
}

class _SensorsSectionState extends State<_SensorsSection> {
  bool loading = false;

  @override
  Widget build(BuildContext context) => ExpansionTile(
    shape: const Border(),
    tilePadding: EdgeInsets.zero,
    title: Text(widget.entry.key.localizedName, style: Theme.of(context).textTheme.titleMedium),
    initiallyExpanded: true,
    children: [
      ...widget.entry.value.mapIndexed(
        (index, sensor) => Dismissible(
          key: ValueKey(sensor),
          direction:
              widget.entry.key != SensorTypeCategory.wiredButton ? DismissDirection.endToStart : DismissDirection.none,
          confirmDismiss: (direction) async {
            if (direction == DismissDirection.endToStart) {
              return showEltakoDialog(
                context: context,
                title: AppLocalizations.of(context).sensorDelete(sensor.category.localizedCategory),
                content: EltakoStyledText(
                  text: AppLocalizations.of(context).sensorDeleteHint(sensor.name, sensor.category.localizedCategory),
                  textStyle: Theme.of(context).textTheme.bodyMedium,
                  styledText: sensor.name,
                  styledTextStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                actions: [
                  EltakoButton(
                    label: Text(AppLocalizations.of(context).generalCancel),
                    onPressed: () => Navigator.of(context).pop(false),
                  ),
                  EltakoButton(
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (loading)
                          EltakoProgress(height: 12, width: 12, color: EltakoStyle.of(context).primaryTextSwitched),
                        SizedBox(width: loading ? EltakoPadding.xxs : EltakoPadding.zero),
                        Text(AppLocalizations.of(context).generalDelete, style: const TextStyle(color: Colors.white)),
                      ],
                    ),
                    eltakoButtonStyle: EltakoButtonStyle.filled,
                    color: EltakoStyle.of(context).error,
                    onPressed: () async {
                      widget._viewModel.sensors.remove(sensor);
                      Navigator.of(context).pop(true);
                      final result = await widget._viewModel.delete(sensor);
                      if (!context.mounted) return;
                      EltakoSnackBar.result(
                        result,
                        message:
                            result.success
                                ? AppLocalizations.of(context).sensorDeletedSuccessfully(sensor.name)
                                : result.message,
                      ).show(context);
                    },
                  ),
                ],
              );
            }
            return true;
          },
          background: DecoratedBox(
            decoration: BoxDecoration(
              color: EltakoStyle.of(context).error,
              borderRadius: BorderRadius.circular(EltakoRadius.l),
            ),
            child: Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(right: EltakoPadding.xl),
                child: EltakoIcon.getIcon(
                  Assets.icons.bin.outline,
                  context,
                ).image(height: 24, color: EltakoStyle.of(context).white),
              ),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.only(bottom: EltakoPadding.xxxs),
            child: EltakoListTile(
              onTap: () async {
                await Navigator.of(context).push(
                  MaterialPageRoute(
                    builder:
                        (context) => SensorEditServiceView(
                          viewModel: SensorEditServiceViewModel(
                            sensor,
                            widget._viewModel.device,
                            widget._viewModel.setSensor,
                            widget._viewModel.delete,
                          ),
                        ),
                  ),
                );
                await widget._viewModel.loadService();
              },
              position: RowPositionHelper.fromIndex(index, widget.entry.value.length),
              title: sensor.name,
              leading: EltakoIcon.getIcon(
                SensorType.fromId(sensor.productId).icon,
                context,
              ).image(width: 32, height: 32),
              titleWidget: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.entry.key.isEnOcean) ...{
                    Text(
                      widget.entry.key.localizedName,
                      style: Theme.of(context).textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  },
                  Text(sensor.name, style: Theme.of(context).textTheme.titleMedium, overflow: TextOverflow.ellipsis),
                  if (widget.entry.key.isEnOcean) ...{
                    Text(
                      sensor.sensorType.telegram,
                      style: Theme.of(context).textTheme.labelSmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  },
                ],
              ),
              // Hide until firmware supports it
              // trailing: widget.entry.key.isEnOcean
              //     ? ConstrainedBox(
              //         constraints: const BoxConstraints(
              //           minWidth: 100,
              //           maxHeight: 200,
              //         ),
              //         child: Column(
              //           mainAxisAlignment: MainAxisAlignment.center,
              //           children: [
              //             EltakoSignalStrength(
              //               signalStrength: sensor.tryCast<EnOceanButton>()?.signalStrength ?? 0,
              //             ),
              //             const SizedBox(height: EltakoPadding.xxxs),
              //             Text(
              //               sensor.tryCast<EnOceanButton>()?.lastSeen?.timeAgo ??
              //                   AppLocalizations.of(context).timerListitemUnknown,
              //               style: Theme.of(context).textTheme.bodySmall,
              //             ),
              //           ],
              //         ),
              //       )
              //     : null,
            ),
          ),
        ),
      ),
    ],
  );
}
