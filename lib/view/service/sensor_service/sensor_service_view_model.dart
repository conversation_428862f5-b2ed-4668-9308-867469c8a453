//
//  sensor_service_view_model.dart
//  EltakoConnect
//
//  Created by dennis on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/pairing/i_scan_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/service/sensor_service/i_sensor_service.dart';
import 'package:eltako_connect/view/service/sensor_service/i_sensor_service_view_model.dart';
import 'package:eltako_connect/widget/service_loading/service_loading.dart';
import 'package:eltako_log/eltako_log.dart';

/// Sensor Service View Model
class SensorServiceViewModel extends ISensorServiceViewModel {
  /// Sensor service
  final ISensorService _sensorService;

  @override
  bool get enOceanAvailable => _sensorService.supportsEnOcean;

  @override
  IDevice get device => _sensorService.device;

  /// Default constructor
  SensorServiceViewModel(this._sensorService) {
    unawaited(loadService());
    _sensorService.addListener(_updateFromData);
  }

  void _updateFromData() {
    if (_sensorService.data == null) return;
    unawaited(loadService());
  }

  @override
  Future<ServiceLoadingResult> loadService() async {
    final result = await _sensorService.getData(forceRefresh: true);
    if (result.success) {
      if (result.data == null) {
        return ServiceLoadingResult.error(ServiceLoadingError.missingData, result.message);
      }
      sensors
        ..clear()
        ..addAll(result.data!.sensors);
      notifyListeners();
      return ServiceLoadingResult.success();
    } else {
      debug(result.error);
      if (result.error == LocalizedError.deviceError) {
        return ServiceLoadingResult.success();
      }
      return ServiceLoadingResult.error(ServiceLoadingError.unknown, result.message);
    }
  }

  @override
  Future<VoidResult> delete(ISensor sensor) async {
    final result = await _sensorService.unpairSensor(sensor: sensor);
    notifyListeners();
    return result;
  }

  @override
  Future<DataResult<List<EnOceanScanResult>>> getScanResults() async {
    final result = await _sensorService.getScanResults();
    _sensorService.updateWidgetFromServiceData();
    return result;
  }

  @override
  Future<DataResult<ISensor>> pairSensor({required String name, required IScanResult scanResult}) async =>
      _sensorService.pairSensor(scanResult, name: name);

  @override
  Future<VoidResult> startScanning({Duration? duration}) => _sensorService.startScanning(duration: duration);

  @override
  // TODO(UI): not necessary, sensors are saved via setSensors function
  Future<VoidResult> save() => throw UnimplementedError();

  @override
  Future<VoidResult> setSensor(ISensor sensor) async => _sensorService.setSensor(sensor);
}
