//
//  sensor_service_view_model_demo.dart
//  EltakoConnect
//
//  Created by dennis on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/pairing/i_scan_result.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/view/service/sensor_service/i_sensor_service_view_model.dart';
import 'package:eltako_connect/widget/service_loading/service_loading.dart';

/// Sensor Service View Model
class SensorServiceViewModelDemo extends ISensorServiceViewModel {
  /// List of sensors
  final List<ISensor> demoSensors;

  /// Default constructor
  SensorServiceViewModelDemo(this.demoSensors);

  @override
  Future<VoidResult> save() => Future.value(const VoidResult.success());

  @override
  Future<ServiceLoadingResult> loadService() async {
    sensors.addAll(demoSensors);
    return ServiceLoadingResult.success();
  }

  @override
  Future<VoidResult> delete(ISensor sensor) => Future.value(const VoidResult.success());

  @override
  Future<DataResult<List<EnOceanScanResult>>> getScanResults() {
    // TODO(UI): implement getScanResults
    throw UnimplementedError();
  }

  @override
  Future<DataResult<ISensor>> pairSensor({required String name, required IScanResult scanResult}) {
    // TODO(UI): implement pairSensor
    throw UnimplementedError();
  }

  @override
  Future<VoidResult> startScanning({Duration? duration}) {
    // TODO(UI): implement startScanning
    throw UnimplementedError();
  }

  @override
  Future<VoidResult> setSensor(ISensor sensor) {
    // TODO(UI): implement setSensor
    throw UnimplementedError();
  }

  @override
  // TODO(UI): implement device
  IDevice get device => throw UnimplementedError();
}
