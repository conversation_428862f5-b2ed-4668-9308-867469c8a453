//
//  add_sensor_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 22.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:collection/collection.dart';
import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/localized_error.dart';
import 'package:eltako_connect/enumeration/sensor_orientation.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/extension/object_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/model/data_result.dart';
import 'package:eltako_connect/model/sensor/i_sensor.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_code_result.dart';
import 'package:eltako_connect/model/sensor/pairing/enocean_scan_result.dart';
import 'package:eltako_connect/model/sensor/pairing/i_scan_result.dart';
import 'package:eltako_connect/model/sensor/sensor/button_config.dart';
import 'package:eltako_connect/model/sensor/sensor/enocean_button.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/global/button_unset_configuration.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/add_sensor/scan_enocean_code_view.dart';
import 'package:eltako_connect/view/service/sensor_service/widgets/button_behavior.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_progress.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_sheet.dart';
import 'package:eltako_connect/widget/eltako_widgets/slider/eltako_icon_button.dart';
import 'package:eltako_connect/widget/eltako_widgets/stepper/eltako_step.dart';
import 'package:eltako_connect/widget/eltako_widgets/stepper/eltako_stepper.dart';
import 'package:eltako_connect/widget/validator/eltako_validator.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Add Sensors View
class AddSensorsView extends StatefulWidget {
  /// Device
  final IDevice device;

  /// Cancel teach-in
  final Function() cancelTeachIn;

  /// Scan for sensors function
  final Future<VoidResult> Function({Duration? duration}) scanForSensors;

  /// Get scan results function
  final Future<DataResult<List<EnOceanScanResult>>> Function() getScanResults;

  /// Pair sensor
  final Future<DataResult<ISensor>> Function({required String name, required IScanResult scanResult}) pairSensor;

  /// Update sensor
  final Future<VoidResult> Function(ISensor sensor) setSensor;

  /// Default constructor
  const AddSensorsView({
    required this.device,
    required this.cancelTeachIn,
    required this.scanForSensors,
    required this.getScanResults,
    required this.pairSensor,
    required this.setSensor,
    super.key,
  });

  @override
  State<AddSensorsView> createState() => _AddSensorsViewState();
}

class _AddSensorsViewState extends State<AddSensorsView> {
  /// UI Logic
  int currentStep = 0;
  int lastStep = 4;
  bool showSensorType = false;
  bool isSensorAddedWithTelegram = true;
  bool disableTeachInButton = true;
  bool scanningOrSaving = false; // is used for scanning and saving sensors

  /// Sensor
  SensorType sensorType = SensorType.enOceanButton1Way;
  List<ButtonConfig> buttonConfigs = [];
  TextEditingController sensorNameController = TextEditingController();

  // ignore: avoid_string_literals_inside_widget
  String enOceanId = '';

  /// Sensor Scanning
  List<EnOceanScanResult> scanResults = [];
  IScanResult? newScanResult;
  Timer? scanTimer;
  bool scanCanceled = false;

  Future<void> _scanForSensors() async {
    info('Start scanning for EnOcean sensors', category: LogCategory.uiEvent);
    const scanDuration = Duration(seconds: 15);
    int scanCount = 1;
    setState(() => scanCanceled = false);
    final result = await widget.scanForSensors(duration: scanDuration);
    if (!result.success) {
      warning(result.message);
      return;
    }
    scanTimer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (scanCanceled || scanCount > scanDuration.inSeconds) {
        info('Scanning ${scanCanceled ? 'canceled' : 'finished'}', category: LogCategory.uiEvent);
        setState(() => scanCanceled = true);
        timer.cancel();
        return;
      }
      info('$scanCount. Polling scan results', category: LogCategory.uiEvent);
      await _getScanResults();
      scanCount++;
    });
  }

  Future<void> _getScanResults() async {
    final result = await widget.getScanResults();
    if (!result.success) {
      warning(result.message, category: LogCategory.uiEvent);
      return;
    }
    info('Scan results: ${result.data}', category: LogCategory.uiEvent);

    final data = result.data;
    if (data == null) return;

    // filter scan results for chosen sensor type
    final filteredScanResults = data.where((result) => sensorType.matchesAny(result.potentialProducts)).toList();
    setState(() => scanResults = filteredScanResults);
  }

  Future<void> _pairSensor() async {
    final newSensor = newScanResult;
    if (newSensor == null) {
      warning('No sensor found', category: LogCategory.uiEvent);
      return;
    }
    sensorNameController.text.trim();
    if (sensorNameController.text.isEmpty) {
      sensorNameController.text = newSensor.id.toUpperCase();
    }
    if (isSensorAddedWithTelegram) {
      final enOceanScanResult = newSensor.tryCast<EnOceanScanResult>();
      enOceanScanResult?.selectProduct(
        enOceanScanResult.potentialProducts.firstWhere((product) => product.id == sensorType.id).id,
      );
    }
    final result = await widget.pairSensor(name: sensorNameController.text, scanResult: newSensor);
    if (!result.success) {
      if (mounted) {
        EltakoSnackBar.result(
          result,
          message:
              result.error == LocalizedError.pairingError
                  ? AppLocalizations.of(context).pairingFailed(sensorType.category.localizedCategory)
                  : result.message,
        ).show(context);
      }
      warning(result.message, category: LogCategory.uiEvent);
      setState(() => scanningOrSaving = false);
      return;
    }
    info('New Sensor: ${result.data}', category: LogCategory.data);
    final data = result.data;
    if (data == null) return;
    if (scanningOrSaving) await _setSensor(data);
  }

  Future<void> _setSensor(ISensor sensor) async {
    final button = sensor.tryCast<EnOceanButton>();
    if (button == null) {
      warning('No sensor found', category: LogCategory.uiEvent);
      return;
    }
    for (final config in buttonConfigs) {
      button.setConfiguration(config.configuration ?? ButtonUnsetConfiguration(), position: config.position);
    }
    button.orientation = SensorOrientation.clockwise0;

    final result = await widget.setSensor(button);
    if (!result.success) {
      if (mounted) EltakoSnackBar.result(result, message: result.message).show(context);
      warning(result.message, category: LogCategory.uiEvent);
      setState(() => scanningOrSaving = false);
      return;
    }
    if (scanningOrSaving) nextStep();
    info('${button.name} updated successfully');
  }

  @override
  void dispose() {
    scanTimer?.cancel();
    super.dispose();
  }

  void nextStep() => setState(() => currentStep++);

  String get currentHeader {
    switch (currentStep) {
      // Choose teach in method
      case 1:
        return AppLocalizations.of(context).sensorTeachInHeader(sensorType.localizedName);
      // Wait for telegram
      case 2:
        return sensorType.localizedName;
      // Configure sensor
      case 3:
        return AppLocalizations.of(context).sensorTeachInHeader(sensorType.category.localizedName);
      // Success dialog
      case 4:
        return AppLocalizations.of(context).timerDetailsSuccessdialogHeader;
      // Choose sensor type
      default:
        return AppLocalizations.of(context).sensorChooseHeader(l10n.button);
    }
  }

  @override
  Widget build(BuildContext context) => NavigationView(
    isSheet: true,
    header: NavigationHeader(
      isSheet: true,
      title: currentHeader,
      leading:
          currentStep != 0 && currentStep != lastStep
              ? NavigationItem(
                icon: EltakoIcon.getIcon(Assets.icons.arrowLeft.outline, context),
                onTap: () {
                  scanCanceled = true;
                  scanningOrSaving = false;
                  setState(() => currentStep--);
                },
              )
              : null,
      trailing: NavigationItem(
        icon: EltakoIcon.getIcon(Assets.icons.cancel.outline, context),
        onTap: () async => currentStep == 0 || currentStep == lastStep ? context.pop() : showCancelDialog(),
      ),
    ),
    child: SliverToBoxAdapter(
      child: Column(
        children: [
          EltakoStepper(
            currentStep: currentStep,
            showVerticalStepper: false,
            steps: [
              /// Hidden until there are more sensors, than only buttons
              if (showSensorType) _step0SensorType(context),
              _step1ButtonType(context),
              _step2ScanUI(context),
              _step2WaitForTelegram(context),
              _step3ConfigureSensor(context),
              _step4Done(context),
            ],
          ),
          if (kDebugMode && currentStep != lastStep) ...{
            const SizedBox(height: EltakoPadding.xxl),
            // ignore: avoid_string_literals_inside_widget
            EltakoButton(label: const Text('Next Step'), onPressed: nextStep),
          },
        ],
      ),
    ),
  );

  EltakoStep _step0SensorType(BuildContext context) => EltakoStep(
    title: currentHeader,
    state: EltakoStepState.disabled,
    content: Text(AppLocalizations.of(context).sensorCategoryDescription),
  );

  /// Choose the button type (1-way, 2-way, 4-way)
  EltakoStep _step1ButtonType(BuildContext context) => EltakoStep(
    title: currentHeader,
    state: EltakoStepState.active,
    content: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(AppLocalizations.of(context).enOceanButton, style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: EltakoPadding.xs),
        ...SensorType.enOceanButtons.mapIndexed(
          (index, button) => Padding(
            padding: const EdgeInsets.only(bottom: EltakoPadding.xxxs),
            child: EltakoListTile(
              position: RowPositionHelper.fromIndex(index, SensorType.enOceanButtons.length),
              leading: EltakoIcon.getIcon(button.icon, context).image(height: 32, width: 32),
              title: button.localizedName,
              onTap: () async {
                sensorType = button;
                nextStep();
              },
            ),
          ),
        ),
      ],
    ),
  );

  /// Choose the teach in method (EnOcean or manual via EurID or QR code)
  EltakoStep _step2ScanUI(BuildContext context) => EltakoStep(
    title: currentHeader,
    state: EltakoStepState.done,
    content: Column(
      children: [
        EltakoIcon.getIcon(Assets.icons.enocean.outline, context).image(),
        const SizedBox(height: EltakoPadding.xs),
        Text(AppLocalizations.of(context).enOceanAddDescription),
        const SizedBox(height: EltakoPadding.xl),
        EltakoButton(
          eltakoButtonStyle: EltakoButtonStyle.filled,
          expand: true,
          label: Text(AppLocalizations.of(context).enOceanAddAutomatically),
          onPressed: () async {
            scanResults.clear();
            isSensorAddedWithTelegram = true;
            nextStep();
            await _scanForSensors();
          },
        ),
        EltakoButton(
          expand: true,
          label: Text(AppLocalizations.of(context).enOceanAddManually),
          onPressed: () {
            isSensorAddedWithTelegram = false;
            nextStep();
          },
        ),
      ],
    ),
  );

  /// Shows either the EnOcean sensor scanning view or the EurID and QR Code view
  EltakoStep _step2WaitForTelegram(BuildContext context) => EltakoStep(
    title: currentHeader,
    state: EltakoStepState.active,
    content: AnimatedCrossFade(
      firstChild: _sensorList(context),
      secondChild: _sensorAddManually(),
      crossFadeState: isSensorAddedWithTelegram ? CrossFadeState.showFirst : CrossFadeState.showSecond,
      duration: const Duration(milliseconds: 300),
    ),
  );

  /// Add sensor automatically via EnOcean telegram
  Widget _sensorList(BuildContext context) => Column(
    children: [
      Image.asset(sensorType.imagePath, height: 150),
      const SizedBox(height: EltakoPadding.m),
      Text(AppLocalizations.of(context).buttonTapDescription),
      const SizedBox(height: EltakoPadding.xl),
      Row(
        children: [
          Text(
            AppLocalizations.of(context).buttonsFound(scanResults.length),
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const Spacer(),
          if (!scanCanceled) ...{
            const Padding(
              padding: EdgeInsets.only(right: EltakoPadding.xxxs),
              child: SizedBox(height: 14, width: 14, child: EltakoProgress()),
            ),
          } else if (scanResults.isNotEmpty) ...{
            EltakoButton(
              label: Text(AppLocalizations.of(context).searchAgain),
              expand: true,
              onPressed: _scanForSensors,
            ),
          } else ...{
            const SizedBox(height: EltakoPadding.m),
          },
        ],
      ),
      Align(alignment: Alignment.centerLeft, child: Text(AppLocalizations.of(context).sensorChooseDescription)),
      const SizedBox(height: EltakoPadding.xs),
      if (scanResults.isEmpty) ...{
        const SizedBox(height: EltakoPadding.s),
        EltakoIcon.getIcon(Assets.images.sensors.noSwitchFound, context).image(height: 50),
        const SizedBox(height: EltakoPadding.xs),
        Text(AppLocalizations.of(context).sensorsFound(0)),
        const SizedBox(height: EltakoPadding.xl),
        if (scanCanceled)
          EltakoButton(
            eltakoButtonStyle: EltakoButtonStyle.filled,
            expand: true,
            label: Text(AppLocalizations.of(context).sensorSearch),
            onPressed: _scanForSensors,
          ),
      },
      ...scanResults.map(
        (scanResult) => Padding(
          padding: const EdgeInsets.only(bottom: EltakoPadding.xxxs),
          child: EltakoListTile(
            position: RowPositionHelper.fromIndex(
              scanResults.indexWhere((element) => element == scanResult),
              scanResults.length,
            ),
            leading: EltakoIcon.getIcon(
              sensorType.icon,
              // scanResult.
              // SensorType.fromId(
              //   scanResult.potentialProducts.firstWhereOrNull((product) => product.id == sensorType.id)?.id ??
              //       sensorType.id,
              // ).icon,
              context,
            ).image(height: 32, width: 32),
            titleWidget: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  SensorType.fromId(
                    scanResult.potentialProducts.firstWhereOrNull((product) => product.id == sensorType.id)?.id ??
                        sensorType.id,
                  ).category.localizedName,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                Text(scanResult.id.toUpperCase(), style: Theme.of(context).textTheme.titleMedium),
                Text(sensorType.telegram, style: Theme.of(context).textTheme.labelSmall),
              ],
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  (scanResult.signalsReceived / 2).round().toStringAsFixed(0),
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                // Hide until firmware supports it
                // if (scanResult.lastSeen != null)
                //   Text(scanResult.lastSeen?.timeAgo ?? AppLocalizations.of(context).timerListitemUnknown),
              ],
            ),
            onTap: () {
              newScanResult = scanResult;
              _clearButtonConfigs();
              nextStep();
              scanCanceled = true;
            },
          ),
        ),
      ),
    ],
  );

  /// Add sensor manually via EurID or QR code
  Widget _sensorAddManually() => Column(
    children: [
      EltakoIcon.getIcon(Assets.icons.qrCode.outline, context).image(height: 150),
      const SizedBox(height: EltakoPadding.m),
      Text(AppLocalizations.of(context).enOceanCodeScan(sensorType.category.localizedCategory)),
      const SizedBox(height: EltakoPadding.xl),
      Align(
        alignment: Alignment.centerLeft,
        child: Text(AppLocalizations.of(context).enOceanAddManually, style: Theme.of(context).textTheme.titleMedium),
      ),
      const SizedBox(height: EltakoPadding.xs),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: EltakoTextField(
              // ignore: avoid_string_literals_inside_widget
              hintText: AppLocalizations.of(context).forExample('FEF0CB0D'),
              controller: TextEditingController(text: enOceanId),
              showClearButton: true,
              onChanged:
                  (value) => setState(() {
                    disableTeachInButton = value.isEmpty || (value.length != 8 && value.length != 12);
                    enOceanId = value;
                  }),

              /// Because eurIdHex can be 8-12 characters long with leading 0000, but might be already valid with 8 characters
              characterLimit: 12,
              allowedCharsRegex: EltakoValidator.allowedHexChars,
              validator: EltakoValidator.enOceanIdValidator,
            ),
          ),
          const SizedBox(width: EltakoPadding.xxs),
          EltakoIconButton.squared(
            icon: EltakoIcon.getIcon(Assets.icons.qrCode.outline, context),
            onTap:
                () async => showEltakoSheet(
                  isDismissible: false,
                  context: context,
                  builder:
                      (context, scrollController) => ScanEnOceanCodeView(
                        onCanceled: () => context.pop(),
                        onQRScanComplete: (barcode) async {
                          context.pop();
                          sensorNameController.clear();
                          _clearButtonConfigs();
                          nextStep();
                          newScanResult = EnOceanCodeResult.fromEnOceanCode(barcode, productId: sensorType.id);
                        },
                        scrollController: scrollController,
                      ),
                ),
          ),
        ],
      ),
      const SizedBox(height: EltakoPadding.s),
      if (newScanResult != null)
        Padding(
          padding: const EdgeInsets.only(bottom: EltakoPadding.xxxs),
          child: EltakoListTile(
            position: RowPosition.single,
            leading: EltakoIcon.getIcon(sensorType.icon, context).image(height: 32, width: 32),
            titleWidget: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(sensorType.category.localizedName, style: Theme.of(context).textTheme.bodySmall),
                Text(newScanResult!.id.toUpperCase(), style: Theme.of(context).textTheme.titleMedium),
                Text(AppLocalizations.of(context).timerListitemUnknown, style: Theme.of(context).textTheme.labelSmall),
              ],
            ),
            onTap: nextStep,
          ),
        ),
      const SizedBox(height: EltakoPadding.xxxs),
      EltakoButton(
        eltakoButtonStyle: EltakoButtonStyle.filled,
        expand: true,
        label: Text(AppLocalizations.of(context).sensorTeachInHeader(sensorType.category.localizedName)),
        onPressed:
            disableTeachInButton
                ? null
                : () {
                  if (enOceanId.length != 8 && enOceanId.length != 12) {
                    EltakoSnackBar(
                      title: AppLocalizations.of(context).enOceanIdInvalid,
                      state: EltakoSnackBarState.error,
                    ).show(context);
                    return;
                  }
                  scanCanceled = true;
                  sensorNameController.clear();
                  _clearButtonConfigs();
                  newScanResult = EnOceanCodeResult.fromEurId(enOceanId.toUpperCase(), productId: sensorType.id);
                  nextStep();
                },
      ),
      const SizedBox(height: EltakoPadding.xs),
    ],
  );

  /// Configure the sensor like as in sensor edit view
  EltakoStep _step3ConfigureSensor(BuildContext context) => EltakoStep(
    title: currentHeader,
    state: EltakoStepState.active,
    content: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        EltakoTextField(
          headerText: AppLocalizations.of(context).sensorName,
          hintText: AppLocalizations.of(context).generalTextName,
          footerText: AppLocalizations.of(context).sensorNameFooter,
          controller: sensorNameController,
        ),
        const SizedBox(height: EltakoPadding.xl),
        Text(
          AppLocalizations.of(context).detailsConfigurationSwitchbehavior,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: EltakoPadding.xs),
        ButtonBehavior(
          isSavingWithoutRequest: true,
          device: widget.device,
          sensorType: sensorType,
          buttonConfigs: buttonConfigs,
          onButtonChange: (position, category, configuration) async {
            setState(
              () =>
                  buttonConfigs
                    ..removeWhere((config) => config.position == position)
                    ..add(
                      ButtonConfig(
                        position: position,
                        category: category,
                        configuration: configuration,
                        configurationLogic: configuration.buttonLogic,
                      ),
                    ),
            );

            return const VoidResult.success();
          },
        ),
        const SizedBox(height: EltakoPadding.xl),
        EltakoButton(
          icon:
              scanningOrSaving
                  ? const EltakoProgress(
                    width: 10,
                    height: 10,
                    color: EltakoColors.white,
                    padding: EdgeInsets.symmetric(horizontal: EltakoPadding.xxxs),
                  )
                  : null,
          eltakoButtonStyle: EltakoButtonStyle.filled,
          expand: true,
          label: Text(AppLocalizations.of(context).generalSave),
          onPressed: () async {
            if (scanningOrSaving) return;
            setState(() => scanningOrSaving = true);
            await _pairSensor();
          },
        ),
      ],
    ),
  );

  /// Successfully added sensor
  EltakoStep _step4Done(BuildContext context) => EltakoStep(
    title: currentHeader,
    state: EltakoStepState.done,
    content: Column(
      children: [
        Stack(
          alignment: Alignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: EltakoPadding.xl, vertical: EltakoPadding.xl),
              child: Image.asset(sensorType.imagePath, height: 150),
            ),
            Positioned(
              bottom: 12,
              right: 12,
              child: EltakoIcon.getIcon(Assets.icons.success.filled, context).image(height: 64),
            ),
          ],
        ),
        const SizedBox(height: EltakoPadding.xs),
        Text(AppLocalizations.of(context).sensorAddedSuccessfully(sensorNameController.text)),
        const SizedBox(height: EltakoPadding.xl),
        EltakoButton(
          eltakoButtonStyle: EltakoButtonStyle.filled,
          expand: true,
          label: Text(AppLocalizations.of(context).generalDone),
          onPressed: () => context.pop(),
        ),
      ],
    ),
  );

  Future<void> showCancelDialog() async => showEltakoDialog(
    context: context,
    title: AppLocalizations.of(context).sensorCancel,
    content: Text(AppLocalizations.of(context).sensorCancelDescription),
    actions: [
      EltakoButton(
        label: Text(AppLocalizations.of(context).generalCancel),
        onPressed: () => Navigator.of(context).pop(),
      ),
      EltakoButton(
        color: EltakoStyle.of(context).error,
        label: Text(AppLocalizations.of(context).sensorCancel),
        onPressed: () {
          scanCanceled = true;
          widget.cancelTeachIn();
        },
      ),
    ],
  );

  void _clearButtonConfigs() =>
      buttonConfigs
        ..clear()
        ..addAll(
          sensorType.possibleButtonBehaviorPositions
              .map(
                (position) => ButtonConfig(
                  category: widget.device.deviceInfo.deviceType.category,
                  position: position,
                  configurationLogic: ButtonChannelConfigurationLogic.unset,
                ),
              )
              .toList(),
        );
}
