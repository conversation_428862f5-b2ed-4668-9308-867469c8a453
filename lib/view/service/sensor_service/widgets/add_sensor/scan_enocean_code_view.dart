//
//  scan_enocean_code_view.dart
//  EltakoConnect
//
//  Created by <PERSON> on 30.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:eltako_connect/enumeration/authorization_code_type.dart';
import 'package:eltako_connect/enumeration/sensor_type_category.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/widget/barcode_scanner/barcode_scanner.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

/// Login password view
class ScanEnOceanCodeView extends StatefulWidget {
  /// Scroll controller
  final ScrollController? scrollController;

  /// On canceled
  final Function()? onCanceled;

  /// Login
  final Function(String barcode)? onQRScanComplete;

  /// Default constructor
  const ScanEnOceanCodeView({super.key, this.scrollController, this.onCanceled, this.onQRScanComplete});

  @override
  State<ScanEnOceanCodeView> createState() => _ScanEnOceanCodeViewState();
}

class _ScanEnOceanCodeViewState extends State<ScanEnOceanCodeView> {
  final MobileScannerController _scanController = MobileScannerController(detectionSpeed: DetectionSpeed.noDuplicates);

  Future<void> enOceanIdFound(String barcode, BuildContext context) async {
    if (barcode.isEmpty) {
      EltakoSnackBar(
        state: EltakoSnackBarState.error,
        title: AppLocalizations.of(context).error,
        message: AppLocalizations.of(context).wifiAuthorizationPopIsEmpty,
      ).show(context);
      return;
    }
    widget.onQRScanComplete?.call(barcode);
  }

  @override
  void dispose() {
    _scanController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => NavigationView(
    header: NavigationHeader(
      title: AppLocalizations.of(context).detailsConfigurationWifiloginScan,
      isSheet: true,
      leading: NavigationItem(text: AppLocalizations.of(context).generalCancel, onTap: () => widget.onCanceled?.call()),
    ),
    child: SliverToBoxAdapter(
      child: Column(
        children: [
          const SizedBox(height: EltakoPadding.xs),
          BarcodeScanner(
            controller: _scanController,
            onBarcodeDetected: (type, barcode) async {
              if (type == AuthorizationCodeType.enOcean) {
                EltakoSnackBar(title: AppLocalizations.of(context).codeFound(type.name)).show(context);
                await enOceanIdFound(barcode, context);
              } else if (type == AuthorizationCodeType.enOceanKatekSn) {
                EltakoSnackBar(
                  title: AppLocalizations.of(context).detailsConfigurationWifiloginScannotvalid,
                  message: AppLocalizations.of(context).enOceanQRCodeInvalidDescription,
                ).show(context);
              } else {
                EltakoSnackBar(
                  title: type.name,
                  message: AppLocalizations.of(context).detailsConfigurationWifiloginScannotvalid,
                ).show(context);
              }
            },
          ),
          const SizedBox(height: EltakoPadding.xl),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(AppLocalizations.of(context).enOceanCode, style: Theme.of(context).textTheme.titleMedium),
          ),
          const SizedBox(height: EltakoPadding.l),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(flex: 3, child: EltakoIcon.getIcon(Assets.icons.qrCode.outline, context).image()),
              const Flexible(child: SizedBox(width: EltakoPadding.xs)),
              Flexible(
                flex: 6,
                child: Text(
                  AppLocalizations.of(
                    context,
                  ).enOceanCodeScanDescription(SensorTypeCategory.enOceanButton.localizedCategory),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
