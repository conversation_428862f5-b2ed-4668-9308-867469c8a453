//
//  switch_behavior.dart
//  EltakoConnect
//
//  Created by <PERSON> on 22.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/device/wifi/esb64ip.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_function.dart';
import 'package:eltako_connect/enumeration/device_type_category.dart';
import 'package:eltako_connect/enumeration/sensor_channel_position.dart';
import 'package:eltako_connect/enumeration/sensor_type.dart';
import 'package:eltako_connect/extension/list_extension.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/sensor/sensor/button_config.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/i_button_configuration.dart';
import 'package:eltako_connect/model/void_result.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:el<PERSON><PERSON>_connect/view/service/sensor_service/widgets/switch_behavior.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_sheet.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Button Behavior
class ButtonBehavior extends StatelessWidget {
  /// Device
  final IDevice device;

  /// Channels
  final List<ButtonConfig> buttonConfigs;

  /// On button change Callback
  final Future<VoidResult> Function(
    SensorChannelPosition position,
    DeviceTypeCategory category,
    IButtonConfiguration configuration,
  )
  onButtonChange;

  /// Button type
  final SensorType sensorType;

  /// Is saving without request
  final bool isSavingWithoutRequest;

  /// Default constructor
  const ButtonBehavior({
    required this.device,
    required this.buttonConfigs,
    required this.onButtonChange,
    this.sensorType = SensorType.enOceanButton4Way,
    this.isSavingWithoutRequest = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) => Center(
    child: ConstrainedBox(
      constraints: const BoxConstraints(minWidth: 150, maxWidth: 450, minHeight: 150, maxHeight: 450),
      child: DecoratedBox(
        decoration: BoxDecoration(
          borderRadius: SensorChannelPosition.single.cornerRadius,
          color: EltakoStyle.of(context).primaryElementBackground,
        ),
        child: Padding(
          padding: const EdgeInsets.all(EltakoPadding.xs),
          child: switch (sensorType) {
            SensorType.enOceanButton1Way => AspectRatio(
              aspectRatio: 1,
              child: _tapArea(context, sensorPosition: SensorChannelPosition.single),
            ),
            SensorType.enOceanButton2Way => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                AspectRatio(aspectRatio: 2 / 1, child: _tapArea(context, sensorPosition: SensorChannelPosition.top)),
                AspectRatio(aspectRatio: 2 / 1, child: _tapArea(context, sensorPosition: SensorChannelPosition.bottom)),
              ],
            ),
            SensorType.enOceanButton4Way => _multiSwitch(context),
            _ => Center(child: Text(AppLocalizations.of(context).deviceNameChangedFailed)),
          },
        ),
      ),
    ),
  );

  Widget _multiSwitch(BuildContext context) => AspectRatio(
    aspectRatio: 1,
    child: Row(
      children: [
        Flexible(
          fit: FlexFit.tight,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _tapArea(context, sensorPosition: SensorChannelPosition.topLeft),
              _tapArea(context, sensorPosition: SensorChannelPosition.bottomLeft),
            ],
          ),
        ),
        const SizedBox(width: EltakoPadding.xxs),
        Flexible(
          fit: FlexFit.tight,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _tapArea(context, sensorPosition: SensorChannelPosition.topRight),
              _tapArea(context, sensorPosition: SensorChannelPosition.bottomRight),
            ],
          ),
        ),
      ],
    ),
  );

  Widget _tapArea(BuildContext context, {required SensorChannelPosition sensorPosition}) {
    final buttonConfig = buttonConfigs.firstWhereOrNull((config) => config.position == sensorPosition);
    info('Button configuration for $sensorPosition is ${buttonConfig?.configurationLogic.name ?? 'not available'}');

    final Widget buttonLabel = Padding(
      padding: const EdgeInsets.all(EltakoPadding.xxxs),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            buttonConfig?.configurationLogic.localizedName(device is Esb64ip) ??
                AppLocalizations.of(context).buttonUnset,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          if (buttonConfig?.configurationLogic != null &&
              buttonConfig!.configurationLogic.hasSelectableFunctions &&
              buttonConfig.function != ButtonChannelConfigurationFunction.none) ...{
            Text(
              // ignore: avoid_string_literals_inside_widget
              '(${buttonConfig.function.name})',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          },
        ],
      ),
    );

    return DecoratedBox(
      decoration: BoxDecoration(
        borderRadius: sensorPosition.cornerRadius,
        color: EltakoStyle.of(context).primaryElementBackground,
      ),
      child: InkWell(
        onTap:
            () async => showEltakoSheet(
              context: context,
              builder:
                  (context, _) => SwitchBehavior(
                    isSavingWithoutRequest: isSavingWithoutRequest,
                    device: device,
                    configuration: buttonConfig?.configuration,
                    onSave: (configuration) async {
                      final result = await onButtonChange(
                        sensorPosition,
                        buttonConfig?.category ?? DeviceTypeCategory.unknown,
                        configuration,
                      );
                      if (!context.mounted) return;
                      if (isSavingWithoutRequest) {
                        context.pop();
                        return;
                      }
                      EltakoSnackBar.result(
                        result,
                        message:
                            result.success
                                ? AppLocalizations.of(context).wiredInputChangedSuccesfully
                                : result.error.message,
                      ).show(context);

                      if (!result.success) return;

                      context.pop();
                    },
                  ),
            ),
        borderRadius: sensorPosition.cornerRadius,
        child: AspectRatio(
          aspectRatio: 1,
          child: ConstrainedBox(
            constraints: const BoxConstraints(minWidth: 150, maxWidth: 300, minHeight: 150, maxHeight: 300),
            child:
                sensorPosition == SensorChannelPosition.single
                    ? Column(children: [const Spacer(), const Spacer(), const Spacer(), buttonLabel, const Spacer()])
                    : buttonLabel,
          ),
        ),
      ),
    );
  }
}
