//
//  enocean_backpack_missing.dart
//  EltakoConnect
//
//  Created by <PERSON> on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/enumeration/device_type.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/launcher.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// EnOcean Backpack Missing
class EnOceanBackpackMissing extends StatelessWidget {
  /// Device
  final DeviceType deviceType;

  /// Default constructor
  const EnOceanBackpackMissing({required this.deviceType, super.key});

  @override
  Widget build(BuildContext context) => NavigationView(
    isSheet: true,
    header: NavigationHeader(
      isSheet: true,
      title: AppLocalizations.of(context).enOceanBackpack,
      trailing: NavigationItem(
        icon: EltakoIcon.getIcon(Assets.icons.cancel.outline, context),
        onTap: () => context.pop(),
      ),
    ),
    child: SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: EltakoPadding.xl),
          Text(
            AppLocalizations.of(context).enOceanAdapterNotFound,
            style: Theme.of(context).textTheme.titleLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: EltakoPadding.xl),
          Row(
            children: [
              Image.asset(_series64EnOceanOpen(), height: 150),
              const SizedBox(width: EltakoPadding.xl),
              Image.asset(_series64EnOceanCombined(), height: 150),
            ],
          ),
          const SizedBox(height: EltakoPadding.xl),
          Text(AppLocalizations.of(context).enOceanBackpackMissing),
          const SizedBox(height: EltakoPadding.xl),
          EltakoButton(
            expand: true,
            eltakoButtonStyle: EltakoButtonStyle.filled,
            label: Text(AppLocalizations.of(context).getEnOceanBackpack),
            onPressed:
                () async => Launcher.launchInBrowser(
                  context,
                  // TODO(UI): get URL from anywhere else
                  // ignore: avoid_string_literals_inside_widget
                  'https://eltako.com/redirect/EOA64',
                ),
          ),
        ],
      ),
    ),
  );

  String _series64EnOceanOpen() => switch (deviceType) {
    DeviceType.esr64pfipm => Assets.images.devices.esr64pfIpm.eOA64ESR64PFIPMOpen.path,
    DeviceType.esr64npipm => Assets.images.devices.esr64npIpm.eOA64ESR64NPIPMOpen.path,
    DeviceType.eud64npnipm => Assets.images.devices.eud64npnIpm.eOA64EUD64NPNIPMOpen.path,
    // TODO(DK): add image for ESB64NPIPM
    DeviceType.esb64npipm => Assets.images.devices.eud64npnIpm.eOA64EUD64NPNIPMOpen.path,
    _ => Assets.images.devices.eoa64.backpack.path,
  };

  String _series64EnOceanCombined() => switch (deviceType) {
    DeviceType.esr64pfipm => Assets.images.devices.esr64pfIpm.eOA64ESR64PFIPMCombined.path,
    DeviceType.esr64npipm => Assets.images.devices.esr64npIpm.eOA64ESR64NPIPMCombined.path,
    DeviceType.eud64npnipm => Assets.images.devices.eud64npnIpm.eOA64EUD64NPNIPMCombined.path,
    // TODO(DK): add image for ESB64NPIPM
    DeviceType.esb64npipm => Assets.images.devices.eud64npnIpm.eOA64EUD64NPNIPMCombined.path,
    _ => Assets.images.devices.eoa64.backpack.path,
  };
}
