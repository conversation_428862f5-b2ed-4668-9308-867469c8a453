//
//  no_sensors_available.dart
//  EltakoConnect
//
//  Created by <PERSON> on 15.07.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_button.dart';
import 'package:flutter/material.dart';

///
class NoSensorsAvailable extends StatelessWidget {
  /// On add sensors
  final VoidCallback onAddSensors;

  /// Default constructor
  const NoSensorsAvailable({required this.onAddSensors, super.key});

  @override
  Widget build(BuildContext context) => Column(
    children: [
      const SizedBox(height: EltakoPadding.xl),
      EltakoIcon.getIcon(Assets.icons.devices.outline, context).image(height: 180),
      Text(AppLocalizations.of(context).sensorNotAvailable, style: Theme.of(context).textTheme.titleMedium),
      const SizedBox(height: EltakoPadding.xl),
      EltakoButton(
        eltakoButtonStyle: EltakoButtonStyle.filled,
        expand: true,
        label: Text(AppLocalizations.of(context).sensorAdd),
        onPressed: onAddSensors,
      ),
    ],
  );
}
