//
//  SwitchBehavior.dart
//  EltakoConnect
//
//  Created by <PERSON> on 23.08.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/device/wifi/esb64ip.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_function.dart';
import 'package:eltako_connect/enumeration/button_channel_configuration_logic.dart';
import 'package:eltako_connect/enumeration/eltako_slider_unit.dart';
import 'package:eltako_connect/enumeration/shutter_tilt_automatic_type.dart';
import 'package:eltako_connect/enumeration/time_multiplier.dart';
import 'package:eltako_connect/extension/double_extension.dart';
import 'package:eltako_connect/extension/int_extension.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/model/multiplied_time.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_central_down_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_central_up_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_directional_down_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_directional_up_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_scene_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/dimmer/button_dimmer_universal_logic_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/global/button_unset_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/i_button_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/relay/button_relay_on_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/relay/button_relay_universal_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_directional_down_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_directional_up_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_scene_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/button/shutter/button_shutter_universal_add_on_configuration.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/extension/children_room_logic_configuration_extension.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/extension/light_alarm_logic_configuration_extension.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/extension/slumber_logic_configuration_extension.dart';
import 'package:eltako_connect/model/sensor/sensor_logic_configuration/i_sensor_configuration.dart';
import 'package:eltako_connect/model/shutter_tilt_automatic.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/view/service/relay_fuction_service/widgets/multiplied_time_edit.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Switch Behavior
class SwitchBehavior extends StatefulWidget {
  /// Configuration
  final IButtonConfiguration? configuration;

  /// Device
  final IDevice device;

  /// Is wired input
  final bool isWired;

  /// Is saving without request
  final bool isSavingWithoutRequest;

  /// On save Callback
  final Function(IButtonConfiguration configuration) onSave;

  /// Default constructor
  const SwitchBehavior({
    required this.configuration,
    required this.device,
    required this.onSave,
    this.isWired = false,
    this.isSavingWithoutRequest = false,
    super.key,
  });

  @override
  State<SwitchBehavior> createState() => _SwitchBehaviorState();
}

class _SwitchBehaviorState extends State<SwitchBehavior> {
  late IButtonConfiguration configuration;
  late List<IButtonConfiguration> _supportedConfigurations;
  late List<ButtonChannelConfigurationFunction> _supportedFunctions;

  @override
  void initState() {
    configuration =
        ISensorConfiguration.fromMap((widget.configuration ?? ButtonUnsetConfiguration()).toMap())
            as IButtonConfiguration? ??
        ButtonUnsetConfiguration();
    _supportedConfigurations = ButtonChannelConfigurationLogic.getSupportedLogics(
      widget.device,
      isWired: widget.isWired,
    );
    _supportedFunctions = ButtonChannelConfigurationFunction.getSupportedFunctions(
      widget.device,
      logic: configuration.buttonLogic,
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) => NavigationView(
    isSheet: true,
    header: NavigationHeader(
      isSheet: true,
      title: AppLocalizations.of(context).detailsConfigurationSwitchbehavior,
      leading: NavigationItem(text: AppLocalizations.of(context).generalCancel, onTap: () => context.pop()),
      trailing:
          widget.isSavingWithoutRequest
              ? NavigationItem(
                text: AppLocalizations.of(context).generalTextDone,
                onTap: () => widget.onSave(configuration),
              )
              : NavigationSaveItem(onTap: () => widget.onSave(configuration)),
    ),
    child: SliverToBoxAdapter(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).detailsConfigurationSwitchbehavior,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: EltakoPadding.xs),
          EltakoPicker(
            style: EltakoPickerStyle.dialog,
            tabs: _supportedConfigurations.map((supportedConfig) => supportedConfig.buttonLogic).toList(),
            onTap:
                (newConfiguration) => setState(
                  () =>
                      configuration = _supportedConfigurations.firstWhere(
                        (supportedConfig) => supportedConfig.buttonLogic == newConfiguration,
                      ),
                ),
            initialSelection: configuration.buttonLogic,
            icon: EltakoIcon.getIcon(Assets.icons.lightSwitch.circle, context).image(height: 40, width: 40),
            dialogTitle: AppLocalizations.of(context).detailsConfigurationSwitchbehavior,
            labelBuilder: (config) => config.localizedName(widget.device is Esb64ip),
            subtitleWidgetBuilder:
                (config) => Text(
                  config.localizedDescription(widget.device is Esb64ip),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            child: EltakoListTile(
              leading: EltakoIcon.getIcon(Assets.icons.lightSwitch.circle, context).image(height: 40, width: 40),
              title: configuration.buttonLogic.localizedName(widget.device is Esb64ip),
            ),
          ),
          if (configuration.buttonLogic.getImage(device: widget.device) != null) ...{
            const SizedBox(height: EltakoPadding.xl),
            Center(
              child: Image.asset(
                configuration.buttonLogic.getImage(device: widget.device)!,
                width: 200,
                color: configuration.buttonLogic.imageColor(context),
              ),
            ),
            const SizedBox(height: EltakoPadding.l),
          },
          const SizedBox(height: EltakoPadding.xs),
          Text(configuration.buttonLogic.localizedDescription(widget.device is Esb64ip)),
          _buildButtonFunctions(context),
        ],
      ),
    ),
  );

  final List<Widget> _divider = [
    const SizedBox(height: EltakoPadding.l),
    const Divider(),
    const SizedBox(height: EltakoPadding.l),
  ];

  Widget _buildButtonFunctions(BuildContext context) {
    _supportedFunctions = ButtonChannelConfigurationFunction.getSupportedFunctions(
      widget.device,
      logic: configuration.buttonLogic,
    );

    return (configuration.buttonLogic.hasSelectableFunctions && _supportedFunctions.isNotEmpty) ||
            configuration.buttonLogic.hasExtensions(widget.device)
        ? Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ..._divider,
            ListenableBuilder(
              listenable: configuration,
              builder: (context, snapshot) => _buildFunctionsAndExtensions(context),
            ),
            const SizedBox(height: EltakoPadding.xl),
          ],
        )
        : const SizedBox();
  }

  Widget _buildFunctionsAndExtensions(BuildContext context) {
    switch (configuration) {
      case ButtonRelayUniversalAddOnConfiguration():
        final config = configuration as ButtonRelayUniversalAddOnConfiguration;
        final MultipliedTime fallbackDelay = MultipliedTime.fromDuration(config.fallbackDelay);
        fallbackDelay.addListener(() => setState(() => configuration = config..fallbackDelay = fallbackDelay.duration));
        return _functions(
          config.function,
          fallbackDelay,
          (newFunction) => setState(() => configuration = config..function = newFunction),
        );
      case ButtonRelayOnAddOnConfiguration():
        final config = configuration as ButtonRelayOnAddOnConfiguration;
        final MultipliedTime fallbackDelay = MultipliedTime.fromDuration(config.fallbackDelay);
        fallbackDelay.addListener(() => setState(() => configuration = config..fallbackDelay = fallbackDelay.duration));
        return _functions(
          config.function,
          fallbackDelay,
          (newFunction) => setState(() => configuration = config..function = newFunction),
        );
      case ButtonShutterUniversalAddOnConfiguration():
        final config = configuration as ButtonShutterUniversalAddOnConfiguration;
        return Column(
          children: [
            _functions(
              config.function,
              MultipliedTime.fromDuration(Duration.zero),
              (newFunction) => setState(() => configuration = config..function = newFunction),
            ),
            if (config.function != ButtonChannelConfigurationFunction.gs4)
              const SizedBox()
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: EltakoPadding.xl),
                  Text(
                    AppLocalizations.of(context).generalTextSlatSetting,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: EltakoPadding.xs),
                  EltakoPicker(
                    tabs: ShutterTiltAutomaticType.values,
                    onTap:
                        (newMode) => setState(() {
                          final newTiltValue = config.tiltAutomatic.value;
                          configuration =
                              config..tiltAutomatic = ShutterTiltAutomatic(type: newMode, tiltValue: newTiltValue);
                        }),
                    initialSelection: config.tiltAutomatic.type,
                    labelBuilder: (mode) => mode.name,
                  ),
                  const SizedBox(height: EltakoPadding.s),
                  if (config.tiltAutomatic.type == ShutterTiltAutomaticType.slat)
                    EltakoHint(text: AppLocalizations.of(context).slatAutoSettingHint)
                  else if (config.tiltAutomatic.type != ShutterTiltAutomaticType.none)
                    EltakoHint(text: AppLocalizations.of(context).slatReversalSettingHint),
                  const SizedBox(height: EltakoPadding.s),
                  if (config.tiltAutomatic.type != ShutterTiltAutomaticType.none) ...[
                    const SizedBox(height: EltakoPadding.xl),
                    EltakoSlider.percentage(
                      value: config.tiltAutomatic.value.toDouble(),
                      onChangeEnd:
                          (value) => setState(() {
                            configuration =
                                config
                                  ..tiltAutomatic = ShutterTiltAutomatic(
                                    type: config.tiltAutomatic.type,
                                    tiltValue: value.round(),
                                  );
                          }),
                    ),
                  ],
                ],
              ),
          ],
        );
      case ButtonShutterDirectionalDownAddOnConfiguration():
        final config = configuration as ButtonShutterDirectionalDownAddOnConfiguration;
        return Column(
          children: [
            _functions(
              config.function,
              MultipliedTime.fromDuration(Duration.zero),
              (newFunction) => setState(() => configuration = config..function = newFunction),
            ),
            if (config.function != ButtonChannelConfigurationFunction.gs4)
              const SizedBox()
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: EltakoPadding.xl),
                  Text(
                    AppLocalizations.of(context).generalTextSlatSetting,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: EltakoPadding.xs),
                  EltakoPicker(
                    tabs: ShutterTiltAutomaticType.values,
                    onTap:
                        (newMode) => setState(() {
                          final newTiltValue = config.tiltAutomatic.value;
                          configuration =
                              config..tiltAutomatic = ShutterTiltAutomatic(type: newMode, tiltValue: newTiltValue);
                        }),
                    initialSelection: config.tiltAutomatic.type,
                    labelBuilder: (mode) => mode.name,
                  ),
                  const SizedBox(height: EltakoPadding.s),
                  if (config.tiltAutomatic.type == ShutterTiltAutomaticType.slat)
                    EltakoHint(text: AppLocalizations.of(context).slatAutoSettingHint)
                  else if (config.tiltAutomatic.type != ShutterTiltAutomaticType.none)
                    EltakoHint(text: AppLocalizations.of(context).slatReversalSettingHint),
                  const SizedBox(height: EltakoPadding.s),
                  if (config.tiltAutomatic.type != ShutterTiltAutomaticType.none) ...[
                    const SizedBox(height: EltakoPadding.xl),
                    EltakoSlider.percentage(
                      value: config.tiltAutomatic.value.toDouble(),
                      onChangeEnd:
                          (value) => setState(() {
                            configuration =
                                config
                                  ..tiltAutomatic = ShutterTiltAutomatic(
                                    type: config.tiltAutomatic.type,
                                    tiltValue: value.round(),
                                  );
                          }),
                    ),
                  ],
                ],
              ),
          ],
        );
      case ButtonShutterDirectionalUpAddOnConfiguration():
        final config = configuration as ButtonShutterDirectionalUpAddOnConfiguration;
        return Column(
          children: [
            _functions(
              config.function,
              MultipliedTime.fromDuration(Duration.zero),
              (newFunction) => setState(() => configuration = config..function = newFunction),
            ),
            if (config.function != ButtonChannelConfigurationFunction.gs4)
              const SizedBox()
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: EltakoPadding.xl),
                  Text(
                    AppLocalizations.of(context).generalTextSlatSetting,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: EltakoPadding.xs),
                  EltakoPicker(
                    tabs: ShutterTiltAutomaticType.values,
                    onTap:
                        (newMode) => setState(() {
                          final newTiltValue = config.tiltAutomatic.value;
                          configuration =
                              config..tiltAutomatic = ShutterTiltAutomatic(type: newMode, tiltValue: newTiltValue);
                        }),
                    initialSelection: config.tiltAutomatic.type,
                    labelBuilder: (mode) => mode.name,
                  ),
                  const SizedBox(height: EltakoPadding.s),
                  if (config.tiltAutomatic.type == ShutterTiltAutomaticType.slat)
                    EltakoHint(text: AppLocalizations.of(context).slatAutoSettingHint)
                  else if (config.tiltAutomatic.type != ShutterTiltAutomaticType.none)
                    EltakoHint(text: AppLocalizations.of(context).slatReversalSettingHint),
                  const SizedBox(height: EltakoPadding.s),
                  if (config.tiltAutomatic.type != ShutterTiltAutomaticType.none) ...[
                    const SizedBox(height: EltakoPadding.xl),
                    EltakoSlider.percentage(
                      value: config.tiltAutomatic.value.toDouble(),
                      onChangeEnd:
                          (value) => setState(() {
                            configuration =
                                config
                                  ..tiltAutomatic = ShutterTiltAutomatic(
                                    type: config.tiltAutomatic.type,
                                    tiltValue: value.round(),
                                  );
                          }),
                    ),
                  ],
                ],
              ),
          ],
        );
      case ButtonShutterSceneAddOnConfiguration():
        final config = configuration as ButtonShutterSceneAddOnConfiguration;
        return _shutterSceneConfiguration(config);
      case ButtonDimmerUniversalLogicConfiguration():
        final config = configuration as ButtonDimmerUniversalLogicConfiguration;
        return Column(
          children: [
            _dimDurationSlider(
              config.dimDuration.inSeconds,
              (value) => configuration = config..dimDuration = Duration(seconds: value),
            ),
            const SizedBox(height: EltakoPadding.xl),
            _turnOnDurationSlider(
              config.turnOnDuration.inSeconds,
              (value) => configuration = config..turnOnDuration = Duration(seconds: value),
            ),
            const SizedBox(height: EltakoPadding.xl),
            _turnOffDurationSlider(
              config.turnOffDuration.inSeconds,
              (value) => configuration = config..turnOffDuration = Duration(seconds: value),
            ),
            ..._divider,
            _childrenRoomExtension(
              config.childrenRoomExtension,
              (value) => configuration = config..childrenRoomExtension = value,
            ),
            ..._divider,
            _slumberExtension(config.slumberExtension, (value) => configuration = config..slumberExtension = value),
          ],
        );
      case ButtonDimmerDirectionalUpLogicConfiguration():
        final config = configuration as ButtonDimmerDirectionalUpLogicConfiguration;
        return Column(
          children: [
            _dimDurationSlider(
              config.dimDuration.inSeconds,
              (value) => configuration = config..dimDuration = Duration(seconds: value),
            ),
            const SizedBox(height: EltakoPadding.xl),
            _turnOnDurationSlider(
              config.turnOnDuration.inSeconds,
              (value) => configuration = config..turnOnDuration = Duration(seconds: value),
            ),
            ..._divider,
            _childrenRoomExtension(
              config.childrenRoomExtension,
              (value) => configuration = config..childrenRoomExtension = value,
            ),
            ..._divider,
            _lightAlarmExtension(
              config.lightAlarmExtension,
              (value) => configuration = config..lightAlarmExtension = value,
            ),
          ],
        );
      case ButtonDimmerDirectionalDownLogicConfiguration():
        final config = configuration as ButtonDimmerDirectionalDownLogicConfiguration;
        return Column(
          children: [
            _dimDurationSlider(
              config.dimDuration.inSeconds,
              (value) => configuration = config..dimDuration = Duration(seconds: value),
            ),
            const SizedBox(height: EltakoPadding.xl),
            _turnOffDurationSlider(
              config.turnOffDuration.inSeconds,
              (value) => configuration = config..turnOffDuration = Duration(seconds: value),
            ),
            ..._divider,
            _slumberExtension(config.slumberExtension, (value) => configuration = config..slumberExtension = value),
          ],
        );
      case ButtonDimmerCentralUpLogicConfiguration():
        final config = configuration as ButtonDimmerCentralUpLogicConfiguration;
        return _turnOnDurationSlider(
          config.turnOnDuration.inSeconds,
          (value) => configuration = config..turnOnDuration = Duration(seconds: value),
        );
      case ButtonDimmerCentralDownLogicConfiguration():
        final config = configuration as ButtonDimmerCentralDownLogicConfiguration;
        return _turnOffDurationSlider(
          config.turnOffDuration.inSeconds,
          (value) => configuration = config..turnOffDuration = Duration(seconds: value),
        );
      case ButtonDimmerSceneLogicConfiguration():
        final config = configuration as ButtonDimmerSceneLogicConfiguration;
        return Column(
          children: [
            _dimDurationSlider(
              config.dimDuration.inSeconds,
              (value) => configuration = config..dimDuration = Duration(seconds: value),
            ),
            const SizedBox(height: EltakoPadding.xl),
            _turnOnDurationSlider(
              config.turnOnDuration.inSeconds,
              (value) => configuration = config..turnOnDuration = Duration(seconds: value),
            ),
            const SizedBox(height: EltakoPadding.xl),
            _turnOffDurationSlider(
              config.turnOffDuration.inSeconds,
              (value) => configuration = config..turnOffDuration = Duration(seconds: value),
            ),
            ..._divider,
            EltakoToggle(
              label: AppLocalizations.of(context).buttonSceneValueOverride,
              value: config.overrideExtension.isEnabled,
              onChanged: (value) => configuration = config..overrideExtension.isEnabled = value,
            ),
            EltakoHint(text: AppLocalizations.of(context).buttonSceneValueOverrideDescription),
            ..._divider,
            EltakoSlider.percentage(
              headerText: AppLocalizations.of(context).timerDetailsDimtoval,
              footerText: AppLocalizations.of(context).timerDetailsDimtovalDescription,
              value: config.targetBrightness.toDouble(),
              min: 1,
              onChangeEnd: (value) => configuration = config..targetBrightness = value.round(),
            ),
          ],
        );
      default:
        return const SizedBox();
    }
  }

  EltakoSlider _dimDurationSlider(int initialValue, Function(int value) onChange) => EltakoSlider.speed(
    value: initialValue.invertToDouble(min: 1, max: 10),
    min: 1,
    max: 10,
    onChangeEnd: (value) => onChange(value.invertToInt(min: 1, max: 10)),
    headerText: AppLocalizations.of(context).eud12TextDimspeed,
    footerText: AppLocalizations.of(context).eud12DescriptionDimspeed,
  );

  EltakoSlider _turnOnDurationSlider(int initialValue, Function(int value) onChange) => EltakoSlider.speed(
    value: initialValue.invertToDouble(min: 1, max: 10),
    min: 1,
    max: 10,
    onChangeEnd: (value) => onChange(value.invertToInt(min: 1, max: 10)),
    headerText: AppLocalizations.of(context).eud12TextSwitchonspeed,
    footerText: AppLocalizations.of(context).eud12DescriptionSwitchonspeed,
  );

  EltakoSlider _turnOffDurationSlider(int initialValue, Function(int value) onChange) => EltakoSlider.speed(
    value: initialValue.invertToDouble(min: 1, max: 10),
    min: 1,
    max: 10,
    onChangeEnd: (value) => onChange(value.invertToInt(min: 1, max: 10)),
    headerText: AppLocalizations.of(context).eud12TextSwitchoffspeed,
    footerText: AppLocalizations.of(context).eud12DescriptionSwitchoffspeed,
  );

  Widget _functions(
    ButtonChannelConfigurationFunction function,
    MultipliedTime fallbackDelay,
    Function(ButtonChannelConfigurationFunction function) onChange,
  ) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(AppLocalizations.of(context).detailsFunctionsHeader, style: Theme.of(context).textTheme.titleMedium),
      const SizedBox(height: EltakoPadding.xs),
      EltakoPicker(
        key: ValueKey(configuration),
        style: EltakoPickerStyle.dialog,
        tabs: _supportedFunctions,
        onTap: (newFunction) => onChange(newFunction),
        initialSelection: function,
        icon: EltakoIcon.getIcon(Assets.icons.mfz.circle, context).image(height: 40, width: 40),
        dialogTitle: AppLocalizations.of(context).detailsFunctionsHeader,
        labelWidgetBuilder:
            (function) => Text(
              function.localizedName,
              style: Theme.of(context).textTheme.titleMedium,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
        subtitleWidgetBuilder:
            (function) => Text(function.localizedDescription, maxLines: 1, overflow: TextOverflow.ellipsis),
        child: EltakoListTile(
          leading: EltakoIcon.getIcon(Assets.icons.mfz.circle, context).image(height: 40, width: 40),
          title: function.localizedName,
        ),
      ),
      const SizedBox(height: EltakoPadding.xs),
      Text(function.localizedDescription),
      const SizedBox(height: EltakoPadding.xs),
      if (function.hasFallbackDelay) ...{
        MultipliedTimeEdit(
          title: AppLocalizations.of(context).generalTextOffdelay,
          multipliedTime: fallbackDelay,
          timeMultipliers: const [TimeMultiplier.seconds, TimeMultiplier.minutes, TimeMultiplier.hours],
          getMinimum: (_) => 1,
          factorWithValue: false,
          hint:
              function == ButtonChannelConfigurationFunction.esv
                  ?
                  // ignore: avoid_string_literals_inside_widget
                  '+${AppLocalizations.of(context).secondsWithValue(30)} ${AppLocalizations.of(context).generalTextSwitchoffprewarning}'
                  : null,
        ),
        const SizedBox(height: EltakoPadding.xs),
        Text(AppLocalizations.of(context).generalDescriptionOffdelay, style: Theme.of(context).textTheme.labelSmall),
      },
    ],
  );

  Widget _childrenRoomExtension(
    ChildrenRoomLogicConfigurationExtension childrenRoom,
    Function(ChildrenRoomLogicConfigurationExtension value) onChange,
  ) => Column(
    children: [
      EltakoToggle(
        key: ValueKey(childrenRoom),
        label: AppLocalizations.of(context).generalTextKidsRoom,
        value: childrenRoom.isEnabled,
        onChanged: (value) => onChange(childrenRoom..isEnabled = value),
      ),
      const SizedBox(height: EltakoPadding.xs),
      AnimatedCrossFade(
        firstChild: Column(
          children: [
            EltakoSlider.time(
              headerText: AppLocalizations.of(context).detailsConfigurationRuntime,
              value: childrenRoom.dimDuration.inSeconds.toDouble(),
              onChangeEnd: (value) => onChange(childrenRoom..dimDuration = Duration(seconds: value.round())),
              unit: EltakoSliderUnit.seconds,
              min: 1,
              max: 10,
              footerText: AppLocalizations.of(
                context,
              ).generalDescriptionKidsRoom(AppLocalizations.of(context).triggerOn),
            ),
            const SizedBox(height: EltakoPadding.s),
            EltakoSlider.percentage(
              headerText: AppLocalizations.of(context).detailsConfigurationMaximum,
              value: childrenRoom.targetBrightness.roundToDouble(),
              min: 1,
              onChangeEnd: (value) => onChange(childrenRoom..targetBrightness = value.round()),
            ),
          ],
        ),
        secondChild: const SizedBox(),
        crossFadeState: childrenRoom.isEnabled ? CrossFadeState.showFirst : CrossFadeState.showSecond,
        duration: const Duration(milliseconds: 300),
      ),
    ],
  );

  Widget _slumberExtension(
    SlumberLogicConfigurationExtension slumber,
    Function(SlumberLogicConfigurationExtension value) onChange,
  ) => Column(
    children: [
      EltakoToggle(
        key: ValueKey(slumber),
        label: AppLocalizations.of(context).generalTextSnoozeclock,
        value: slumber.isEnabled,
        onChanged: (value) => onChange(slumber..isEnabled = value),
      ),
      const SizedBox(height: EltakoPadding.xs),
      AnimatedCrossFade(
        firstChild: EltakoSlider.time(
          headerText: AppLocalizations.of(context).detailsConfigurationRuntime,
          value: slumber.dimDuration.inMinutes.toDouble(),
          onChangeEnd: (value) => onChange(slumber..dimDuration = Duration(minutes: value.round())),
          min: 1,
          max: 60,
          footerText: AppLocalizations.of(
            context,
          ).generalDescriptionSnoozeclock(AppLocalizations.of(context).triggerOff),
        ),
        secondChild: const SizedBox(),
        crossFadeState: slumber.isEnabled ? CrossFadeState.showFirst : CrossFadeState.showSecond,
        duration: const Duration(milliseconds: 300),
      ),
    ],
  );

  Widget _lightAlarmExtension(
    LightAlarmLogicConfigurationExtension lightAlarm,
    Function(LightAlarmLogicConfigurationExtension value) onChange,
  ) => Column(
    children: [
      EltakoToggle(
        key: ValueKey(lightAlarm),
        label: AppLocalizations.of(context).generalTextLightclock,
        value: lightAlarm.isEnabled,
        onChanged: (value) => onChange(lightAlarm..isEnabled = value),
      ),
      const SizedBox(height: EltakoPadding.xs),
      AnimatedCrossFade(
        firstChild: EltakoSlider.time(
          headerText: AppLocalizations.of(context).detailsConfigurationRuntime,
          value: lightAlarm.dimDuration.inMinutes.toDouble(),
          onChangeEnd: (value) => onChange(lightAlarm..dimDuration = Duration(minutes: value.round())),
          min: 1,
          max: 60,
          footerText: AppLocalizations.of(
            context,
          ).generalDescriptionLightclock(AppLocalizations.of(context).doubleImpuls),
        ),
        secondChild: const SizedBox(),
        crossFadeState: lightAlarm.isEnabled ? CrossFadeState.showFirst : CrossFadeState.showSecond,
        duration: const Duration(milliseconds: 300),
      ),
    ],
  );

  Widget _shutterSceneConfiguration(ButtonShutterSceneAddOnConfiguration config) => Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      EltakoToggle(
        label: AppLocalizations.of(context).buttonSceneValueOverride,
        value: config.sceneValueOverride,
        onChanged: (value) => setState(() => configuration = config..sceneValueOverride = value),
        description: AppLocalizations.of(context).sceneValueOverride,
      ),
      ..._divider,
      EltakoToggle(
        label: AppLocalizations.of(context).referenceRun,
        value: config.sceneReferenceMovement,
        onChanged: (value) => setState(() => configuration = config..sceneReferenceMovement = value),
        description: AppLocalizations.of(context).sceneCalibration,
      ),
      ..._divider,
      EltakoSlider.percentage(
        headerText: AppLocalizations.of(context).generalTextPosition,
        footerText: AppLocalizations.of(context).scenePositionSliderDescription,
        value: config.targetValue.toDouble(),
        onChangeEnd: (value) => setState(() => configuration = config..targetValue = value.round()),
      ),

      const SizedBox(height: EltakoPadding.xxl),

      EltakoSlider.percentage(
        headerText: AppLocalizations.of(context).generalTextSlatPosition,
        footerText: AppLocalizations.of(context).sceneSlatPositionSliderDescription,
        value: config.targetAngle.toDouble(),
        onChangeEnd: (value) => setState(() => configuration = config..targetAngle = value.round()),
      ),
    ],
  );
}
