// ignore_for_file: avoid_string_literals_inside_widget
//
//  barcode_scanner.dart
//  EltakoConnect
//
//  Created by <PERSON> on 25.03.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'dart:async';

import 'package:eltako_connect/enumeration/authorization_code_type.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/gen/colors.gen.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/manager/permission_manager.dart';
import 'package:eltako_connect/theme/theme.dart';
import 'package:eltako_connect/widget/eltako_widgets/eltako_button.dart';
import 'package:eltako_log/eltako_log.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:native_device_orientation/native_device_orientation.dart';

/// Code Scanner
class BarcodeScanner extends StatefulWidget {
  /// Default constructor
  BarcodeScanner({required this.onBarcodeDetected, MobileScannerController? controller, super.key})
    : _cameraController = controller ?? MobileScannerController(detectionSpeed: DetectionSpeed.noDuplicates);

  /// On barcode detected callback
  final Function(AuthorizationCodeType type, String barcode) onBarcodeDetected;

  final MobileScannerController _cameraController;

  @override
  State<BarcodeScanner> createState() => _BarcodeScannerState();
}

class _BarcodeScannerState extends State<BarcodeScanner> {
  int turns = 0;
  Orientation oldOrientation = Orientation.portrait;

  Future<void> _updateOrientation() async {
    final newOrientation = await NativeDeviceOrientationCommunicator().orientation();
    setState(
      () =>
          turns = switch (newOrientation) {
            NativeDeviceOrientation.portraitUp => 0,
            NativeDeviceOrientation.landscapeRight => 1,
            NativeDeviceOrientation.portraitDown => 2,
            NativeDeviceOrientation.landscapeLeft => 3,
            _ => 0,
          },
    );
  }

  int _getControlTurns() => switch (turns) {
    1 => 3,
    3 => 1,
    _ => turns,
  };

  @override
  Widget build(BuildContext context) {
    final orientation = MediaQuery.of(context).orientation;
    if (orientation != oldOrientation) {
      oldOrientation = orientation;
      unawaited(_updateOrientation());
    }
    return _buildScanner(context, turns, _getControlTurns());
  }

  Widget _buildScanner(BuildContext context, int cameraTurns, int controlTurns) => ClipRRect(
    borderRadius: BorderRadius.circular(EltakoRadius.xl),
    child: AspectRatio(
      aspectRatio: 16 / 9,
      child: RotatedBox(
        quarterTurns: cameraTurns,
        child: MobileScanner(
          placeholderBuilder: (context, child) => const Center(child: CircularProgressIndicator()),
          errorBuilder: (context, error, stackTrace) => _buildError(error, context, controlTurns),
          overlay: _buildTorchLight(context, controlTurns),
          controller: widget._cameraController,
          onDetect: (barcode) {
            info('Barcode detected: $barcode');
            unawaited(HapticFeedback.selectionClick());
            if (barcode.barcodes.isEmpty) {
              warning('Failed to scan barcode', category: LogCategory.camera);
              return;
            }
            for (final code in barcode.barcodes) {
              // Get raw value and type
              final rawValue = code.rawValue ?? '';
              final codeType = AuthorizationCodeType.fromBarcode(rawValue);

              info('Found barcode (${codeType.name}): $rawValue', category: LogCategory.authorization);
              widget.onBarcodeDetected(codeType, rawValue);
            }
          },
        ),
      ),
    ),
  );

  Widget _buildError(MobileScannerException error, BuildContext context, int controlTurns) => Center(
    child: RotatedBox(
      quarterTurns: controlTurns,
      child: switch (error.errorCode) {
        MobileScannerErrorCode.permissionDenied => Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Assets.images.permissions.noCameraPermission.image(width: 100, color: EltakoStyle.of(context).primaryBlue),
            const SizedBox(height: EltakoPadding.xs),
            Text(AppLocalizations.of(context).errorCameraPermission, textAlign: TextAlign.center),
            const SizedBox(height: EltakoPadding.xl),
            EltakoButton(
              eltakoButtonStyle: EltakoButtonStyle.filled,
              label: Text(AppLocalizations.of(context).openBluetoothSettings),
              onPressed: () async => PermissionManager().showAppSettings(),
            ),
          ],
        ),
        _ => Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.warning_rounded, color: ColorName.professionalSmartHome),
            const SizedBox(height: EltakoPadding.xs),
            Text(
              kDebugMode
                  ? 'Error: ${error.errorDetails?.message}'
                  : AppLocalizations.of(context).deviceNameChangedFailed,
            ),
            EltakoButton(
              onPressed: () async {
                unawaited(HapticFeedback.selectionClick());
                await widget._cameraController.stop();
                await widget._cameraController.start();
              },
              label: Text(AppLocalizations.of(context).generalTextRetry),
            ),
          ],
        ),
      },
    ),
  );

  Widget _buildTorchLight(BuildContext context, int controlTurns) => Align(
    alignment: switch (controlTurns) {
      0 => Alignment.bottomRight,
      1 => Alignment.bottomLeft,
      2 => Alignment.topLeft,
      3 => Alignment.topRight,
      _ => Alignment.bottomRight,
    },
    child: IconButton(
      icon: ValueListenableBuilder(
        valueListenable: widget._cameraController.torchState,
        builder:
            (context, torchState, child) => RotatedBox(
              quarterTurns: controlTurns,
              child: Icon(
                Icons.flashlight_on_rounded,
                color: torchState == TorchState.on ? Colors.amber : Colors.white,
                shadows: const [BoxShadow(color: Colors.black26, blurRadius: 2, spreadRadius: 3)],
              ),
            ),
      ),
      onPressed: () async {
        unawaited(HapticFeedback.selectionClick());
        await widget._cameraController.toggleTorch();
      },
    ),
  );
}
