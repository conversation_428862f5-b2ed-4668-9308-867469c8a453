import 'package:eltako_connect/theme/theme.dart';
import 'package:flutter/material.dart';

/// EltakoButtonStyle
enum EltakoButtonSize {
  /// Small size
  small,

  /// Medium size
  medium,

  /// Large size
  large,
}

/// EltakoButtonStyle
enum EltakoButtonStyle {
  /// Border style
  border,

  /// Text style
  text,

  /// Transparent style
  transparent,

  /// Filled style
  filled,
}

/// EltakoButton
class EltakoButton extends StatelessWidget {
  // region [PUBLIC PROPERTIES]

  /// Label
  final Widget? label;

  /// Icon
  final Widget? icon;

  /// Color
  final Color? color;

  /// Button style
  final EltakoButtonStyle eltakoButtonStyle;

  /// Button size
  final EltakoButtonSize size;

  /// On pressed
  final VoidCallback? onPressed;

  /// Expand
  final bool expand;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  const EltakoButton({
    super.key,
    this.label,
    this.icon,
    this.onPressed,
    this.eltakoButtonStyle = EltakoButtonStyle.text,
    this.size = EltakoButtonSize.medium,
    this.expand = false,
    this.color,
  });

  // endregion

  // region [PRIVATE FUNCTIONS]

  TextStyle? _getTextStyle(BuildContext context) {
    switch (size) {
      case EltakoButtonSize.small:
        return Theme.of(context).textTheme.bodySmall;
      case EltakoButtonSize.medium:
        return Theme.of(context).textTheme.bodyMedium;
      case EltakoButtonSize.large:
        return Theme.of(context).textTheme.titleLarge;
    }
  }

  ButtonStyle _getButtonStyle(BuildContext context) {
    switch (eltakoButtonStyle) {
      case EltakoButtonStyle.text:
        return TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: EltakoPadding.xs, vertical: EltakoPadding.xs),
          foregroundColor: color ?? EltakoStyle.of(context).primaryButton,
          textStyle: _getTextStyle(context),
        );
      case EltakoButtonStyle.border:
        return OutlinedButton.styleFrom(
          backgroundColor: Colors.transparent,
          foregroundColor: color ?? EltakoStyle.of(context).primaryButton,
          textStyle: _getTextStyle(context),
          padding: const EdgeInsets.symmetric(horizontal: EltakoPadding.xs, vertical: EltakoPadding.xs),
          side: BorderSide(color: color ?? EltakoStyle.of(context).primaryButton),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(EltakoRadius.xs)),
        );
      case EltakoButtonStyle.filled:
        return ElevatedButton.styleFrom(
          backgroundColor: color ?? EltakoStyle.of(context).primaryButton,
          foregroundColor: EltakoStyle.of(context).primaryTextSwitched,
          textStyle: _getTextStyle(context),
          padding: const EdgeInsets.symmetric(horizontal: EltakoPadding.xs, vertical: EltakoPadding.xs),
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(EltakoRadius.xs)),
        );
      case EltakoButtonStyle.transparent:
        return ElevatedButton.styleFrom(
          backgroundColor: EltakoStyle.of(context).primaryElementBackground,
          foregroundColor: EltakoStyle.of(context).primaryText,
          textStyle: _getTextStyle(context),
          padding: const EdgeInsets.symmetric(horizontal: EltakoPadding.xs, vertical: EltakoPadding.xs),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(EltakoRadius.xs)),
        );
    }
  }

  // endregion

  // region [OVERRIDES]

  @override
  Widget build(BuildContext context) {
    switch (eltakoButtonStyle) {
      case EltakoButtonStyle.text:
        return TextButton(
          onPressed: onPressed,
          style: _getButtonStyle(context),
          child: _EltakoButtonChild(label: label, icon: icon, expand: expand),
        );
      case EltakoButtonStyle.border:
        return OutlinedButton(
          onPressed: onPressed,
          style: _getButtonStyle(context),
          child: _EltakoButtonChild(expand: expand, label: label, icon: icon),
        );
      case EltakoButtonStyle.filled:
        return ElevatedButton(
          onPressed: onPressed,
          style: _getButtonStyle(context),
          child: _EltakoButtonChild(expand: expand, label: label, icon: icon),
        );
      case EltakoButtonStyle.transparent:
        return TextButton(
          onPressed: onPressed,
          style: _getButtonStyle(context),
          child: _EltakoButtonChild(expand: expand, label: label, icon: icon),
        );
    }
  }

  // endregion
}

class _EltakoButtonChild extends StatelessWidget {
  // region [PUBLIC FUNCTIONS]

  const _EltakoButtonChild({required this.label, required this.icon, required this.expand});

  // endregion

  // region [PUBLIC PROPERTIES]

  final Widget? label;
  final Widget? icon;
  final bool expand;

  // endregion

  // region [OVERRIDES]

  @override
  Widget build(BuildContext context) => Row(
    mainAxisSize: expand ? MainAxisSize.max : MainAxisSize.min,
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      if (icon != null) ...{icon!},
      if (label != null && icon != null) ...{const SizedBox(width: EltakoPadding.xxxs)},
      if (label != null) ...{label!},
    ],
  );
  // endregion
}
