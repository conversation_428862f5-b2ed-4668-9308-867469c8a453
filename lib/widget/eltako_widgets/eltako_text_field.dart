//
//  eltako_text_field.dart
//  EltakoConnect
//
//  Created by <PERSON> on 08.01.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/l10n/app_localizations_extension.dart';
import 'package:eltako_connect/theme/eltako_padding.dart';
import 'package:eltako_connect/theme/eltako_radius.dart';
import 'package:eltako_connect/theme/styles/eltako_style.dart';
import 'package:eltako_connect/widget/validator/eltako_validator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Eltako Text Field
class EltakoTextField extends StatefulWidget {
  // region [PUBLIC PROPERTIES]

  /// TextEditingController
  final TextEditingController? controller;

  /// On changed
  final ValueChanged<String>? onChanged;

  /// Hint Text
  final String? hintText;

  /// Header Text
  final String? headerText;

  /// Footer Text
  final String? footerText;

  /// Enabled
  final bool enabled;

  /// Read only
  final bool readOnly;

  /// Leading Icon
  final Widget? leadingIcon;

  /// Trailing Icon
  final Widget? trailingIcon;

  /// Show clear button
  final bool showClearButton;

  /// Keyboard type
  final TextInputType keyboardType;

  /// Obscure text
  final bool obscureText;

  /// Is PIN or Password
  final bool isPinOrPassword;

  /// Focus node
  final FocusNode? focusNode;

  /// Character limit
  final int? characterLimit;

  /// Show character limit
  final bool showCharacterLimit;

  /// Error text
  final String? errorText;

  /// Allowed chars regex
  final String? allowedCharsRegex;

  /// Validator
  final FormFieldValidator<String?>? validator;

  // endregion

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  const EltakoTextField({
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.showClearButton = false,
    this.leadingIcon,
    this.trailingIcon,
    this.keyboardType = TextInputType.text,
    this.obscureText = false,
    this.focusNode,
    int? characterLimit,
    this.errorText,
    this.allowedCharsRegex,
    this.showCharacterLimit = true,
    this.validator,
    super.key,
  }) : isPinOrPassword = false,
       characterLimit =
           characterLimit != null
               ? characterLimit > 0
                   ? characterLimit
                   : null
               : null;

  /// Default constructor
  const EltakoTextField.password({
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.showClearButton = false,
    this.leadingIcon,
    this.trailingIcon,
    this.keyboardType = TextInputType.visiblePassword,
    this.obscureText = false,
    this.focusNode,
    int? characterLimit,
    this.errorText,
    this.allowedCharsRegex,
    this.showCharacterLimit = true,
    this.validator,
    super.key,
  }) : isPinOrPassword = true,
       characterLimit =
           characterLimit != null
               ? characterLimit > 0
                   ? characterLimit
                   : null
               : null;

  /// BLE Pin Text Field
  const EltakoTextField.blePin({
    this.errorText,
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.focusNode,
    int? characterLimit,
    this.allowedCharsRegex = EltakoValidator.allowedBlePinChars,
    this.showCharacterLimit = true,
    this.validator,
    super.key,
  }) : isPinOrPassword = true,
       showClearButton = false,
       leadingIcon = null,
       trailingIcon = null,
       keyboardType = TextInputType.number,
       characterLimit =
           characterLimit != null
               ? characterLimit > 0
                   ? characterLimit
                   : null
               : null,
       obscureText = true;

  /// IP Text Field
  const EltakoTextField.ip({
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.keyboardType = TextInputType.text,
    this.focusNode,
    this.showCharacterLimit = true,
    this.errorText,
    super.key,
  }) : isPinOrPassword = false,
       showClearButton = false,
       leadingIcon = null,
       trailingIcon = null,
       characterLimit = 0,
       allowedCharsRegex = EltakoValidator.allowedIPChars,
       validator = EltakoValidator.ipValidator,
       obscureText = false;

  /// DNS Text Field
  const EltakoTextField.dns({
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.keyboardType = TextInputType.text,
    this.focusNode,
    this.showCharacterLimit = true,
    this.errorText,
    super.key,
  }) : isPinOrPassword = false,
       showClearButton = false,
       leadingIcon = null,
       trailingIcon = null,
       characterLimit = 0,
       allowedCharsRegex = EltakoValidator.allowedIPChars,
       validator = EltakoValidator.dnsValidator,
       obscureText = false;

  /// Subnet Text Field
  const EltakoTextField.subnet({
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.keyboardType = TextInputType.text,
    this.focusNode,
    this.showCharacterLimit = true,
    this.errorText,
    super.key,
  }) : isPinOrPassword = false,
       showClearButton = false,
       leadingIcon = null,
       trailingIcon = null,
       characterLimit = 0,
       allowedCharsRegex = EltakoValidator.allowedIPChars,
       validator = EltakoValidator.subnetMaskValidator,
       obscureText = false;

  /// URI Text Field
  const EltakoTextField.uri({
    this.controller,
    this.onChanged,
    this.hintText,
    this.headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    this.keyboardType = TextInputType.url,
    this.focusNode,
    this.errorText,
    this.allowedCharsRegex,
    this.showCharacterLimit = true,
    super.key,
  }) : isPinOrPassword = false,
       showClearButton = false,
       leadingIcon = null,
       trailingIcon = null,
       characterLimit = 0,
       validator = EltakoValidator.uriValidator,
       obscureText = false;

  /// Port Text Field
  EltakoTextField.port({
    this.controller,
    this.onChanged,
    String? hintText,
    String? headerText,
    this.footerText,
    this.enabled = true,
    this.readOnly = false,
    bool? showClearButton,
    this.keyboardType = TextInputType.number,
    this.focusNode,
    this.errorText,
    this.allowedCharsRegex = EltakoValidator.allowedPortChars,
    int? characterLimit,
    bool? showCharacterLimit,
    this.validator = EltakoValidator.portValidator,
    super.key,
  }) : headerText = headerText ?? l10n.mqttPort,
       // ignore: avoid_string_literals_inside_widget
       hintText = hintText ?? l10n.forExample('502'),
       isPinOrPassword = false,
       showClearButton = showClearButton ?? true,
       leadingIcon = null,
       trailingIcon = null,
       characterLimit = characterLimit ?? 5,
       showCharacterLimit = showCharacterLimit ?? false,
       obscureText = false;

  // endregion

  // region [OVERRIDES]

  @override
  State<EltakoTextField> createState() => _EltakoTextFieldState();

  // endregion
}

class _EltakoTextFieldState extends State<EltakoTextField> {
  // region [PRIVATE PROPERTIES]

  late FocusNode _focusNode;
  bool _obscureText = false;
  late TextEditingController _controller;

  // endregion

  // region [GETTERS]

  Widget get showPasswordIcon => IconButton(
    onPressed: () {
      if (!widget.obscureText) return;
      setState(() => _obscureText = !_obscureText);
    },
    icon: _obscureText ? const Icon(Icons.remove_red_eye_outlined) : const Icon(Icons.remove_red_eye),
  );

  Widget get showClearButton => IconButton(
    onPressed:
        () => setState(() {
          _controller.clear();
          widget.onChanged?.call(StringExtension.empty);
          // widget.controller?.clear();
        }),
    icon: const Icon(Icons.cancel, color: Colors.grey),
  );

  // endregion

  // region [OVERRIDES]

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    if (widget.focusNode != null) {
      _focusNode = widget.focusNode!;
    } else {
      _focusNode = FocusNode();
    }
    _focusNode.addListener(
      () => setState(() {
        if (widget.obscureText) _obscureText = true;
      }),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => Column(
    mainAxisSize: MainAxisSize.min,
    children: [
      if (widget.headerText != null)
        Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(bottom: EltakoPadding.xs),
            child: Text(
              widget.headerText!,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: widget.enabled ? EltakoStyle.of(context).primaryText : EltakoStyle.of(context).iconsDisabled,
              ),
            ),
          ),
        ),
      TextFormField(
        enableSuggestions: !widget.isPinOrPassword,
        autocorrect: !widget.isPinOrPassword,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        autovalidateMode: AutovalidateMode.always,
        validator: widget.validator,
        inputFormatters:
            widget.allowedCharsRegex != null
                ? [FilteringTextInputFormatter.allow(RegExp(widget.allowedCharsRegex!))]
                : null,
        maxLength: widget.characterLimit != null ? (widget.characterLimit! > 0 ? widget.characterLimit : null) : null,
        focusNode: _focusNode,
        onTapOutside: (event) => FocusManager.instance.primaryFocus?.unfocus(),
        keyboardType: widget.keyboardType,
        obscureText: _obscureText,
        controller: _controller,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: widget.enabled ? EltakoStyle.of(context).secondaryText : EltakoStyle.of(context).iconsDisabled,
        ),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: EltakoStyle.of(context).secondaryText.withValues(alpha: 0.5)),
          contentPadding: const EdgeInsets.symmetric(vertical: EltakoPadding.s, horizontal: EltakoPadding.xs),
          border: OutlineInputBorder(borderSide: BorderSide.none, borderRadius: BorderRadius.circular(EltakoRadius.xs)),
          counter: widget.showCharacterLimit ? null : const SizedBox.shrink(),
          filled: true,
          fillColor:
              widget.enabled
                  ? EltakoStyle.of(context).primaryElementBackground
                  : EltakoStyle.of(context).backgroundDisabled,
          prefixIconColor: EltakoStyle.of(context).secondaryText,
          prefixIcon: widget.leadingIcon,
          suffixIconColor: EltakoStyle.of(context).secondaryText,
          suffixIcon:
              widget.obscureText
                  ? showPasswordIcon
                  : widget.showClearButton
                  ? _controller.value.text.isNotEmpty
                      ? showClearButton
                      : null
                  : widget.trailingIcon,
        ),
        onChanged: (value) => widget.onChanged?.call(value),
      ),
      if (widget.footerText != null && widget.errorText == null)
        Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(top: EltakoPadding.xs),
            child: Text(
              widget.footerText!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: EltakoStyle.of(context).secondaryText),
            ),
          ),
        ),
      if (widget.errorText != null)
        Align(
          alignment: Alignment.centerLeft,
          child: Padding(
            padding: const EdgeInsets.only(top: EltakoPadding.xs),
            child: Text(
              widget.errorText!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: EltakoStyle.of(context).error),
            ),
          ),
        ),
    ],
  );
}
