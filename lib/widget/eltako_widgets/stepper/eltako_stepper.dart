//
//  eltako_stepper.dart
//  EltakoConnect
//
//  Created by <PERSON> on 09.04.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:collection/collection.dart';
import 'package:eltako_connect/gen/assets.gen.dart';
import 'package:eltako_connect/theme/eltako_padding.dart';
import 'package:eltako_connect/theme/helper/eltako_icon.dart';
import 'package:eltako_connect/theme/styles/eltako_style.dart';
import 'package:eltako_connect/widget/eltako_widgets/stepper/eltako_step.dart';
import 'package:flutter/material.dart';

/// A stepper widget, that takes steps and navigates through them
class EltakoStepper extends StatelessWidget {
  /// Default constructor
  const EltakoStepper({
    required this.steps,
    required this.currentStep,
    this.eventFailed = false,
    this.showVerticalStepper = true,
    this.scrollController,
    super.key,
  });

  /// Scroll controller
  final ScrollController? scrollController;

  /// Steps
  final List<EltakoStep> steps;

  /// Event failed
  final bool eventFailed;

  /// Current step
  final int currentStep;

  /// Show first step
  final bool showVerticalStepper;

  double get _iconSize => 24;

  double get _strokeWidth => 2;

  double get _opacity => 0.3;

  @override
  Widget build(BuildContext context) => Column(
    children: [
      Row(
        children:
            steps
                .whereNot((step) => step == steps.last)
                .map(
                  (step) => Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Opacity(
                            opacity: currentStep >= steps.indexOf(step) || eventFailed ? 1 : _opacity,
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: _strokeWidth),
                              child: ColoredBox(
                                color: step == steps.first ? Colors.transparent : EltakoStyle.of(context).primaryBlue,
                              ),
                            ),
                          ),
                        ),
                        Opacity(
                          opacity: currentStep >= steps.indexOf(step) || eventFailed ? 1 : _opacity,
                          child: _stepIcon(step, context, showLoading: false),
                        ),
                        Expanded(
                          child: Opacity(
                            opacity: currentStep > steps.indexOf(step) || eventFailed ? 1 : _opacity,
                            child: ConstrainedBox(
                              constraints: BoxConstraints(minHeight: _strokeWidth),
                              child: ColoredBox(
                                color:
                                    step == steps.whereNot((step) => step == steps.last).last
                                        ? Colors.transparent
                                        : EltakoStyle.of(context).primaryBlue,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
      ),
      const SizedBox(height: EltakoPadding.l),
      if (showVerticalStepper)
        ...steps
            .whereNot((step) => step == steps.last)
            .map(
              (step) => Opacity(
                opacity: currentStep >= steps.indexOf(step) || eventFailed ? 1 : _opacity,
                child: _stepIcon(
                  step,
                  context,
                  showLabel: true,
                  showIndex: false,
                  color: EltakoStyle.of(context).success,
                ),
              ),
            ),
      const SizedBox(height: EltakoPadding.l),
      steps[currentStep].content,
    ],
  );

  Widget _stepIcon(
    EltakoStep step,
    BuildContext context, {
    bool showLabel = false,
    bool showIndex = true,
    bool showLoading = true,
    Color? color,
  }) => Padding(
    padding: const EdgeInsets.symmetric(vertical: EltakoPadding.xxxs),
    child: Row(
      children: [
        ConstrainedBox(
          constraints: const BoxConstraints(minWidth: 50),
          child: Center(
            child:
                currentStep == steps.indexOf(step)
                    ? Stack(
                      alignment: Alignment.center,
                      children: [
                        if (showLoading && !eventFailed)
                          SizedBox(
                            height: _iconSize,
                            width: _iconSize,
                            child: CircularProgressIndicator(strokeWidth: _strokeWidth),
                          ),
                        if (eventFailed)
                          _inActiveStep(context, step, color: EltakoStyle.of(context).error, active: true)
                        else
                          _inActiveStep(context, step, color: showLoading ? Colors.transparent : null),
                      ],
                    )
                    : steps.indexOf(step) < currentStep
                    ? showIndex
                        ? _inActiveStep(context, step, active: true)
                        : EltakoIcon.getIcon(
                          Assets.icons.success.transparent,
                          context,
                        ).image(width: _iconSize, color: color ?? EltakoStyle.of(context).primaryBlue)
                    : eventFailed
                    ? EltakoIcon.getIcon(
                      Assets.icons.error.transparent,
                      context,
                    ).image(width: _iconSize, color: EltakoStyle.of(context).error)
                    : _inActiveStep(context, step),
          ),
        ),
        if (showLabel) Text(step.title, style: Theme.of(context).textTheme.titleMedium),
      ],
    ),
  );

  Widget _inActiveStep(BuildContext context, EltakoStep step, {Color? color, bool active = false}) => DecoratedBox(
    decoration: BoxDecoration(
      shape: BoxShape.circle,
      color: active ? color ?? EltakoStyle.of(context).primaryBlue : Colors.transparent,
      border: Border.all(width: _strokeWidth, color: color ?? EltakoStyle.of(context).primaryBlue),
    ),
    child: Padding(
      padding: const EdgeInsets.all(EltakoPadding.xxs),
      child: ConstrainedBox(
        constraints: const BoxConstraints(minWidth: 8, minHeight: 8),
        child: Center(
          child: Text(
            (steps.indexOf(step) + 1).toString(),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: active ? EltakoStyle.of(context).primaryTextSwitched : EltakoStyle.of(context).primaryBlue,
            ),
          ),
        ),
      ),
    ),
  );
}
