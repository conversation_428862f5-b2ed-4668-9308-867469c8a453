// ignore_for_file: require_trailing_commas

import 'dart:async';

import 'package:eltako_connect/env/environment.dart';
import 'package:eltako_connect/extension/string_extension.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/theme/eltako_padding.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Container for the navigation
class NavigationView extends StatelessWidget {
  // region [PUBLIC PROPERTIES]
  /// Child widget
  final Widget child;

  ///  Leading widget
  // TODO(UI): Remove unused leading and trailing
  final Widget? leading;

  /// Trailing widget
  final Widget? trailing;

  /// Toolbar widget
  final Widget? toolbar;

  /// Bottom navigation bar widget
  final Widget? bottomNavigationBar;

  /// Header widget
  final SliverPersistentHeaderDelegate header;

  /// Scroll controller
  final ScrollController? scrollController;

  /// Is sheet
  final bool isSheet;

  /// Is loading
  final ValueListenable<bool>? loading;

  /// Scrollbar visibility
  final ValueNotifier<bool> _scrollbarVisibility = ValueNotifier(true);

  /// Cancel loading, if loading is true and this is not null, a cancel button will be shown
  final VoidCallback? cancelLoading;

  /// On done
  final VoidCallback? onDone;

  // endregion

  void _scrollBarTimer() {
    if (Environment.current.useDelays) {
      Timer(const Duration(seconds: 3), () {
        _scrollbarVisibility.value = false;
      });
    } else {
      _scrollbarVisibility.value = false;
    }
  }

  // region [PUBLIC FUNCTIONS]

  /// Default constructor
  NavigationView({
    required this.child,
    required this.header,
    this.leading,
    this.trailing,
    super.key,
    this.scrollController,
    this.isSheet = false,
    this.toolbar,
    this.bottomNavigationBar,
    this.loading,
    this.cancelLoading,
    this.onDone,
  }) {
    _scrollBarTimer();
  }

  /// Service not implemented View
  NavigationView.serviceViewNotImplemented({required String name, Widget? child, super.key, this.bottomNavigationBar})
    : loading = null,
      leading = null,
      trailing = null,
      toolbar = null,
      scrollController = null,
      isSheet = false,
      header = NavigationHeader(title: name),
      cancelLoading = null,
      onDone = null,
      // ignore: avoid_string_literals_inside_widget
      child = SliverToBoxAdapter(child: child ?? const Text('Service not implemented yet')) {
    _scrollBarTimer();
  }

  /// Basic sheet, where the navigation buttons only pop
  NavigationView.sheet({
    required this.child,
    required BuildContext context,
    String title = StringExtension.empty,
    bool showCancel = true,
    bool showDone = true,
    this.cancelLoading,
    this.onDone,
    super.key,
  }) : loading = null,
       leading = null,
       trailing = null,
       toolbar = null,
       bottomNavigationBar = null,
       scrollController = ScrollController(),
       isSheet = true,
       header = NavigationHeader(
         isSheet: true,
         title: title,
         leading:
             showCancel
                 ? NavigationItem(text: AppLocalizations.of(context).generalCancel, onTap: () async => context.pop())
                 : null,
         trailing:
             showDone
                 ? NavigationItem(
                   text: AppLocalizations.of(context).generalDone,
                   onTap: () async => {onDone?.call(), context.pop()},
                 )
                 : null,
       ) {
    _scrollBarTimer();
  }

  // region [OVERRIDES]

  @override
  Widget build(BuildContext context) => Background(
    isSheet: isSheet,
    child: Scaffold(
      backgroundColor: Colors.transparent,
      bottomNavigationBar: bottomNavigationBar,
      body: ValueListenableBuilder(
        valueListenable: _scrollbarVisibility,
        builder:
            (context, visible, _) => Scrollbar(
              controller: scrollController,
              trackVisibility: visible,
              thumbVisibility: visible,
              child: CustomScrollView(
                controller: scrollController,
                physics: const AlwaysScrollableScrollPhysics(),
                slivers: [
                  SliverPersistentHeader(
                    delegate: header,
                    // Set this param so that it won't go off the screen when scrolling
                    pinned: true,
                  ),

                  if (toolbar != null)
                    SliverSafeArea(
                      top: false,
                      bottom: false,
                      minimum: const EdgeInsets.only(
                        left: EltakoPadding.s,
                        right: EltakoPadding.s,
                        top: EltakoPadding.l,
                      ),
                      sliver: SliverToBoxAdapter(child: toolbar!),
                    ),

                  if (loading == null)
                    SliverSafeArea(
                      top: false,
                      minimum: const EdgeInsets.only(
                        bottom: EltakoPadding.s,
                        left: EltakoPadding.s,
                        right: EltakoPadding.s,
                        top: EltakoPadding.l,
                      ),
                      sliver: child,
                    ),
                  // ),
                  if (loading != null)
                    ValueListenableBuilder(
                      valueListenable: loading!,
                      builder: (context, loading, child) {
                        if (loading) {
                          return SliverFillRemaining(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const CircularProgressIndicator(),
                                  if (cancelLoading != null) ...{
                                    const SizedBox(height: EltakoPadding.s),
                                    EltakoButton(
                                      label: Text(AppLocalizations.of(context).generalCancel),
                                      onPressed: cancelLoading,
                                    ),
                                  },
                                ],
                              ),
                            ),
                          );
                        }
                        return SliverSafeArea(
                          top: false,
                          minimum: const EdgeInsets.only(
                            bottom: EltakoPadding.s,
                            left: EltakoPadding.s,
                            right: EltakoPadding.s,
                            top: EltakoPadding.l,
                          ),
                          sliver: child!,
                        );
                      },
                      child: child,
                    ),
                ],
              ),
            ),
      ),
    ),
  );
  // endregion
}
