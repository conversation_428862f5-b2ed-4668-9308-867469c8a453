//
//  device_export_row.dart
//  EltakoConnect
//
//  Created by <PERSON> on 03.02.24.
//  Copyright © 2024 Eltako GmbH. All rights reserved.
//

import 'package:eltako_connect/device/i_device.dart';
import 'package:eltako_connect/l10n/app_localizations.dart';
import 'package:eltako_connect/theme/eltako_padding.dart';
import 'package:eltako_connect/widget/widgets.dart';
import 'package:flutter/material.dart';

/// Device Export Row
class DeviceExportRow extends StatelessWidget {
  // region [PUBLIC PROPERTIES]

  /// Device
  final IDevice device;

  /// On export
  final void Function(String)? onExport;

  /// Text controller
  late final TextEditingController _controller;

  // endregion

  // region [PUBLIC FUNCTIONS]
  /// Default constructor
  DeviceExportRow({required this.device, this.onExport, super.key}) : _controller = TextEditingController();

  // endregion

  // region [PRIVATE FUNCTIONS]

  Future<void> _showExportDialog(BuildContext context) async {
    _controller.clear();
    await showEltakoDialog(
      context: context,
      title: AppLocalizations.of(context).export,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(AppLocalizations.of(context).configurationsStepName),
          const SizedBox(height: EltakoPadding.xxs),
          EltakoTextField(
            hintText: AppLocalizations.of(context).nameOfConfiguration,
            showClearButton: true,
            controller: _controller,
          ),
        ],
      ),
      actions: [
        EltakoButton(
          onPressed: () => Navigator.of(context).pop(),
          label: Text(AppLocalizations.of(context).generalCancel),
        ),
        EltakoButton(
          eltakoButtonStyle: EltakoButtonStyle.filled,
          onPressed: () {
            if (_controller.text.isEmpty) {
              EltakoSnackBar(
                title: AppLocalizations.of(context).settingsNameFailEmpty,
                state: EltakoSnackBarState.error,
              ).show(context);
              return;
            }
            Navigator.of(context).pop();
            onExport?.call(_controller.text);
          },
          label: Text(AppLocalizations.of(context).export),
        ),
      ],
    );
  }

  // endregion

  // region [OVERRIDES]

  @override
  Widget build(BuildContext context) => EltakoListTile(
    leading: Transform.rotate(angle: 0.2, child: Image.asset(device.deviceInfo.deviceType.imagePath)),
    titleWidget: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: EltakoPadding.xxs),
        Text(device.deviceInfo.model, style: Theme.of(context).textTheme.bodySmall),
        Text(
          device.deviceInfo.name.trim().isEmpty ? device.deviceInfo.deviceType.name : device.deviceInfo.name,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        Text(device.deviceInfo.deviceType.localizedDescription, style: Theme.of(context).textTheme.labelSmall),
        const SizedBox(height: EltakoPadding.xxs),
      ],
    ),
    trailing:
        onExport != null
            ? EltakoButton(
              size: EltakoButtonSize.small,
              label: Text(AppLocalizations.of(context).generalSave),
              eltakoButtonStyle: EltakoButtonStyle.filled,
              onPressed: () async => _showExportDialog(context),
            )
            : null,
  );
  // endregion
}
