#!/usr/bin/env python3
# report_ms_teams.py
#
# Updated by <PERSON> on 2025-03-28
# Copyright (c) 2025 Eltako. All rights reserved.
#
"""
This script posts a markdown file to a webhook URL.
It takes three command-line arguments:
    --webhook_url: The webhook URL.
    --report_path: The path to the markdown report file.
    --title: The title to add to the top of the report body.

Example usage:
    ```bash
    python report_ms_teams.py --webhook_url "YOUR_WEBHOOK_URL" \
        --report_path "path/to/report.md" --title "Project XYZ"
    ```
"""
import requests
import json
import argparse
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(
    level=logging.DEBUG, format="%(asctime)s - %(levelname)s - %(message)s"
)


def parse_args():
    """
    Parse command-line arguments.

    Returns:
        args: A namespace containing the parsed arguments.
    """
    parser = argparse.ArgumentParser(
        description="Post a markdown file to a webhook URL."
    )
    parser.add_argument("--webhook_url", required=True, help="Webhook URL.")
    parser.add_argument(
        "--report_path", required=True, help="Path to markdown report file."
    )
    parser.add_argument(
        "--title", required=True, help="The title to add to the report."
    )
    return parser.parse_args()


def send_message(webhook_url, message_text):
    """
    Send a message to the webhook URL.

    Args:
        webhook_url (str): The webhook URL.
        message_text (str): The message text.

    Returns:
        bool: True if the message was sent successfully, False otherwise.
    """
    message = {"text": message_text}

    try:
        response = requests.post(
            webhook_url,
            data=json.dumps(message),
            headers={"Content-Type": "application/json"},
        )
        response.raise_for_status()
        logging.info("Message sent successfully")
        return True
    except requests.exceptions.RequestException as e:
        logging.error(f"Failed to send message: {e}")
        return False


def main():
    args = parse_args()
    report_path = Path(args.report_path)

    if not report_path.is_file():
        logging.error(f"The file {report_path} does not exist.")
        md_content = f"\n**Error:** Report file `{report_path}` not found."
    else:
        md_content = report_path.read_text(encoding="utf-8")
        logging.debug(f"Markdown content: {md_content}")

    header = f"# {args.title}\n"
    full_message = header + md_content
    success = send_message(args.webhook_url, full_message)
    return 0 if success else 1


if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
