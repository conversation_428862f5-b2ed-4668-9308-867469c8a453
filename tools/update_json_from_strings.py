import json
import re
import argparse
import os


def update_json_from_strings(strings_file_path, json_file_path):
    # Load the .strings file
    with open(strings_file_path, 'r', encoding='utf-8') as f:
        strings_content = f.read()

    # Parse the .strings file content and maintain order
    strings_list = []
    for match in re.finditer(r'"([^"]+)"\s*=\s*"([^"]+)";', strings_content):
        key, value = match.groups()
        if not key.startswith("NS"):
            strings_list.append((key, value))

    # Organize strings by version and type (header or text)
    strings_by_version = {}
    for key, value in strings_list:
        version_match = re.search(r'_(\d+_\d+_\d+)$', key)
        if version_match:
            version = version_match.group(1).replace('_', '.')
            if version not in strings_by_version:
                strings_by_version[version] = {"headers": [], "texts": [], "release_header": ""}
            if key.startswith("release_header"):
                strings_by_version[version]["release_header"] = value
            elif "header" in key:
                strings_by_version[version]["headers"].append((key, value))
            elif "text" in key:
                strings_by_version[version]["texts"].append((key, value))

    # Load the JSON file
    with open(json_file_path, 'r', encoding='utf-8') as f:
        json_data = json.load(f)

    # Update the JSON file with the values from the .strings file
    for release in json_data:
        release_name = release.get("release_name", "")

        if release_name in strings_by_version:
            version_strings = strings_by_version[release_name]
            release_notes = release.get("release_notes", [])

            # Set the release header
            release["release_header"] = version_strings["release_header"]

            # Match headers and texts correctly
            for i, (header, text) in enumerate(zip(version_strings["headers"], version_strings["texts"])):
                if i < len(release_notes):
                    release_notes[i]["title"] = header[1]
                    release_notes[i]["description"] = text[1]
                else:
                    release_notes.append({
                        "icon_uri": "",  # Placeholder or default value if necessary
                        "title": header[1],
                        "description": text[1],
                        "media": []
                    })

    # Save the updated JSON file
    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, indent=4, ensure_ascii=False)


def main():
    parser = argparse.ArgumentParser(description="Update JSON files with values from .strings files.")
    parser.add_argument('files', metavar='FILES', type=str, nargs='+',
                        help='Pairs of .strings and JSON files (e.g., strings1.strings json1.json strings2.strings json2.json)')

    args = parser.parse_args()
    files = args.files

    if len(files) % 2 != 0:
        raise ValueError(
            "Please provide an even number of file paths, each pair consisting of a .strings file and a JSON file.")

    # Process each pair of files
    for i in range(0, len(files), 2):
        strings_file_path = files[i]
        json_file_path = files[i + 1]

        if not os.path.isfile(strings_file_path):
            print(f"Error: The .strings file '{strings_file_path}' does not exist.")
            continue
        if not os.path.isfile(json_file_path):
            print(f"Error: The JSON file '{json_file_path}' does not exist.")
            continue

        update_json_from_strings(strings_file_path, json_file_path)
        print(f"Updated {json_file_path} with values from {strings_file_path}")


if __name__ == "__main__":
    main()
